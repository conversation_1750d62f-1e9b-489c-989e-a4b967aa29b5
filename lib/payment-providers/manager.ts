import { db } from "@/lib/db";
import { paymentProviderCredentials } from "@/lib/db/schema";
import { PaymentCredentialsVault } from "@/lib/encryption/payment-vault";
import { and, eq, isNull } from "drizzle-orm";

export interface PaymentProviderCredential {
  id: string;
  provider: string;
  credentialType: string;
  value: string;
  keyPrefix: string;
  isActive: boolean;
  lastValidated: Date | null;
  validationStatus: string;
}

export interface RevenueData {
  provider: string;
  amount: number;
  currency: string;
  transactionId: string;
  customerEmail?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

/**
 * Common interface for all payment provider clients
 */
export interface PaymentProviderClient {
  validateCredentials(): Promise<{
    valid: boolean;
    error?: string;
    accountInfo?: any;
  }>;
  verifyWebhook(body: string, signature: string): Promise<any>;
  extractRevenueFromEvent(event: any): Promise<RevenueData | null>;
  createWebhookEndpoint?(webhookUrl: string): Promise<any>;
  updateWebhookEndpoint?(webhookId: string, webhookUrl: string): Promise<any>;
  deleteWebhookEndpoint?(webhookId: string): Promise<void>;
}

/**
 * PaymentProviderManager - Manages user payment provider credentials and clients
 */
export class PaymentProviderManager {
  private static credentialsCache = new Map<
    string,
    { credentials: PaymentProviderCredential[]; timestamp: number }
  >();
  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get user's credentials for a specific payment provider
   */
  static async getUserCredentials(
    userId: string,
    provider: string,
    websiteId?: string
  ): Promise<PaymentProviderCredential[] | null> {
    const cacheKey = `${userId}:${provider}:${websiteId || "global"}`;

    // Check cache first
    const cached = this.credentialsCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.credentials;
    }

    try {
      // Query database - try website-specific first, then fall back to global
      let credentials: any[] = [];

      if (websiteId) {
        // First try website-specific credentials
        credentials = await db
          .select()
          .from(paymentProviderCredentials)
          .where(
            and(
              eq(paymentProviderCredentials.userId, userId),
              eq(paymentProviderCredentials.provider, provider),
              eq(paymentProviderCredentials.isActive, true),
              eq(paymentProviderCredentials.websiteId, websiteId)
            )
          );
      }

      // If no website-specific credentials found, try global credentials
      if (credentials.length === 0) {
        credentials = await db
          .select()
          .from(paymentProviderCredentials)
          .where(
            and(
              eq(paymentProviderCredentials.userId, userId),
              eq(paymentProviderCredentials.provider, provider),
              eq(paymentProviderCredentials.isActive, true),
              isNull(paymentProviderCredentials.websiteId)
            )
          );
      }

      if (credentials.length === 0) return null;

      // Decrypt credentials
      const decryptedCredentials = credentials.map((cred) => ({
        id: cred.id,
        provider: cred.provider,
        credentialType: cred.credentialType,
        value: PaymentCredentialsVault.decrypt(cred.encryptedValue),
        keyPrefix: cred.keyPrefix || "",
        isActive: cred.isActive,
        lastValidated: cred.lastValidated,
        validationStatus: cred.validationStatus || "pending",
      }));

      // Cache the decrypted credentials
      this.credentialsCache.set(cacheKey, {
        credentials: decryptedCredentials,
        timestamp: Date.now(),
      });

      return decryptedCredentials;
    } catch (error) {
      console.error("Failed to get user credentials:", error);
      return null;
    }
  }

  /**
   * Create a payment provider client instance
   * fetch the credentials  from the database and decrypt and create the client
   */
  static async createProviderClient(
    userId: string,
    provider: string,
    websiteId?: string
  ): Promise<PaymentProviderClient> {
    const credentials = await this.getUserCredentials(
      userId,
      provider,
      websiteId
    );
    if (!credentials) {
      throw new Error(`No credentials found for provider ${provider}`);
    }

    switch (provider) {
      case "stripe":
        const { StripeClient } = await import("./stripe-client");
        return new StripeClient(credentials);
      case "paypal":
        const { PayPalClient } = await import("./paypal-client");
        return new PayPalClient(credentials);
      case "lemonsqueezy":
        const { LemonSqueezyClient } = await import("./lemonsqueezy-client");
        return new LemonSqueezyClient(credentials);
      case "polar":
        const { PolarClient } = await import("./polar-client");
        return new PolarClient(credentials);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Store encrypted credentials for a user
   */
  static async storeCredentials(
    userId: string,
    provider: string,
    credentials: { type: string; value: string }[],
    websiteId?: string
  ): Promise<string[]> {
    const storedIds: string[] = [];

    try {
      for (const cred of credentials) {
        const encrypted = PaymentCredentialsVault.encrypt(cred.value);
        const keyPrefix = PaymentCredentialsVault.getCredentialPrefix(
          cred.value
        );

        const [stored] = await db
          .insert(paymentProviderCredentials)
          .values({
            userId,
            websiteId: websiteId || null,
            provider,
            credentialType: cred.type,
            encryptedValue: encrypted,
            keyPrefix,
            validationStatus: "pending",
            isActive: true,
          })
          .returning({ id: paymentProviderCredentials.id });

        storedIds.push(stored.id);
      }

      // Clear cache for this user/provider combination
      const cacheKey = `${userId}:${provider}:${websiteId || "global"}`;
      this.credentialsCache.delete(cacheKey);

      return storedIds;
    } catch (error) {
      console.error("Failed to store credentials:", error);
      throw new Error("Failed to store payment provider credentials");
    }
  }

  /**
   * Update credential validation status
   */
  static async updateCredentialValidation(
    credentialId: string,
    status: "valid" | "invalid" | "pending"
  ): Promise<void> {
    try {
      await db
        .update(paymentProviderCredentials)
        .set({
          validationStatus: status,
          lastValidated: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(paymentProviderCredentials.id, credentialId));

      // Clear relevant cache entries
      this.credentialsCache.clear();
    } catch (error) {
      console.error("Failed to update credential validation:", error);
      throw error;
    }
  }

  /**
   * Delete credentials for a user/provider
   */
  static async deleteCredentials(
    userId: string,
    provider: string,
    websiteId?: string
  ): Promise<void> {
    try {
      await db
        .delete(paymentProviderCredentials)
        .where(
          and(
            eq(paymentProviderCredentials.userId, userId),
            eq(paymentProviderCredentials.provider, provider),
            websiteId
              ? eq(paymentProviderCredentials.websiteId, websiteId)
              : isNull(paymentProviderCredentials.websiteId)
          )
        );

      // Clear cache
      const cacheKey = `${userId}:${provider}:${websiteId || "global"}`;
      this.credentialsCache.delete(cacheKey);
    } catch (error) {
      console.error("Failed to delete credentials:", error);
      throw error;
    }
  }

  /**
   * List all configured providers for a user
   * If websiteId is provided, returns providers for that website + global fallbacks
   * If websiteId is not provided, returns only global providers
   */
  static async getUserProviders(
    userId: string,
    websiteId?: string
  ): Promise<string[]> {
    try {
      const providers = new Set<string>();

      if (websiteId) {
        // Get website-specific providers
        const websiteProviders = await db
          .selectDistinct({ provider: paymentProviderCredentials.provider })
          .from(paymentProviderCredentials)
          .where(
            and(
              eq(paymentProviderCredentials.userId, userId),
              eq(paymentProviderCredentials.isActive, true),
              eq(paymentProviderCredentials.websiteId, websiteId)
            )
          );

        websiteProviders.forEach((p) => providers.add(p.provider));

        // Get global providers (as fallbacks for providers not configured for this website)
        const globalProviders = await db
          .selectDistinct({ provider: paymentProviderCredentials.provider })
          .from(paymentProviderCredentials)
          .where(
            and(
              eq(paymentProviderCredentials.userId, userId),
              eq(paymentProviderCredentials.isActive, true),
              isNull(paymentProviderCredentials.websiteId)
            )
          );

        globalProviders.forEach((p) => providers.add(p.provider));
      } else {
        // Get only global providers
        const globalProviders = await db
          .selectDistinct({ provider: paymentProviderCredentials.provider })
          .from(paymentProviderCredentials)
          .where(
            and(
              eq(paymentProviderCredentials.userId, userId),
              eq(paymentProviderCredentials.isActive, true),
              isNull(paymentProviderCredentials.websiteId)
            )
          );

        globalProviders.forEach((p) => providers.add(p.provider));
      }

      return Array.from(providers);
    } catch (error) {
      console.error("Failed to get user providers:", error);
      return [];
    }
  }

  /**
   * Get detailed provider information for a website
   * Returns which providers are configured at website level vs global level
   */
  static async getWebsiteProviderDetails(
    userId: string,
    websiteId: string
  ): Promise<{
    websiteSpecific: string[];
    globalFallbacks: string[];
    allAvailable: string[];
  }> {
    try {
      // Get website-specific providers
      const websiteProviders = await db
        .selectDistinct({ provider: paymentProviderCredentials.provider })
        .from(paymentProviderCredentials)
        .where(
          and(
            eq(paymentProviderCredentials.userId, userId),
            eq(paymentProviderCredentials.isActive, true),
            eq(paymentProviderCredentials.websiteId, websiteId)
          )
        );

      const websiteSpecific = websiteProviders.map((p) => p.provider);

      // Get global providers
      const globalProviders = await db
        .selectDistinct({ provider: paymentProviderCredentials.provider })
        .from(paymentProviderCredentials)
        .where(
          and(
            eq(paymentProviderCredentials.userId, userId),
            eq(paymentProviderCredentials.isActive, true),
            isNull(paymentProviderCredentials.websiteId)
          )
        );

      const globalFallbacks = globalProviders
        .map((p) => p.provider)
        .filter((provider) => !websiteSpecific.includes(provider));

      const allAvailable = [...websiteSpecific, ...globalFallbacks];

      return {
        websiteSpecific,
        globalFallbacks,
        allAvailable,
      };
    } catch (error) {
      console.error("Failed to get website provider details:", error);
      return {
        websiteSpecific: [],
        globalFallbacks: [],
        allAvailable: [],
      };
    }
  }

  /**
   * Check if a specific provider is configured for a website
   */
  static async isProviderConfigured(
    userId: string,
    provider: string,
    websiteId?: string
  ): Promise<{ configured: boolean; level: "website" | "global" | "none" }> {
    try {
      // Check website-specific first
      if (websiteId) {
        const websiteCredentials = await db
          .select()
          .from(paymentProviderCredentials)
          .where(
            and(
              eq(paymentProviderCredentials.userId, userId),
              eq(paymentProviderCredentials.provider, provider),
              eq(paymentProviderCredentials.isActive, true),
              eq(paymentProviderCredentials.websiteId, websiteId)
            )
          )
          .limit(1);

        if (websiteCredentials.length > 0) {
          return { configured: true, level: "website" };
        }
      }

      // Check global
      const globalCredentials = await db
        .select()
        .from(paymentProviderCredentials)
        .where(
          and(
            eq(paymentProviderCredentials.userId, userId),
            eq(paymentProviderCredentials.provider, provider),
            eq(paymentProviderCredentials.isActive, true),
            isNull(paymentProviderCredentials.websiteId)
          )
        )
        .limit(1);

      if (globalCredentials.length > 0) {
        return { configured: true, level: "global" };
      }

      return { configured: false, level: "none" };
    } catch (error) {
      console.error("Failed to check provider configuration:", error);
      return { configured: false, level: "none" };
    }
  }

  /**
   * Clear credentials cache (useful for testing or manual cache invalidation)
   */
  static clearCache(): void {
    this.credentialsCache.clear();
  }
}
