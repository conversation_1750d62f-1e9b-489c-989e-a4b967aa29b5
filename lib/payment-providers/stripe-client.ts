import Stripe from "stripe";
import type {
  PaymentProviderCredential,
  RevenueData,
  PaymentProviderClient,
} from "./manager";

export interface StripeWebhookEndpoint {
  id: string;
  url: string;
  enabled_events: string[];
  status: string;
  secret: string;
}

/**
 * StripeClient - Handles Stripe-specific operations
 */
export class StripeClient implements PaymentProviderClient {
  private stripe: Stripe;
  private webhookSecret: string;
  private credentials: PaymentProviderCredential[];

  constructor(credentials: PaymentProviderCredential[]) {
    const secretKey = credentials.find(
      (c) => c.credentialType === "secret_key"
    )?.value;
    this.webhookSecret =
      credentials.find((c) => c.credentialType === "webhook_secret")?.value ||
      "";

    if (!secretKey) {
      throw new Error("Stripe secret key not found");
    }

    this.credentials = credentials;
    this.stripe = new Stripe(secretKey, {
      apiVersion: "2025-07-30.basil",
    });
  }

  /**
   * Validate credentials by making a test API call
   */
  async validateCredentials(): Promise<{
    valid: boolean;
    error?: string;
    accountInfo?: any;
  }> {
    try {
      const account = await this.stripe.accounts.retrieve();
      return {
        valid: true,
        accountInfo: {
          id: account.id,
          email: account.email,
          country: account.country,
          currency: account.default_currency,
          business_type: account.business_type,
          charges_enabled: account.charges_enabled,
          payouts_enabled: account.payouts_enabled,
        },
      };
    } catch (error: any) {
      return {
        valid: false,
        error: error.message || "Invalid Stripe credentials",
      };
    }
  }

  /**
   * Create a webhook endpoint for revenue tracking
   */
  async createWebhookEndpoint(
    webhookUrl: string
  ): Promise<StripeWebhookEndpoint> {
    try {
      const endpoint = await this.stripe.webhookEndpoints.create({
        url: webhookUrl,
        enabled_events: [
          "checkout.session.completed",
          "payment_intent.succeeded",
          "invoice.payment_succeeded",
          "subscription_schedule.completed",
        ],
      });

      return {
        id: endpoint.id,
        url: endpoint.url,
        enabled_events: endpoint.enabled_events,
        status: endpoint.status,
        secret: endpoint.secret || "",
      };
    } catch (error: any) {
      console.error("Failed to create Stripe webhook endpoint:", error);
      throw new Error(`Failed to create webhook endpoint: ${error.message}`);
    }
  }

  /**
   * Update an existing webhook endpoint
   */
  async updateWebhookEndpoint(
    webhookId: string,
    webhookUrl: string
  ): Promise<StripeWebhookEndpoint> {
    try {
      const endpoint = await this.stripe.webhookEndpoints.update(webhookId, {
        url: webhookUrl,
        enabled_events: [
          "checkout.session.completed",
          "payment_intent.succeeded",
          "invoice.payment_succeeded",
          "subscription_schedule.completed",
        ],
      });

      return {
        id: endpoint.id,
        url: endpoint.url,
        enabled_events: endpoint.enabled_events,
        status: endpoint.status,
        secret: endpoint.secret || "",
      };
    } catch (error: any) {
      console.error("Failed to update Stripe webhook endpoint:", error);
      throw new Error(`Failed to update webhook endpoint: ${error.message}`);
    }
  }

  /**
   * Delete a webhook endpoint
   */
  async deleteWebhookEndpoint(webhookId: string): Promise<void> {
    try {
      await this.stripe.webhookEndpoints.del(webhookId);
    } catch (error: any) {
      console.error("Failed to delete Stripe webhook endpoint:", error);
      throw new Error(`Failed to delete webhook endpoint: ${error.message}`);
    }
  }

  /**
   * Verify webhook signature and parse event with enhanced security
   */
  async verifyWebhook(body: string, signature: string): Promise<Stripe.Event> {
    if (!this.webhookSecret) {
      throw new Error("Webhook secret not configured");
    }

    // Validate signature format
    if (!signature || typeof signature !== "string") {
      throw new Error("Invalid webhook signature format");
    }

    // Validate body
    if (!body || typeof body !== "string") {
      throw new Error("Invalid webhook body");
    }

    try {
      // Use Stripe's built-in verification which includes:
      // - HMAC-SHA256 signature verification
      // - Timestamp validation (prevents replay attacks)
      // - Timing attack protection
      const event = this.stripe.webhooks.constructEvent(
        body,
        signature,
        this.webhookSecret
      );

      // Additional validation: ensure event has required fields
      if (!event.id || !event.type || !event.created) {
        throw new Error("Invalid webhook event structure");
      }

      // Log successful verification (without sensitive data)
      console.log(`Stripe webhook verified: ${event.type} (${event.id})`);

      return event;
    } catch (error: any) {
      // Enhanced error logging for debugging (without exposing secrets)
      const errorDetails = {
        message: error.message,
        type: error.constructor.name,
        hasSignature: !!signature,
        hasBody: !!body,
        bodyLength: body?.length || 0,
        signaturePrefix: signature?.substring(0, 10) + "...",
      };

      console.error(
        "Stripe webhook signature verification failed:",
        errorDetails
      );

      // Throw generic error to prevent information leakage
      throw new Error("Webhook signature verification failed");
    }
  }

  /**
   * Extract revenue data from Stripe webhook event
   */
  async extractRevenueFromEvent(
    event: Stripe.Event
  ): Promise<RevenueData | null> {
    try {
      switch (event.type) {
        case "checkout.session.completed": {
          const session = event.data.object as Stripe.Checkout.Session;

          // Skip if no payment was required (e.g., free trial)
          if (!session.amount_total || session.amount_total === 0) {
            return null;
          }

          return {
            provider: "stripe",
            amount: session.amount_total / 100, // Convert from cents
            currency: session.currency?.toUpperCase() || "USD",
            transactionId: session.id,
            customerEmail: session.customer_email || undefined,
            metadata: {
              ...session.metadata,
              payment_status: session.payment_status,
              mode: session.mode,
              customer_id: session.customer,
            },
            timestamp: new Date(event.created * 1000),
          };
        }

        case "payment_intent.succeeded": {
          const paymentIntent = event.data.object as Stripe.PaymentIntent;

          return {
            provider: "stripe",
            amount: paymentIntent.amount / 100, // Convert from cents
            currency: paymentIntent.currency.toUpperCase(),
            transactionId: paymentIntent.id,
            customerEmail: paymentIntent.receipt_email || undefined,
            metadata: {
              ...paymentIntent.metadata,
              payment_method: paymentIntent.payment_method,
              customer_id: paymentIntent.customer,
            },
            timestamp: new Date(event.created * 1000),
          };
        }

        case "invoice.payment_succeeded": {
          const invoice = event.data.object as Stripe.Invoice;

          return {
            provider: "stripe",
            amount: invoice.amount_paid / 100, // Convert from cents
            currency: invoice.currency.toUpperCase(),
            transactionId: invoice.id || "",
            customerEmail: invoice.customer_email || undefined,
            metadata: {
              ...invoice.metadata,
              customer_id:
                typeof invoice.customer === "string"
                  ? invoice.customer
                  : invoice.customer?.id,
              billing_reason: invoice.billing_reason,
            },
            timestamp: new Date(event.created * 1000),
          };
        }

        default:
          console.log(`Unhandled Stripe event type: ${event.type}`);
          return null;
      }
    } catch (error) {
      console.error("Failed to extract revenue from Stripe event:", error);
      return null;
    }
  }

  /**
   * Get webhook endpoint details
   */
  async getWebhookEndpoint(
    webhookId: string
  ): Promise<StripeWebhookEndpoint | null> {
    try {
      const endpoint = await this.stripe.webhookEndpoints.retrieve(webhookId);

      return {
        id: endpoint.id,
        url: endpoint.url,
        enabled_events: endpoint.enabled_events,
        status: endpoint.status,
        secret: endpoint.secret || "",
      };
    } catch (error: any) {
      console.error("Failed to get Stripe webhook endpoint:", error);
      return null;
    }
  }

  /**
   * List all webhook endpoints for this account
   */
  async listWebhookEndpoints(): Promise<StripeWebhookEndpoint[]> {
    try {
      const endpoints = await this.stripe.webhookEndpoints.list();

      return endpoints.data.map((endpoint) => ({
        id: endpoint.id,
        url: endpoint.url,
        enabled_events: endpoint.enabled_events,
        status: endpoint.status,
        secret: endpoint.secret || "",
      }));
    } catch (error: any) {
      console.error("Failed to list Stripe webhook endpoints:", error);
      return [];
    }
  }
}
