import { db } from "@/lib/db";
import { webhookEndpoints, paymentProviderCredentials } from "@/lib/db/schema";
import { PaymentProviderManager } from "./manager";
import { PaymentCredentialsVault } from "@/lib/encryption/payment-vault";
import { and, eq, isNull } from "drizzle-orm";

export interface WebhookEndpointConfig {
  id: string;
  userId: string;
  websiteId?: string;
  provider: string;
  webhookId: string;
  webhookUrl: string;
  events: string[];
  isActive: boolean;
  lastTriggered?: Date;
}

/**
 * WebhookManager - Manages webhook endpoints for payment providers
 */
export class WebhookManager {
  /**
   * Generate webhook URL for a user, provider, and website
   * Format: /api/webhooks/[provider]/[userId]/[websiteId]
   * If websiteId is not provided, uses 'global' for user-level credentials
   */
  static generateWebhookUrl(
    userId: string,
    provider: string,
    websiteId?: string
  ): string {
    const baseUrl =
      process.env.NODE_ENV === "production"
        ? process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
        : process.env.NEXT_PUBLIC_APP_URL_LOCAL || "http://localhost:3000";
    const websiteSegment = websiteId || "global";
    return `${baseUrl}/api/webhooks/${provider}/${userId}/${websiteSegment}`;
  }

  /**
   * Setup webhook endpoints for a user's payment provider
   */
  static async setupWebhookEndpoints(
    userId: string,
    provider: string,
    websiteId?: string
  ): Promise<{ webhookUrl: string; webhookId: string; secret?: string }> {
    const webhookUrl = this.generateWebhookUrl(userId, provider, websiteId);

    try {
      // Get the payment provider key from the database and then decrypt to create the payment provider client
      const client = await PaymentProviderManager.createProviderClient(
        userId,
        provider,
        websiteId
      );

      let webhookResult;

      switch (provider) {
        case "stripe":
          webhookResult = await this.setupStripeWebhook(client, webhookUrl);
          break;
        case "lemonsqueezy":
          webhookResult = await this.setupLemonSqueezyWebhook(
            client,
            webhookUrl
          );
          break;
        case "polar":
          webhookResult = await this.setupPolarWebhook(client, webhookUrl);
          break;
        default:
          // For providers that don't support automatic webhook setup
          webhookResult = {
            webhookId: "manual",
            webhookUrl,
            secret: undefined,
          };
      }

      // Store webhook endpoint in database
      await this.storeWebhookEndpoint({
        userId,
        websiteId,
        provider,
        webhookId: webhookResult.webhookId,
        webhookUrl: webhookResult.webhookUrl,
        events: this.getDefaultEventsForProvider(provider),
        isActive: true,
      });

      // Store webhook secret as a credential if provided (for Stripe)
      if (webhookResult.secret && provider === "stripe") {
        await this.storeWebhookSecret(
          userId,
          provider,
          webhookResult.secret,
          websiteId
        );
      }

      return webhookResult;
    } catch (error) {
      console.error(`Failed to setup webhook for ${provider}:`, error);
      throw error;
    }
  }

  /**
   * Store webhook secret as a credential
   */
  private static async storeWebhookSecret(
    userId: string,
    provider: string,
    secret: string,
    websiteId?: string
  ): Promise<void> {
    try {
      // First, delete any existing webhook_secret for this user/provider/website combination
      await db
        .delete(paymentProviderCredentials)
        .where(
          and(
            eq(paymentProviderCredentials.userId, userId),
            eq(paymentProviderCredentials.provider, provider),
            eq(paymentProviderCredentials.credentialType, "webhook_secret"),
            websiteId
              ? eq(paymentProviderCredentials.websiteId, websiteId)
              : isNull(paymentProviderCredentials.websiteId)
          )
        );

      // Store the new webhook secret
      const encrypted = PaymentCredentialsVault.encrypt(secret);
      const keyPrefix = PaymentCredentialsVault.getCredentialPrefix(secret);

      await db.insert(paymentProviderCredentials).values({
        userId,
        websiteId: websiteId || null,
        provider,
        credentialType: "webhook_secret",
        encryptedValue: encrypted,
        keyPrefix,
        validationStatus: "valid", // Webhook secrets from API are always valid
        isActive: true,
      });

      console.log(
        `Stored webhook secret for ${provider} (${websiteId || "global"})`
      );
    } catch (error) {
      console.error(`Failed to store webhook secret for ${provider}:`, error);
      throw error;
    }
  }

  /**
   * Setup Stripe webhook endpoint
   */
  private static async setupStripeWebhook(
    stripeClient: any,
    webhookUrl: string
  ): Promise<{ webhookId: string; webhookUrl: string; secret: string }> {
    try {
      const endpoint = await stripeClient.createWebhookEndpoint(webhookUrl);

      return {
        webhookId: endpoint.id,
        webhookUrl: endpoint.url,
        secret: endpoint.secret,
      };
    } catch (error: any) {
      throw new Error(`Failed to setup Stripe webhook: ${error.message}`);
    }
  }

  /**
   * Setup LemonSqueezy webhook endpoint
   */
  private static async setupLemonSqueezyWebhook(
    lemonSqueezyClient: any,
    webhookUrl: string
  ): Promise<{ webhookId: string; webhookUrl: string; secret?: string }> {
    try {
      const endpoint =
        await lemonSqueezyClient.createWebhookEndpoint(webhookUrl);

      return {
        webhookId: endpoint.id,
        webhookUrl: endpoint.url,
        secret: endpoint.secret,
      };
    } catch (error: any) {
      throw new Error(`Failed to setup LemonSqueezy webhook: ${error.message}`);
    }
  }

  /**
   * Setup Polar webhook endpoint
   */
  private static async setupPolarWebhook(
    polarClient: any,
    webhookUrl: string
  ): Promise<{ webhookId: string; webhookUrl: string; secret?: string }> {
    try {
      const endpoint = await polarClient.createWebhookEndpoint(webhookUrl);

      return {
        webhookId: endpoint.id,
        webhookUrl: endpoint.url,
        secret: endpoint.secret,
      };
    } catch (error: any) {
      throw new Error(`Failed to setup Polar webhook: ${error.message}`);
    }
  }

  /**
   * Store webhook endpoint configuration in database
   */
  private static async storeWebhookEndpoint(config: {
    userId: string;
    websiteId?: string;
    provider: string;
    webhookId: string;
    webhookUrl: string;
    events: string[];
    isActive: boolean;
  }): Promise<string> {
    try {
      const [stored] = await db
        .insert(webhookEndpoints)
        .values({
          userId: config.userId,
          websiteId: config.websiteId || null,
          provider: config.provider,
          webhookId: config.webhookId,
          webhookUrl: config.webhookUrl,
          events: JSON.stringify(config.events),
          isActive: config.isActive,
        })
        .returning({ id: webhookEndpoints.id });

      return stored.id;
    } catch (error) {
      console.error("Failed to store webhook endpoint:", error);
      throw error;
    }
  }

  /**
   * Update webhook endpoint when credentials change
   */
  static async updateWebhookEndpoint(
    userId: string,
    provider: string,
    websiteId?: string
  ): Promise<void> {
    try {
      // Get existing webhook endpoint
      const existing = await this.getWebhookEndpoint(
        userId,
        provider,
        websiteId
      );
      if (!existing) {
        // No existing endpoint, create new one
        await this.setupWebhookEndpoints(userId, provider, websiteId);
        return;
      }

      // For all providers, recreate the webhook to ensure fresh secrets
      // This ensures webhook secret rotation when credentials change
      await this.deleteWebhookEndpoint(userId, provider, websiteId);
      await this.setupWebhookEndpoints(userId, provider, websiteId);
    } catch (error) {
      console.error(
        `Failed to update webhook endpoint for ${provider}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Delete webhook endpoint
   */
  static async deleteWebhookEndpoint(
    userId: string,
    provider: string,
    websiteId?: string
  ): Promise<void> {
    try {
      // Get existing webhook endpoint
      const existing = await this.getWebhookEndpoint(
        userId,
        provider,
        websiteId
      );
      if (!existing) return;

      // Delete from payment provider if not manual
      if (existing.webhookId !== "manual") {
        try {
          const client = await PaymentProviderManager.createProviderClient(
            userId,
            provider,
            websiteId
          );

          switch (provider) {
            case "stripe":
              if (client.deleteWebhookEndpoint) {
                await client.deleteWebhookEndpoint(existing.webhookId);
              }
              break;
            // Add other providers as needed
          }
        } catch (error) {
          console.warn(
            `Failed to delete webhook from ${provider}, continuing with database cleanup:`,
            error
          );
        }
      }

      // Delete webhook endpoint from database
      await db
        .delete(webhookEndpoints)
        .where(
          and(
            eq(webhookEndpoints.userId, userId),
            eq(webhookEndpoints.provider, provider),
            websiteId
              ? eq(webhookEndpoints.websiteId, websiteId)
              : isNull(webhookEndpoints.websiteId)
          )
        );

      // Delete webhook secret credential if it exists (for Stripe)
      if (provider === "stripe") {
        await db
          .delete(paymentProviderCredentials)
          .where(
            and(
              eq(paymentProviderCredentials.userId, userId),
              eq(paymentProviderCredentials.provider, provider),
              eq(paymentProviderCredentials.credentialType, "webhook_secret"),
              websiteId
                ? eq(paymentProviderCredentials.websiteId, websiteId)
                : isNull(paymentProviderCredentials.websiteId)
            )
          );
      }
    } catch (error) {
      console.error(
        `Failed to delete webhook endpoint for ${provider}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get webhook endpoint configuration
   */
  static async getWebhookEndpoint(
    userId: string,
    provider: string,
    websiteId?: string
  ): Promise<WebhookEndpointConfig | null> {
    try {
      const [endpoint] = await db
        .select()
        .from(webhookEndpoints)
        .where(
          and(
            eq(webhookEndpoints.userId, userId),
            eq(webhookEndpoints.provider, provider),
            websiteId
              ? eq(webhookEndpoints.websiteId, websiteId)
              : isNull(webhookEndpoints.websiteId)
          )
        )
        .limit(1);

      if (!endpoint) return null;

      return {
        id: endpoint.id,
        userId: endpoint.userId,
        websiteId: endpoint.websiteId || undefined,
        provider: endpoint.provider,
        webhookId: endpoint.webhookId,
        webhookUrl: endpoint.webhookUrl,
        events: JSON.parse(endpoint.events),
        isActive: endpoint.isActive,
        lastTriggered: endpoint.lastTriggered || undefined,
      };
    } catch (error) {
      console.error("Failed to get webhook endpoint:", error);
      return null;
    }
  }

  /**
   * Get default events for a payment provider
   */
  private static getDefaultEventsForProvider(provider: string): string[] {
    const eventMap = {
      stripe: [
        "checkout.session.completed",
        "payment_intent.succeeded",
        "invoice.payment_succeeded",
      ],
      lemonsqueezy: [
        "order_created",
        "subscription_created",
        "subscription_updated",
      ],
      polar: [
        "checkout.created",
        "subscription.created",
        "subscription.updated",
      ],
    };

    return eventMap[provider as keyof typeof eventMap] || [];
  }

  /**
   * Update webhook last triggered timestamp
   */
  static async updateWebhookLastTriggered(
    userId: string,
    provider: string,
    websiteId?: string
  ): Promise<void> {
    try {
      await db
        .update(webhookEndpoints)
        .set({
          lastTriggered: new Date(),
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(webhookEndpoints.userId, userId),
            eq(webhookEndpoints.provider, provider),
            websiteId
              ? eq(webhookEndpoints.websiteId, websiteId)
              : isNull(webhookEndpoints.websiteId)
          )
        );
    } catch (error) {
      console.error("Failed to update webhook last triggered:", error);
    }
  }
}
