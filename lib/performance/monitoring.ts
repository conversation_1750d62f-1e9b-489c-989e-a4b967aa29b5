// Performance monitoring utilities
import React from "react";

export interface PerformanceMetrics {
  pageLoadTime: number;
  ttfb: number; // Time to First Byte
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  memory?: number;
}

// Web Vitals measurement
export function measureWebVitals(): Promise<PerformanceMetrics> {
  return new Promise((resolve) => {
    const metrics: Partial<PerformanceMetrics> = {};

    // Page Load Time
    if (typeof window !== "undefined" && window.performance) {
      const navigation = performance.getEntriesByType(
        "navigation"
      )[0] as PerformanceNavigationTiming;
      metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
      metrics.ttfb = navigation.responseStart - navigation.fetchStart;
    }

    // Use Web Vitals API if available
    if (typeof window !== "undefined") {
      // First Contentful Paint
      const paintEntries = performance.getEntriesByType("paint");
      const fcpEntry = paintEntries.find(
        (entry) => entry.name === "first-contentful-paint"
      );
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime;
      }

      // Largest Contentful Paint
      if ("PerformanceObserver" in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1] as any;
            metrics.lcp = lastEntry.startTime;
          });
          lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            for (const entry of entries as any[]) {
              metrics.fid =
                (entry as any).processingStart - (entry as any).startTime;
            }
          });
          fidObserver.observe({ entryTypes: ["first-input"] });

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            const entries = list.getEntries();
            for (const entry of entries as any[]) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value;
              }
            }
            metrics.cls = clsValue;
          });
          clsObserver.observe({ entryTypes: ["layout-shift"] });

          // Memory usage (if available)
          if ("memory" in performance) {
            metrics.memory = (performance as any).memory.usedJSHeapSize;
          }

          // Resolve after a short delay to collect metrics
          setTimeout(() => {
            resolve(metrics as PerformanceMetrics);
          }, 3000);
        } catch (error) {
          console.warn("Performance monitoring not supported:", error);
          resolve(metrics as PerformanceMetrics);
        }
      } else {
        resolve(metrics as PerformanceMetrics);
      }
    } else {
      resolve(metrics as PerformanceMetrics);
    }
  });
}

// Database query performance monitoring
export class QueryPerformanceMonitor {
  private static instance: QueryPerformanceMonitor;
  private metrics: Map<
    string,
    { count: number; totalTime: number; avgTime: number }
  > = new Map();

  static getInstance(): QueryPerformanceMonitor {
    if (!QueryPerformanceMonitor.instance) {
      QueryPerformanceMonitor.instance = new QueryPerformanceMonitor();
    }
    return QueryPerformanceMonitor.instance;
  }

  startTimer(queryName: string): () => void {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;

      this.recordMetric(queryName, duration);
    };
  }

  private recordMetric(queryName: string, duration: number): void {
    const existing = this.metrics.get(queryName) || {
      count: 0,
      totalTime: 0,
      avgTime: 0,
    };

    existing.count++;
    existing.totalTime += duration;
    existing.avgTime = existing.totalTime / existing.count;

    this.metrics.set(queryName, existing);

    // Log slow queries
    if (duration > 1000) {
      // Slower than 1 second
      console.warn(
        `Slow query detected: ${queryName} took ${duration.toFixed(2)}ms`
      );
    }
  }

  getMetrics(): Record<
    string,
    { count: number; totalTime: number; avgTime: number }
  > {
    return Object.fromEntries(this.metrics);
  }

  reset(): void {
    this.metrics.clear();
  }
}

// Component performance monitoring HOC
export function withPerformanceMonitoring<T extends object>(
  Component: React.ComponentType<T>,
  componentName: string
) {
  return function PerformanceMonitoredComponent(props: T) {
    const startTime = performance.now();

    React.useEffect(() => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      if (renderTime > 100) {
        // Slower than 100ms
        console.warn(
          `Slow component render: ${componentName} took ${renderTime.toFixed(2)}ms`
        );
      }
    });

    return React.createElement(Component, props);
  };
}

// API endpoint performance monitoring
export function trackAPIPerformance(endpoint: string, startTime: number): void {
  const endTime = Date.now();
  const duration = endTime - startTime;

  // Log slow API calls
  if (duration > 2000) {
    // Slower than 2 seconds
    console.warn(`Slow API call: ${endpoint} took ${duration}ms`);
  }

  // In production, you might want to send this to an analytics service
  if (process.env.NODE_ENV === "production") {
    // Send to monitoring service
    // Example: analytics.track('api_performance', { endpoint, duration });
  }
}

// Memory usage monitoring
export function monitorMemoryUsage():
  | { used: number; total: number; limit: number; percentage: number }
  | undefined {
  if (typeof window !== "undefined" && "memory" in performance) {
    const memory = (performance as any).memory;
    const usedPercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;

    if (usedPercent > 80) {
      console.warn(`High memory usage: ${usedPercent.toFixed(1)}%`);
    }

    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      limit: memory.jsHeapSizeLimit,
      percentage: usedPercent,
    };
  }

  return undefined;
}

// Resource loading performance
export function trackResourcePerformance(): void {
  if (typeof window !== "undefined" && window.performance) {
    const resources = performance.getEntriesByType(
      "resource"
    ) as PerformanceResourceTiming[];

    for (const resource of resources) {
      const loadTime = resource.responseEnd - resource.startTime;
      if (loadTime > 3000) {
        console.warn(
          `Slow resource load: ${resource.name} took ${loadTime.toFixed(2)}ms`
        );
      }
    }
  }
}
