// Date utility functions for analytics

export function getDateRange(preset: string): { from: Date; to: Date } {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  switch (preset) {
    case "today":
      return {
        from: today,
        to: now,
      };

    case "yesterday": {
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return {
        from: yesterday,
        to: today,
      };
    }

    case "last7days": {
      const last7Days = new Date(today);
      last7Days.setDate(last7Days.getDate() - 7);
      return {
        from: last7Days,
        to: now,
      };
    }

    case "last30days": {
      const last30Days = new Date(today);
      last30Days.setDate(last30Days.getDate() - 30);
      return {
        from: last30Days,
        to: now,
      };
    }

    case "last90days": {
      const last90Days = new Date(today);
      last90Days.setDate(last90Days.getDate() - 90);
      return {
        from: last90Days,
        to: now,
      };
    }

    case "thisMonth": {
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      return {
        from: thisMonthStart,
        to: now,
      };
    }

    case "lastMonth": {
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
      return {
        from: lastMonthStart,
        to: lastMonthEnd,
      };
    }

    case "thisYear": {
      const thisYearStart = new Date(now.getFullYear(), 0, 1);
      return {
        from: thisYearStart,
        to: now,
      };
    }

    case "lastYear": {
      const lastYearStart = new Date(now.getFullYear() - 1, 0, 1);
      const lastYearEnd = new Date(now.getFullYear() - 1, 11, 31);
      return {
        from: lastYearStart,
        to: lastYearEnd,
      };
    }

    default:
      // Default to last 30 days
      return getDateRange("last30days");
  }
}

export function formatDateForDisplay(date: Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(date);
}

export function formatDateTimeForDisplay(date: Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
}

export function getRelativeTimeString(date: Date): string {
  const now = Date.now();
  const then = date.getTime();
  const diffInSeconds = Math.floor((now - then) / 1000);

  // Clamp future timestamps to "just now"
  if (diffInSeconds <= 0) return "just now";

  if (diffInSeconds < 60) {
    return `${diffInSeconds}s ago`;
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks}w ago`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths}mo ago`;
  }

  const diffInYears = Math.floor(diffInDays / 365);
  return `${diffInYears}y ago`;
}

export function isValidDateRange(from: Date, to: Date): boolean {
  return from < to && from <= new Date() && to <= new Date();
}

export function getOptimalInterval(
  from: Date,
  to: Date
): "hour" | "day" | "week" | "month" {
  const diffInDays = Math.floor(
    (to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (diffInDays <= 1) {
    return "hour";
  }
  if (diffInDays <= 31) {
    return "day";
  }
  if (diffInDays <= 90) {
    return "week";
  }
  return "month";
}

export function formatChartDate(
  date: string,
  interval: "hour" | "day" | "week" | "month"
): string {
  const d = new Date(date);

  switch (interval) {
    case "hour":
      return new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "numeric",
        hour: "numeric",
      }).format(d);

    case "day":
      return new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "numeric",
      }).format(d);

    case "week":
      return `Week ${date}`;

    case "month":
      return new Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "short",
      }).format(d);

    default:
      return date;
  }
}
