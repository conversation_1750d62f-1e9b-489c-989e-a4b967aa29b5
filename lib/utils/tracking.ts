// Utility functions for tracking script generation and validation

export function generateTrackingScript(
  trackingId: string,
  domain: string,
  options: {
    auto?: boolean;
    debug?: boolean;
    customEndpoint?: string;
  } = {}
) {
  const { auto = true, debug = false, customEndpoint } = options;
  const appUrl =
    process.env.NODE_ENV === "production"
      ? process.env.NEXT_PUBLIC_APP_URL
      : "http://localhost:3000";
  const endpoint = customEndpoint || `${appUrl}/api/events`;

  return `
<!-- InstaSight Analytics -->
<script>
  (function() {
    var script = document.createElement('script');
    script.src = '${appUrl}/api/script/${trackingId}';
    script.async = true;
    script.defer = true;
    script.setAttribute('data-website-id', '${trackingId}');
    script.setAttribute('data-domain', '${domain}');
    script.setAttribute('data-auto', '${auto}');
    script.setAttribute('data-debug', '${debug}');
    script.setAttribute('data-endpoint', '${endpoint}');
    document.head.appendChild(script);
  })();
</script>
<!-- End DataFast Analytics -->
`.trim();
}

export function generateMinimalScript(trackingId: string, domain: string) {
  const appUrl =
    process.env.NODE_ENV === "production"
      ? process.env.NEXT_PUBLIC_APP_URL
      : "http://localhost:3000";
  return `<script src="${appUrl}/api/script/${trackingId}" data-website-id="${trackingId}" data-domain="${domain}" defer></script>`;
}

export function validateTrackingData(data: any): string[] {
  const errors: string[] = [];

  // Required fields
  if (!data.websiteId) errors.push("Website ID is required");
  if (!data.visitorId) errors.push("Visitor ID is required");
  if (!data.sessionId) errors.push("Session ID is required");
  if (!data.url) errors.push("URL is required");

  // URL validation
  if (data.url && typeof data.url === "string") {
    try {
      new URL(data.url);
    } catch {
      errors.push("Invalid URL format");
    }
  }

  // Event type validation
  const validEventTypes = [
    "pageview",
    "custom",
    "payment",
    "signup",
    "conversion",
  ];
  if (data.eventType && !validEventTypes.includes(data.eventType)) {
    errors.push("Invalid event type");
  }

  // Revenue validation
  if (data.revenue !== undefined && data.revenue !== null) {
    const revenue = Number.parseFloat(data.revenue);
    if (Number.isNaN(revenue) || revenue < 0) {
      errors.push("Invalid revenue value");
    }
  }

  // Custom data validation
  if (data.customData && typeof data.customData === "object") {
    const customDataString = JSON.stringify(data.customData);
    if (customDataString.length > 5000) {
      errors.push("Custom data too large (max 5KB)");
    }
  }

  return errors;
}

export function sanitizeEventData(data: any) {
  const sanitized: any = {};

  // Copy safe fields
  const safeFields = [
    "websiteId",
    "visitorId",
    "sessionId",
    "eventType",
    "eventName",
    "url",
    "referrer",
    "userAgent",
    "country",
    "region",
    "city",
    "device",
    "browser",
    "os",
    "utm_source",
    "utm_medium",
    "utm_campaign",
    "utm_content",
    "utm_term",
    "revenue",
    "timestamp",
  ];

  for (const field of safeFields) {
    if (data[field] !== undefined) {
      sanitized[field] = data[field];
    }
  }

  // Sanitize custom data
  if (data.customData && typeof data.customData === "object") {
    sanitized.customData = {};
    for (const [key, value] of Object.entries(data.customData)) {
      if (typeof key === "string" && key.length <= 50) {
        const sanitizedValue = String(value).slice(0, 500);
        sanitized.customData[key] = sanitizedValue;
      }
    }
  }

  return sanitized;
}

export function isBot(userAgent: string): boolean {
  const botPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /lighthouse/i,
    /gtmetrix/i,
    /pingdom/i,
    /prerender/i,
    /headless/i,
    /phantom/i,
    /selenium/i,
    /puppeteer/i,
    /playwright/i,
  ];

  return botPatterns.some((pattern) => pattern.test(userAgent));
}

export function parseUserAgent(userAgent: string) {
  // Simple user agent parsing - you might want to use a library like ua-parser-js
  const device = /Mobile|Android|iPhone|iPad/.test(userAgent)
    ? /iPad/.test(userAgent)
      ? "tablet"
      : "mobile"
    : "desktop";

  let browser = "Other";
  if (/Chrome/.test(userAgent)) browser = "Chrome";
  else if (/Firefox/.test(userAgent)) browser = "Firefox";
  else if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent))
    browser = "Safari";
  else if (/Edge/.test(userAgent)) browser = "Edge";

  let os = "Other";
  if (/Windows/.test(userAgent)) os = "Windows";
  else if (/Mac/.test(userAgent)) os = "macOS";
  else if (/Linux/.test(userAgent)) os = "Linux";
  else if (/Android/.test(userAgent)) os = "Android";
  else if (/iOS/.test(userAgent)) os = "iOS";

  return { device, browser, os };
}
