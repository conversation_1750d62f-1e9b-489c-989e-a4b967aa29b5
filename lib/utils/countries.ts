/**
 * Country utilities for geographic data visualization
 * Provides country code mapping, flag emojis, and country names
 */

import type { CountryInfo } from "@/lib/types/geographic";

// Country code to country info mapping
export const COUNTRIES: Record<string, CountryInfo> = {
  // Major countries
  US: { code: "US", name: "United States", flag: "🇺🇸" },
  GB: { code: "GB", name: "United Kingdom", flag: "🇬🇧" },
  DE: { code: "DE", name: "Germany", flag: "🇩🇪" },
  FR: { code: "FR", name: "France", flag: "🇫🇷" },
  CA: { code: "CA", name: "Canada", flag: "🇨🇦" },
  AU: { code: "AU", name: "Australia", flag: "🇦🇺" },
  JP: { code: "JP", name: "Japan", flag: "🇯🇵" },
  CN: { code: "CN", name: "China", flag: "🇨🇳" },
  IN: { code: "IN", name: "India", flag: "🇮🇳" },
  BR: { code: "BR", name: "Brazil", flag: "🇧🇷" },
  
  // European countries
  NL: { code: "NL", name: "Netherlands", flag: "🇳🇱" },
  ES: { code: "ES", name: "Spain", flag: "🇪🇸" },
  IT: { code: "IT", name: "Italy", flag: "🇮🇹" },
  SE: { code: "SE", name: "Sweden", flag: "🇸🇪" },
  NO: { code: "NO", name: "Norway", flag: "🇳🇴" },
  DK: { code: "DK", name: "Denmark", flag: "🇩🇰" },
  FI: { code: "FI", name: "Finland", flag: "🇫🇮" },
  CH: { code: "CH", name: "Switzerland", flag: "🇨🇭" },
  AT: { code: "AT", name: "Austria", flag: "🇦🇹" },
  BE: { code: "BE", name: "Belgium", flag: "🇧🇪" },
  IE: { code: "IE", name: "Ireland", flag: "🇮🇪" },
  PT: { code: "PT", name: "Portugal", flag: "🇵🇹" },
  PL: { code: "PL", name: "Poland", flag: "🇵🇱" },
  CZ: { code: "CZ", name: "Czech Republic", flag: "🇨🇿" },
  HU: { code: "HU", name: "Hungary", flag: "🇭🇺" },
  RO: { code: "RO", name: "Romania", flag: "🇷🇴" },
  BG: { code: "BG", name: "Bulgaria", flag: "🇧🇬" },
  HR: { code: "HR", name: "Croatia", flag: "🇭🇷" },
  SI: { code: "SI", name: "Slovenia", flag: "🇸🇮" },
  SK: { code: "SK", name: "Slovakia", flag: "🇸🇰" },
  LT: { code: "LT", name: "Lithuania", flag: "🇱🇹" },
  LV: { code: "LV", name: "Latvia", flag: "🇱🇻" },
  EE: { code: "EE", name: "Estonia", flag: "🇪🇪" },
  
  // Asian countries
  KR: { code: "KR", name: "South Korea", flag: "🇰🇷" },
  SG: { code: "SG", name: "Singapore", flag: "🇸🇬" },
  HK: { code: "HK", name: "Hong Kong", flag: "🇭🇰" },
  TW: { code: "TW", name: "Taiwan", flag: "🇹🇼" },
  MY: { code: "MY", name: "Malaysia", flag: "🇲🇾" },
  TH: { code: "TH", name: "Thailand", flag: "🇹🇭" },
  ID: { code: "ID", name: "Indonesia", flag: "🇮🇩" },
  PH: { code: "PH", name: "Philippines", flag: "🇵🇭" },
  VN: { code: "VN", name: "Vietnam", flag: "🇻🇳" },
  
  // Middle East & Africa
  AE: { code: "AE", name: "United Arab Emirates", flag: "🇦🇪" },
  SA: { code: "SA", name: "Saudi Arabia", flag: "🇸🇦" },
  IL: { code: "IL", name: "Israel", flag: "🇮🇱" },
  TR: { code: "TR", name: "Turkey", flag: "🇹🇷" },
  ZA: { code: "ZA", name: "South Africa", flag: "🇿🇦" },
  EG: { code: "EG", name: "Egypt", flag: "🇪🇬" },
  
  // Americas
  MX: { code: "MX", name: "Mexico", flag: "🇲🇽" },
  AR: { code: "AR", name: "Argentina", flag: "🇦🇷" },
  CL: { code: "CL", name: "Chile", flag: "🇨🇱" },
  CO: { code: "CO", name: "Colombia", flag: "🇨🇴" },
  PE: { code: "PE", name: "Peru", flag: "🇵🇪" },
  
  // Oceania
  NZ: { code: "NZ", name: "New Zealand", flag: "🇳🇿" },
  
  // Other
  RU: { code: "RU", name: "Russia", flag: "🇷🇺" },
  UA: { code: "UA", name: "Ukraine", flag: "🇺🇦" },
};

/**
 * Get country information by country code
 */
export function getCountryInfo(countryCode: string): CountryInfo {
  const upperCode = countryCode.toUpperCase();
  return COUNTRIES[upperCode] || {
    code: upperCode,
    name: upperCode,
    flag: "🏳️",
  };
}

/**
 * Get country name by country code
 */
export function getCountryName(countryCode: string): string {
  return getCountryInfo(countryCode).name;
}

/**
 * Get flag emoji by country code
 */
export function getFlagEmoji(countryCode: string): string {
  return getCountryInfo(countryCode).flag;
}

/**
 * Check if country code is valid
 */
export function isValidCountryCode(countryCode: string): boolean {
  return countryCode.toUpperCase() in COUNTRIES;
}

/**
 * Get all available countries
 */
export function getAllCountries(): CountryInfo[] {
  return Object.values(COUNTRIES);
}

/**
 * Search countries by name
 */
export function searchCountries(query: string): CountryInfo[] {
  const lowerQuery = query.toLowerCase();
  return Object.values(COUNTRIES).filter(country =>
    country.name.toLowerCase().includes(lowerQuery) ||
    country.code.toLowerCase().includes(lowerQuery)
  );
}

/**
 * Format visitor count for display
 */
export function formatVisitorCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  }
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`;
  }
  return count.toString();
}

/**
 * Format percentage for display
 */
export function formatPercentage(percentage: number): string {
  return `${percentage.toFixed(1)}%`;
}

/**
 * Format revenue for display
 */
export function formatRevenue(revenue: number): string {
  if (revenue >= 1000000) {
    return `$${(revenue / 1000000).toFixed(1)}M`;
  }
  if (revenue >= 1000) {
    return `$${(revenue / 1000).toFixed(1)}k`;
  }
  return `$${revenue.toFixed(2)}`;
}

/**
 * Get color intensity based on visitor count
 */
export function getColorIntensity(
  visitors: number,
  maxVisitors: number,
  minIntensity = 0.1,
  maxIntensity = 1
): number {
  if (maxVisitors === 0) return minIntensity;
  const ratio = visitors / maxVisitors;
  return minIntensity + (maxIntensity - minIntensity) * ratio;
}

/**
 * Generate color scale for map visualization
 */
export function generateColorScale(baseColor = "#3b82f6"): string[] {
  return [
    `${baseColor}10`, // 10% opacity
    `${baseColor}30`, // 30% opacity
    `${baseColor}50`, // 50% opacity
    `${baseColor}70`, // 70% opacity
    `${baseColor}90`, // 90% opacity
    baseColor,        // 100% opacity
  ];
}

/**
 * Sort countries by visitor count
 */
export function sortByVisitors<T extends { unique_visitors: number }>(
  items: T[],
  order: 'asc' | 'desc' = 'desc'
): T[] {
  return [...items].sort((a, b) => {
    return order === 'desc' 
      ? b.unique_visitors - a.unique_visitors
      : a.unique_visitors - b.unique_visitors;
  });
}

/**
 * Sort countries by revenue
 */
export function sortByRevenue<T extends { total_revenue: number }>(
  items: T[],
  order: 'asc' | 'desc' = 'desc'
): T[] {
  return [...items].sort((a, b) => {
    return order === 'desc' 
      ? b.total_revenue - a.total_revenue
      : a.total_revenue - b.total_revenue;
  });
}
