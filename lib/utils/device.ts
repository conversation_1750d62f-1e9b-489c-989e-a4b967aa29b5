/**
 * Device Analytics Utilities
 * Icon mapping and utility functions for device, browser, and OS data
 */

import type { DeviceInfo, BrowserInfo, OSInfo } from "@/lib/types/device";

// Device type mapping with icons
export const DEVICE_INFO: Record<string, DeviceInfo> = {
  Desktop: {
    name: "Desktop",
    displayName: "Desktop",
    icon: "🖥️",
    category: "desktop",
  },
  Mobile: {
    name: "Mobile",
    displayName: "Mobile",
    icon: "📱",
    category: "mobile",
  },
  Tablet: {
    name: "Tablet",
    displayName: "Tablet",
    icon: "📱",
    category: "tablet",
  },
  TV: {
    name: "TV",
    displayName: "Smart TV",
    icon: "📺",
    category: "other",
  },
  Console: {
    name: "Console",
    displayName: "Game Console",
    icon: "🎮",
    category: "other",
  },
  Unknown: {
    name: "Unknown",
    displayName: "Unknown Device",
    icon: "❓",
    category: "other",
  },
};

// Browser mapping with icons
export const BROWSER_INFO: Record<string, BrowserInfo> = {
  Chrome: {
    name: "Chrome",
    displayName: "Google Chrome",
    icon: "🌐",
    vendor: "Google",
  },
  Safari: {
    name: "Safari",
    displayName: "Safari",
    icon: "🧭",
    vendor: "Apple",
  },
  Firefox: {
    name: "Firefox",
    displayName: "Mozilla Firefox",
    icon: "🦊",
    vendor: "Mozilla",
  },
  Edge: {
    name: "Edge",
    displayName: "Microsoft Edge",
    icon: "🌊",
    vendor: "Microsoft",
  },
  "Microsoft Edge": {
    name: "Microsoft Edge",
    displayName: "Microsoft Edge",
    icon: "🌊",
    vendor: "Microsoft",
  },
  Opera: {
    name: "Opera",
    displayName: "Opera",
    icon: "🎭",
    vendor: "Opera Software",
  },
  Brave: {
    name: "Brave",
    displayName: "Brave Browser",
    icon: "🦁",
    vendor: "Brave Software",
  },
  "Samsung Browser": {
    name: "Samsung Browser",
    displayName: "Samsung Internet",
    icon: "📱",
    vendor: "Samsung",
  },
  "MIUI Browser": {
    name: "MIUI Browser",
    displayName: "MIUI Browser",
    icon: "📱",
    vendor: "Xiaomi",
  },
  "Mobile App": {
    name: "Mobile App",
    displayName: "Mobile App",
    icon: "📱",
    vendor: "Various",
  },
  "Chrome Mobile": {
    name: "Chrome Mobile",
    displayName: "Chrome Mobile",
    icon: "📱",
    vendor: "Google",
  },
  "Safari Mobile": {
    name: "Safari Mobile",
    displayName: "Safari Mobile",
    icon: "📱",
    vendor: "Apple",
  },
  Unknown: {
    name: "Unknown",
    displayName: "Unknown Browser",
    icon: "❓",
    vendor: "Unknown",
  },
};

// Operating System mapping with icons
export const OS_INFO: Record<string, OSInfo> = {
  Windows: {
    name: "Windows",
    displayName: "Microsoft Windows",
    icon: "🪟",
    category: "desktop",
  },
  macOS: {
    name: "macOS",
    displayName: "macOS",
    icon: "🍎",
    category: "desktop",
  },
  Mac: {
    name: "Mac",
    displayName: "macOS",
    icon: "🍎",
    category: "desktop",
  },
  iOS: {
    name: "iOS",
    displayName: "iOS",
    icon: "📱",
    category: "mobile",
  },
  Android: {
    name: "Android",
    displayName: "Android",
    icon: "🤖",
    category: "mobile",
  },
  Linux: {
    name: "Linux",
    displayName: "Linux",
    icon: "🐧",
    category: "desktop",
  },
  "GNU/Linux": {
    name: "GNU/Linux",
    displayName: "GNU/Linux",
    icon: "🐧",
    category: "desktop",
  },
  Ubuntu: {
    name: "Ubuntu",
    displayName: "Ubuntu",
    icon: "🟠",
    category: "desktop",
  },
  "Chrome OS": {
    name: "Chrome OS",
    displayName: "Chrome OS",
    icon: "🌐",
    category: "desktop",
  },
  iPadOS: {
    name: "iPadOS",
    displayName: "iPadOS",
    icon: "📱",
    category: "mobile",
  },
  "Windows Phone": {
    name: "Windows Phone",
    displayName: "Windows Phone",
    icon: "📱",
    category: "mobile",
  },
  Unknown: {
    name: "Unknown",
    displayName: "Unknown OS",
    icon: "❓",
    category: "other",
  },
};

// Device pattern matching arrays for intelligent categorization
const MOBILE_PATTERNS = [
  "iphone",
  "android",
  "samsung",
  "pixel",
  "oneplus",
  "huawei",
  "xiaomi",
  "oppo",
  "vivo",
  "realme",
  "motorola",
  "lg",
  "sony",
  "nokia",
  "blackberry",
  "mobile",
  "phone",
  "sm-",
  "gt-",
  "sgh-",
  "sph-",
  "sch-",
];

const TABLET_PATTERNS = [
  "ipad",
  "tablet",
  "kindle",
  "surface",
  "galaxy tab",
  "nexus 7",
  "nexus 9",
  "nexus 10",
  "fire",
  "mediapad",
  "mipad",
  "tab ",
  "slate",
];

const DESKTOP_PATTERNS = [
  "windows",
  "macintosh",
  "mac os",
  "linux",
  "ubuntu",
  "fedora",
  "debian",
  "chrome os",
  "chromebook",
  "macbook",
  "imac",
  "pc",
  "desktop",
  "laptop",
];

const TV_PATTERNS = [
  "smart tv",
  "tv",
  "roku",
  "apple tv",
  "chromecast",
  "fire tv",
  "android tv",
  "webos",
  "tizen",
  "playstation",
  "xbox",
  "nintendo",
];

/**
 * Categorize a device name into one of the main categories
 */
function categorizeDevice(
  deviceName: string
): "desktop" | "mobile" | "tablet" | "other" {
  const lowerName = deviceName.toLowerCase();

  // Check for tablet patterns first (more specific)
  if (TABLET_PATTERNS.some((pattern) => lowerName.includes(pattern))) {
    return "tablet";
  }

  // Check for mobile patterns
  if (MOBILE_PATTERNS.some((pattern) => lowerName.includes(pattern))) {
    return "mobile";
  }

  // Check for desktop patterns
  if (DESKTOP_PATTERNS.some((pattern) => lowerName.includes(pattern))) {
    return "desktop";
  }

  // Check for TV patterns
  if (TV_PATTERNS.some((pattern) => lowerName.includes(pattern))) {
    return "other";
  }

  return "other";
}

/**
 * Generate a user-friendly display name from a raw device name
 */
function getDeviceDisplayName(
  deviceName: string,
  category: "desktop" | "mobile" | "tablet" | "other"
): string {
  const lowerName = deviceName.toLowerCase();

  // Handle specific device name patterns
  if (lowerName.includes("iphone")) {
    return "iPhone";
  }
  if (lowerName.includes("ipad")) {
    return "iPad";
  }
  if (lowerName.includes("macbook") || lowerName.includes("imac")) {
    return "Mac";
  }
  if (lowerName.includes("samsung")) {
    if (category === "tablet") return "Samsung Tablet";
    return "Samsung";
  }
  if (lowerName.includes("pixel")) {
    return "Google Pixel";
  }
  if (lowerName.includes("windows")) {
    return "Windows PC";
  }
  if (lowerName.includes("android")) {
    if (category === "tablet") return "Android Tablet";
    return "Android";
  }
  if (lowerName.includes("chrome os") || lowerName.includes("chromebook")) {
    return "Chromebook";
  }
  if (lowerName.includes("linux")) {
    return "Linux PC";
  }

  // Fallback to category-based names
  switch (category) {
    case "desktop":
      return "Desktop";
    case "mobile":
      return "Mobile";
    case "tablet":
      return "Tablet";
    default:
      return deviceName || "Unknown Device";
  }
}

// Utility functions
export function getDeviceInfo(deviceName: string): DeviceInfo {
  // First try direct lookup for exact matches (backwards compatibility)
  if (DEVICE_INFO[deviceName]) {
    return DEVICE_INFO[deviceName];
  }

  // If no exact match, use intelligent categorization
  const category = categorizeDevice(deviceName);
  const displayName = getDeviceDisplayName(deviceName, category);

  // Get the appropriate icon and info based on category
  switch (category) {
    case "desktop":
      return {
        name: deviceName,
        displayName,
        icon: "🖥️",
        category: "desktop",
      };
    case "mobile":
      return {
        name: deviceName,
        displayName,
        icon: "📱",
        category: "mobile",
      };
    case "tablet":
      return {
        name: deviceName,
        displayName,
        icon: "📱",
        category: "tablet",
      };
    default:
      return {
        name: deviceName,
        displayName,
        icon: "❓",
        category: "other",
      };
  }
}

export function getBrowserInfo(browserName: string): BrowserInfo {
  // Handle common browser name variations
  const normalizedName = browserName.replace(/\s+/g, " ").trim();

  return (
    BROWSER_INFO[normalizedName] ||
    BROWSER_INFO[browserName] ||
    BROWSER_INFO.Unknown
  );
}

export function getOSInfo(osName: string): OSInfo {
  // Handle common OS name variations
  const normalizedName = osName.replace(/\s+/g, " ").trim();

  return OS_INFO[normalizedName] || OS_INFO[osName] || OS_INFO.Unknown;
}

export function formatDevicePercentage(percentage: number): string {
  return `${percentage.toFixed(1)}%`;
}

export function formatVisitorCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
}

export function formatRevenue(revenue: number): string {
  if (revenue >= 1000000) {
    return `$${(revenue / 1000000).toFixed(1)}M`;
  } else if (revenue >= 1000) {
    return `$${(revenue / 1000).toFixed(1)}K`;
  }
  return `$${revenue.toFixed(2)}`;
}

export function getDeviceCategory(
  deviceName: string
): "desktop" | "mobile" | "tablet" | "other" {
  const deviceInfo = getDeviceInfo(deviceName);
  return deviceInfo.category;
}

export function getBrowserVendor(browserName: string): string {
  const browserInfo = getBrowserInfo(browserName);
  return browserInfo.vendor;
}

export function getOSCategory(
  osName: string
): "desktop" | "mobile" | "server" | "other" {
  const osInfo = getOSInfo(osName);
  return osInfo.category;
}

// Color schemes for different device types
export const DEVICE_COLORS = {
  desktop: "#3B82F6", // Blue
  mobile: "#10B981", // Green
  tablet: "#F59E0B", // Amber
  other: "#6B7280", // Gray
};

export const BROWSER_COLORS = {
  Chrome: "#4285F4",
  Safari: "#007AFF",
  Firefox: "#FF7139",
  Edge: "#0078D4",
  Opera: "#FF1B2D",
  Brave: "#FB542B",
  other: "#6B7280",
};

export const OS_COLORS = {
  Windows: "#0078D4",
  macOS: "#007AFF",
  iOS: "#007AFF",
  Android: "#3DDC84",
  Linux: "#FCC624",
  "Chrome OS": "#4285F4",
  other: "#6B7280",
};

export function getDeviceColor(deviceName: string): string {
  const category = getDeviceCategory(deviceName);
  return DEVICE_COLORS[category] || DEVICE_COLORS.other;
}

export function getBrowserColor(browserName: string): string {
  const normalizedName = browserName.split(" ")[0]; // Get first word
  return (
    BROWSER_COLORS[normalizedName as keyof typeof BROWSER_COLORS] ||
    BROWSER_COLORS.other
  );
}

export function getOSColor(osName: string): string {
  const normalizedName = osName.split(" ")[0]; // Get first word
  return OS_COLORS[normalizedName as keyof typeof OS_COLORS] || OS_COLORS.other;
}

// Sort functions
export function sortByVisitors<T extends { visitors: number }>(
  a: T,
  b: T
): number {
  return b.visitors - a.visitors;
}

export function sortByPercentage<T extends { percentage: number }>(
  a: T,
  b: T
): number {
  return b.percentage - a.percentage;
}

export function sortByRevenue<T extends { total_revenue: number }>(
  a: T,
  b: T
): number {
  return b.total_revenue - a.total_revenue;
}
