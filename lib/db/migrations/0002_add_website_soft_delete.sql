-- Migration: Add Website Soft Delete Support
-- Created: 2024-01-02
-- Description: Add deletedAt field to websites table for soft delete functionality

-- Add deletedAt column to websites table
ALTER TABLE "websites" ADD COLUMN "deleted_at" timestamp;

-- Create index on deletedAt for efficient queries
CREATE INDEX IF NOT EXISTS "websites_deleted_at_idx" ON "websites" ("deleted_at");

-- Create partial index for active websites (where deleted_at IS NULL)
CREATE INDEX IF NOT EXISTS "websites_active_idx" ON "websites" ("user_id", "is_active") WHERE "deleted_at" IS NULL;

-- Update existing queries to exclude soft-deleted websites by default
-- Note: Application code will need to be updated to filter out deleted websites
-- This migration only adds the schema support for soft deletion
