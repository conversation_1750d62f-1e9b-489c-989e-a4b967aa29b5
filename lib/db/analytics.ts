"use server"

import { and, count, desc, eq, gte, lte, sql } from "drizzle-orm"
import { db } from "./index"
import { events, websites } from "./schema"

export interface DateRange {
  from: Date
  to: Date
}

export interface AnalyticsFilters {
  websiteId: string
  dateRange: DateRange
  country?: string
  device?: string
  browser?: string
  utmSource?: string
}

// Get overview metrics for a website
export async function getWebsiteOverview(filters: AnalyticsFilters) {
  const { websiteId, dateRange, country, device, browser, utmSource } = filters

  const conditions = [
    eq(events.websiteId, websiteId),
    gte(events.timestamp, dateRange.from),
    lte(events.timestamp, dateRange.to),
  ]

  if (country) conditions.push(eq(events.country, country))
  if (device) conditions.push(eq(events.device, device))
  if (browser) conditions.push(eq(events.browser, browser))
  if (utmSource) conditions.push(eq(events.utm_source, utmSource))

  const [visitors, pageviews, bounceRate, avgSessionDuration, totalRevenue] = await Promise.all([
    // Unique visitors
    db
      .select({ count: sql<number>`count(distinct ${events.visitorId})` })
      .from(events)
      .where(and(...conditions)),

    // Total pageviews
    db
      .select({ count: count() })
      .from(events)
      .where(and(...conditions, eq(events.eventType, "pageview"))),

    // Bounce rate (sessions with only 1 pageview)
    db
      .select({
        bounceRate: sql<number>`
          round(
            (count(case when session_pageviews = 1 then 1 end) * 100.0 / count(*)), 2
          )
        `,
      })
      .from(
        db
          .select({
            sessionId: events.sessionId,
            session_pageviews: sql<number>`count(case when ${events.eventType} = 'pageview' then 1 end)`,
          })
          .from(events)
          .where(and(...conditions))
          .groupBy(events.sessionId)
          .as("sessions")
      ),

    // Average session duration
    db
      .select({
        avgDuration: sql<number>`
          round(avg(session_duration_minutes), 2)
        `,
      })
      .from(
        db
          .select({
            sessionId: events.sessionId,
            session_duration_minutes: sql<number>`
              extract(epoch from (max(${events.timestamp}) - min(${events.timestamp}))) / 60
            `,
          })
          .from(events)
          .where(and(...conditions))
          .groupBy(events.sessionId)
          .having(sql`count(*) > 1`)
          .as("sessions")
      ),

    // Total revenue
    db
      .select({ total: sql<number>`coalesce(sum(${events.revenue}), 0)` })
      .from(events)
      .where(and(...conditions, sql`${events.revenue} is not null`)),
  ])

  return {
    visitors: visitors[0]?.count || 0,
    pageviews: pageviews[0]?.count || 0,
    bounceRate: bounceRate[0]?.bounceRate || 0,
    avgSessionDuration: avgSessionDuration[0]?.avgDuration || 0,
    totalRevenue: totalRevenue[0]?.total || 0,
  }
}

// Get time-series data for charts
export async function getTimeSeriesData(
  filters: AnalyticsFilters,
  metric: "visitors" | "pageviews" | "revenue",
  interval: "hour" | "day" | "week" | "month" = "day"
) {
  const { websiteId, dateRange } = filters

  const conditions = [
    eq(events.websiteId, websiteId),
    gte(events.timestamp, dateRange.from),
    lte(events.timestamp, dateRange.to),
  ]

  let dateFormat: string
  switch (interval) {
    case "hour":
      dateFormat = "YYYY-MM-DD HH24:00:00"
      break
    case "week":
      dateFormat = 'YYYY-"W"WW'
      break
    case "month":
      dateFormat = "YYYY-MM"
      break
    default:
      dateFormat = "YYYY-MM-DD"
  }

  let selectExpression: any
  switch (metric) {
    case "visitors":
      selectExpression = sql<number>`count(distinct ${events.visitorId})`
      break
    case "pageviews":
      selectExpression = count()
      conditions.push(eq(events.eventType, "pageview"))
      break
    case "revenue":
      selectExpression = sql<number>`coalesce(sum(${events.revenue}), 0)`
      conditions.push(sql`${events.revenue} is not null`)
      break
  }

  const result = await db
    .select({
      date: sql<string>`to_char(${events.timestamp}, '${dateFormat}')`,
      value: selectExpression,
    })
    .from(events)
    .where(and(...conditions))
    .groupBy(sql`to_char(${events.timestamp}, '${dateFormat}')`)
    .orderBy(sql`to_char(${events.timestamp}, '${dateFormat}')`)

  return result
}

// Get top pages
export async function getTopPages(filters: AnalyticsFilters, limit = 10) {
  const { websiteId, dateRange } = filters

  const conditions = [
    eq(events.websiteId, websiteId),
    eq(events.eventType, "pageview"),
    gte(events.timestamp, dateRange.from),
    lte(events.timestamp, dateRange.to),
  ]

  return await db
    .select({
      url: events.url,
      pageviews: count(),
      visitors: sql<number>`count(distinct ${events.visitorId})`,
      bounceRate: sql<number>`
        round(
          (count(case when visitor_pageviews = 1 then 1 end) * 100.0 / count(distinct ${events.visitorId})), 2
        )
      `,
      avgDuration: sql<number>`
        round(avg(
          case when visitor_pageviews > 1 then 
            extract(epoch from (visitor_last_seen - visitor_first_seen)) / 60
          else null end
        ), 2)
      `,
    })
    .from(
      db
        .select({
          url: events.url,
          visitorId: events.visitorId,
          timestamp: events.timestamp,
          visitor_pageviews: sql<number>`count(*) over (partition by ${events.visitorId})`,
          visitor_first_seen: sql<Date>`min(${events.timestamp}) over (partition by ${events.visitorId})`,
          visitor_last_seen: sql<Date>`max(${events.timestamp}) over (partition by ${events.visitorId})`,
        })
        .from(events)
        .where(and(...conditions))
        .as("page_events")
    )
    .groupBy(sql`url`)
    .orderBy(desc(count()))
    .limit(limit)
}

// Get traffic sources
export async function getTrafficSources(filters: AnalyticsFilters, limit = 10) {
  const { websiteId, dateRange } = filters

  const conditions = [
    eq(events.websiteId, websiteId),
    gte(events.timestamp, dateRange.from),
    lte(events.timestamp, dateRange.to),
  ]

  return await db
    .select({
      source: sql<string>`
        case 
          when ${events.utm_source} is not null then ${events.utm_source}
          when ${events.referrer} is null or ${events.referrer} = '' then 'Direct'
          when ${events.referrer} like '%google%' then 'Google'
          when ${events.referrer} like '%facebook%' then 'Facebook'
          when ${events.referrer} like '%twitter%' then 'Twitter'
          when ${events.referrer} like '%linkedin%' then 'LinkedIn'
          else 'Other'
        end
      `,
      visitors: sql<number>`count(distinct ${events.visitorId})`,
      pageviews: count(),
      revenue: sql<number>`coalesce(sum(${events.revenue}), 0)`,
      conversionRate: sql<number>`
        round(
          (count(case when ${events.revenue} > 0 then 1 end) * 100.0 / count(distinct ${events.visitorId})), 2
        )
      `,
    })
    .from(events)
    .where(and(...conditions))
    .groupBy(
      sql`
      case 
        when ${events.utm_source} is not null then ${events.utm_source}
        when ${events.referrer} is null or ${events.referrer} = '' then 'Direct'
        when ${events.referrer} like '%google%' then 'Google'
        when ${events.referrer} like '%facebook%' then 'Facebook'
        when ${events.referrer} like '%twitter%' then 'Twitter'
        when ${events.referrer} like '%linkedin%' then 'LinkedIn'
        else 'Other'
      end
    `
    )
    .orderBy(desc(sql<number>`count(distinct ${events.visitorId})`))
    .limit(limit)
}

// Get device/browser breakdown
export async function getDeviceBreakdown(filters: AnalyticsFilters) {
  const { websiteId, dateRange } = filters

  const conditions = [
    eq(events.websiteId, websiteId),
    gte(events.timestamp, dateRange.from),
    lte(events.timestamp, dateRange.to),
  ]

  const [devices, browsers, os] = await Promise.all([
    // Device breakdown
    db
      .select({
        device: events.device,
        visitors: sql<number>`count(distinct ${events.visitorId})`,
        percentage: sql<number>`
          round(
            (count(distinct ${events.visitorId}) * 100.0 / 
             sum(count(distinct ${events.visitorId})) over()), 2
          )
        `,
      })
      .from(events)
      .where(and(...conditions, sql`${events.device} is not null`))
      .groupBy(events.device)
      .orderBy(desc(sql<number>`count(distinct ${events.visitorId})`)),

    // Browser breakdown
    db
      .select({
        browser: events.browser,
        visitors: sql<number>`count(distinct ${events.visitorId})`,
        percentage: sql<number>`
          round(
            (count(distinct ${events.visitorId}) * 100.0 / 
             sum(count(distinct ${events.visitorId})) over()), 2
          )
        `,
      })
      .from(events)
      .where(and(...conditions, sql`${events.browser} is not null`))
      .groupBy(events.browser)
      .orderBy(desc(sql<number>`count(distinct ${events.visitorId})`)),

    // OS breakdown
    db
      .select({
        os: events.os,
        visitors: sql<number>`count(distinct ${events.visitorId})`,
        percentage: sql<number>`
          round(
            (count(distinct ${events.visitorId}) * 100.0 / 
             sum(count(distinct ${events.visitorId})) over()), 2
          )
        `,
      })
      .from(events)
      .where(and(...conditions, sql`${events.os} is not null`))
      .groupBy(events.os)
      .orderBy(desc(sql<number>`count(distinct ${events.visitorId})`)),
  ])

  return { devices, browsers, os }
}

// Get real-time visitors (last 30 minutes)
export async function getRealTimeVisitors(websiteId: string) {
  const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)

  return await db
    .select({
      id: events.id,
      visitorId: events.visitorId,
      country: events.country,
      city: events.city,
      url: events.url,
      referrer: events.referrer,
      timestamp: events.timestamp,
      device: events.device,
      browser: events.browser,
    })
    .from(events)
    .where(
      and(
        eq(events.websiteId, websiteId),
        eq(events.eventType, "pageview"),
        gte(events.timestamp, thirtyMinutesAgo)
      )
    )
    .orderBy(desc(events.timestamp))
    .limit(50)
}
