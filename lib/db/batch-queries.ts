"use server";

import { <PERSON><PERSON><PERSON><PERSON>s, <PERSON>acheTTL, getCachedData } from "@/lib/cache/redis";
import { and, count, desc, eq, gte, lte, sql } from "drizzle-orm";
import { db } from "./index";
import { events } from "./schema";
import { withPerformanceTracking } from "./config";

// Batched dashboard data query for maximum performance
export const getBatchedDashboardData = withPerformanceTracking(
  "getBatchedDashboardData",
  async (websiteId: string, dateRange: { from: Date; to: Date }) => {
    const cacheKey = `dashboard_batch:${websiteId}:${dateRange.from.toISOString()}:${dateRange.to.toISOString()}`;
    
    return getCachedData(
      cacheKey,
      async () => {
        // Single optimized query to get all dashboard metrics
        const result = await db
          .select({
            totalVisitors: sql<number>`count(distinct case when event_type = 'pageview' then visitor_id end)`,
            totalPageviews: sql<number>`count(case when event_type = 'pageview' then 1 end)`,
            totalRevenue: sql<number>`coalesce(sum(revenue), 0)`,
            avgSessionDuration: sql<number>`
              coalesce(
                avg(
                  extract(epoch from 
                    (max(timestamp) over (partition by session_id) - min(timestamp) over (partition by session_id))
                  )
                ) filter (where event_type = 'pageview'), 
                0
              )
            `,
            bounceRate: sql<number>`
              round(
                coalesce(
                  (count(distinct case when session_pageviews = 1 then session_id end) * 100.0 / 
                   nullif(count(distinct case when event_type = 'pageview' then session_id end), 0)),
                  0
                ), 2
              )
            `,
            topPages: sql<any>`
              json_agg(
                json_build_object(
                  'url', url, 
                  'views', page_views,
                  'visitors', unique_visitors
                )
                ORDER BY page_views DESC
              ) FILTER (WHERE page_rank <= 10 AND event_type = 'pageview')
            `
          })
          .from(
            db
              .select({
                visitorId: events.visitorId,
                sessionId: events.sessionId,
                eventType: events.eventType,
                revenue: events.revenue,
                url: events.url,
                timestamp: events.timestamp,
                sessionPageviews: sql<number>`
                  count(*) over (partition by session_id) filter (where event_type = 'pageview')
                `,
                pageViews: sql<number>`count(*) filter (where event_type = 'pageview')`,
                uniqueVisitors: sql<number>`count(distinct visitor_id)`,
                pageRank: sql<number>`
                  row_number() over (
                    partition by case when event_type = 'pageview' then 1 else 0 end
                    order by count(*) desc
                  )
                `
              })
              .from(events)
              .where(
                and(
                  eq(events.websiteId, websiteId),
                  gte(events.timestamp, dateRange.from),
                  lte(events.timestamp, dateRange.to)
                )
              )
              .groupBy(
                events.visitorId, 
                events.sessionId, 
                events.eventType, 
                events.revenue, 
                events.url, 
                events.timestamp
              )
              .as('aggregated_data')
          );
        
        return {
          visitors: result[0]?.totalVisitors || 0,
          pageviews: result[0]?.totalPageviews || 0,
          totalRevenue: result[0]?.totalRevenue || 0,
          bounceRate: result[0]?.bounceRate || 0,
          avgSessionDuration: result[0]?.avgSessionDuration || 0,
          topPages: result[0]?.topPages || []
        };
      },
      CacheTTL.DASHBOARD_OVERVIEW
    );
  }
);

// Optimized time-series data with better aggregation
export const getBatchedTimeSeriesData = withPerformanceTracking(
  "getBatchedTimeSeriesData",
  async (
    websiteId: string,
    dateRange: { from: Date; to: Date },
    metrics: ("visitors" | "pageviews" | "revenue")[],
    interval: "hour" | "day" | "week" | "month" = "day"
  ) => {
    const cacheKey = `timeseries_batch:${websiteId}:${metrics.join(',')}:${interval}:${dateRange.from.toISOString()}:${dateRange.to.toISOString()}`;
    
    return getCachedData(
      cacheKey,
      async () => {
        let dateFormat: string;
        
        // Optimize date formatting based on interval
        switch (interval) {
          case "hour":
            dateFormat = "YYYY-MM-DD HH24:00:00";
            break;
          case "week":
            dateFormat = 'YYYY-"W"WW';
            break;
          case "month":
            dateFormat = "YYYY-MM";
            break;
          default:
            dateFormat = "YYYY-MM-DD";
        }

        // Build dynamic select based on requested metrics
        const selectFields: Record<string, any> = {
          date: sql<string>`to_char(${events.timestamp}, '${dateFormat}')`
        };

        if (metrics.includes("visitors")) {
          selectFields.visitors = sql<number>`count(distinct ${events.visitorId})`;
        }
        if (metrics.includes("pageviews")) {
          selectFields.pageviews = sql<number>`count(*) filter (where event_type = 'pageview')`;
        }
        if (metrics.includes("revenue")) {
          selectFields.revenue = sql<number>`coalesce(sum(${events.revenue}), 0)`;
        }

        const conditions = [
          eq(events.websiteId, websiteId),
          gte(events.timestamp, dateRange.from),
          lte(events.timestamp, dateRange.to),
        ];

        const result = await db
          .select(selectFields)
          .from(events)
          .where(and(...conditions))
          .groupBy(sql`to_char(${events.timestamp}, '${dateFormat}')`)
          .orderBy(sql`to_char(${events.timestamp}, '${dateFormat}')`);

        return result;
      },
      CacheTTL.TIME_SERIES
    );
  }
);

// Batch query for multiple websites (for admin/overview pages)
export const getMultiWebsiteMetrics = withPerformanceTracking(
  "getMultiWebsiteMetrics",
  async (websiteIds: string[], dateRange: { from: Date; to: Date }) => {
    const cacheKey = `multi_website:${websiteIds.sort().join(',')}:${dateRange.from.toISOString()}:${dateRange.to.toISOString()}`;
    
    return getCachedData(
      cacheKey,
      async () => {
        const result = await db
          .select({
            websiteId: events.websiteId,
            visitors: sql<number>`count(distinct ${events.visitorId})`,
            pageviews: sql<number>`count(*) filter (where event_type = 'pageview')`,
            revenue: sql<number>`coalesce(sum(${events.revenue}), 0)`,
            lastActivity: sql<Date>`max(${events.timestamp})`
          })
          .from(events)
          .where(
            and(
              sql`${events.websiteId} = ANY(${websiteIds})`,
              gte(events.timestamp, dateRange.from),
              lte(events.timestamp, dateRange.to)
            )
          )
          .groupBy(events.websiteId);

        return result;
      },
      CacheTTL.DASHBOARD_OVERVIEW
    );
  }
);

// Optimized real-time metrics for multiple websites
export const getRealTimeMetrics = withPerformanceTracking(
  "getRealTimeMetrics", 
  async (websiteIds: string[]) => {
    const cacheKey = `realtime:${websiteIds.sort().join(',')}`;
    
    return getCachedData(
      cacheKey,
      async () => {
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        
        const result = await db
          .select({
            websiteId: events.websiteId,
            activeVisitors: sql<number>`count(distinct ${events.visitorId})`,
            recentPageviews: sql<number>`count(*) filter (where event_type = 'pageview')`,
            recentRevenue: sql<number>`coalesce(sum(${events.revenue}), 0)`
          })
          .from(events)
          .where(
            and(
              sql`${events.websiteId} = ANY(${websiteIds})`,
              gte(events.timestamp, fiveMinutesAgo)
            )
          )
          .groupBy(events.websiteId);

        return result;
      },
      30 // 30 second cache for real-time data
    );
  }
);
