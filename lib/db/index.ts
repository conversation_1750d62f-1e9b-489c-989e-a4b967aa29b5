import { neon, neonConfig } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-http";
import * as schema from "./schema";

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required");
}

// Configure Neon for better performance
neonConfig.fetchConnectionCache = true;
neonConfig.fetchEndpoint = (host) => `https://${host}/sql`;
neonConfig.fetchFunction = fetch;

// Enhanced connection configuration for better performance
const sql = neon(process.env.DATABASE_URL, {
  // Connection pooling settings
  arrayMode: false,
  fullResults: false,
  fetchOptions: {
    cache: "no-store", // Prevent caching of connections
  },
});

export const db = drizzle(sql, {
  schema,
  logger: process.env.NODE_ENV === "development",
});
