import { relations } from "drizzle-orm";
import {
  boolean,
  decimal,
  index,
  integer,
  json,
  pgEnum,
  pgTable,
  text,
  timestamp,
  unique,
  uuid,
  varchar,
} from "drizzle-orm/pg-core";

// Enums
export const subscriptionPlanEnum = pgEnum("subscription_plan", [
  "free",
  "pro",
  "enterprise",
]);
export const eventTypeEnum = pgEnum("event_type", [
  "pageview",
  "custom",
  "payment",
  "signup",
  "conversion",
]);

// Users table
export const users = pgTable(
  "users",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    email: varchar("email", { length: 255 }).unique().notNull(),
    name: varchar("name", { length: 255 }),
    image: text("image"),
    subscriptionPlan: subscriptionPlanEnum("subscription_plan")
      .default("free")
      .notNull(),
    stripeCustomerId: varchar("stripe_customer_id", { length: 255 }),
    emailVerified: timestamp("email_verified"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    emailIdx: index("users_email_idx").on(table.email),
  })
);

// Accounts table for Auth.js
export const accounts = pgTable(
  "accounts",
  {
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    type: varchar("type", { length: 255 }).notNull(),
    provider: varchar("provider", { length: 255 }).notNull(),
    providerAccountId: varchar("provider_account_id", {
      length: 255,
    }).notNull(),
    refresh_token: text("refresh_token"),
    access_token: text("access_token"),
    expires_at: integer("expires_at"),
    token_type: varchar("token_type", { length: 255 }),
    scope: varchar("scope", { length: 255 }),
    id_token: text("id_token"),
    session_state: varchar("session_state", { length: 255 }),
  },
  (table) => ({
    compoundKey: index("accounts_provider_idx").on(
      table.provider,
      table.providerAccountId
    ),
  })
);

// Sessions table for Auth.js
export const sessions = pgTable(
  "sessions",
  {
    sessionToken: varchar("session_token", { length: 255 }).primaryKey(),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    expires: timestamp("expires").notNull(),
  },
  (table) => ({
    userIdIdx: index("sessions_user_id_idx").on(table.userId),
  })
);

// Verification tokens table for Auth.js
export const verificationTokens = pgTable("verification_tokens", {
  identifier: varchar("identifier", { length: 255 }).notNull(),
  token: varchar("token", { length: 255 }).notNull(),
  expires: timestamp("expires").notNull(),
});

// Websites table
export const websites = pgTable(
  "websites",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    name: varchar("name", { length: 255 }).notNull(),
    domain: varchar("domain", { length: 255 }).notNull(),
    trackingId: uuid("tracking_id").unique().defaultRandom().notNull(),
    isActive: boolean("is_active").default(true).notNull(),
    timezone: varchar("timezone", { length: 50 }).default("UTC").notNull(),
    currency: varchar("currency", { length: 3 }).default("USD").notNull(),
    deletedAt: timestamp("deleted_at"), // Soft delete timestamp
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    userIdIdx: index("websites_user_id_idx").on(table.userId),
    domainIdx: index("websites_domain_idx").on(table.domain),
    trackingIdIdx: index("websites_tracking_id_idx").on(table.trackingId),
  })
);

// Events table
export const events = pgTable(
  "events",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    websiteId: uuid("website_id")
      .notNull()
      .references(() => websites.id, { onDelete: "cascade" }),
    sessionId: uuid("session_id").notNull(),
    visitorId: uuid("visitor_id").notNull(),
    eventType: eventTypeEnum("event_type").notNull(),
    eventName: varchar("event_name", { length: 255 }),
    url: text("url").notNull(),
    referrer: text("referrer"),
    userAgent: text("user_agent"),
    country: varchar("country", { length: 2 }),
    region: varchar("region", { length: 100 }),
    city: varchar("city", { length: 100 }),
    device: varchar("device", { length: 50 }),
    browser: varchar("browser", { length: 50 }),
    os: varchar("os", { length: 50 }),
    utm_source: varchar("utm_source", { length: 255 }),
    utm_medium: varchar("utm_medium", { length: 255 }),
    utm_campaign: varchar("utm_campaign", { length: 255 }),
    utm_content: varchar("utm_content", { length: 255 }),
    utm_term: varchar("utm_term", { length: 255 }),
    customData: json("custom_data"),
    revenue: decimal("revenue", { precision: 10, scale: 2 }),
    timestamp: timestamp("timestamp").defaultNow().notNull(),
  },
  (table) => ({
    websiteIdIdx: index("events_website_id_idx").on(table.websiteId),
    sessionIdIdx: index("events_session_id_idx").on(table.sessionId),
    visitorIdIdx: index("events_visitor_id_idx").on(table.visitorId),
    timestampIdx: index("events_timestamp_idx").on(table.timestamp),
    eventTypeIdx: index("events_event_type_idx").on(table.eventType),
  })
);

// Goals table
export const goals = pgTable(
  "goals",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    websiteId: uuid("website_id")
      .notNull()
      .references(() => websites.id, { onDelete: "cascade" }),
    name: varchar("name", { length: 255 }).notNull(),
    eventName: varchar("event_name", { length: 255 }),
    targetValue: decimal("target_value", { precision: 10, scale: 2 }),
    isActive: boolean("is_active").default(true).notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    websiteIdIdx: index("goals_website_id_idx").on(table.websiteId),
  })
);

// Payment Provider Credentials table
export const paymentProviderCredentials = pgTable(
  "payment_provider_credentials",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    websiteId: uuid("website_id").references(() => websites.id, {
      onDelete: "cascade",
    }), // Optional: per-website credentials
    provider: varchar("provider", { length: 50 }).notNull(), // stripe, paypal, lemonsqueezy, polar
    credentialType: varchar("credential_type", { length: 50 }).notNull(), // api_key, secret_key, webhook_secret
    encryptedValue: text("encrypted_value").notNull(), // Encrypted credential
    keyPrefix: varchar("key_prefix", { length: 10 }), // First few chars for identification
    isActive: boolean("is_active").default(true).notNull(),
    lastValidated: timestamp("last_validated"),
    validationStatus: varchar("validation_status", { length: 20 }).default(
      "pending"
    ), // pending, valid, invalid
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    userProviderIdx: index("payment_credentials_user_provider_idx").on(
      table.userId,
      table.provider
    ),
    websiteProviderIdx: index("payment_credentials_website_provider_idx").on(
      table.websiteId,
      table.provider
    ),
    uniqueUserProviderType: unique("unique_user_provider_credential_type").on(
      table.userId,
      table.provider,
      table.credentialType,
      table.websiteId
    ),
  })
);

// Webhook Endpoints table
export const webhookEndpoints = pgTable(
  "webhook_endpoints",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    websiteId: uuid("website_id").references(() => websites.id, {
      onDelete: "cascade",
    }),
    provider: varchar("provider", { length: 50 }).notNull(),
    webhookId: varchar("webhook_id", { length: 255 }).notNull(), // Provider's webhook endpoint ID
    webhookUrl: text("webhook_url").notNull(),
    events: text("events").notNull(), // JSON array of subscribed events
    isActive: boolean("is_active").default(true).notNull(),
    lastTriggered: timestamp("last_triggered"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    userProviderIdx: index("webhook_endpoints_user_provider_idx").on(
      table.userId,
      table.provider
    ),
    webhookIdIdx: index("webhook_endpoints_webhook_id_idx").on(table.webhookId),
  })
);

// Revenue Events table for deduplication and audit
export const revenueEvents = pgTable(
  "revenue_events",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    websiteId: uuid("website_id")
      .notNull()
      .references(() => websites.id, { onDelete: "cascade" }),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    provider: varchar("provider", { length: 50 }).notNull(),
    transactionId: varchar("transaction_id", { length: 255 }).notNull(),
    sessionId: uuid("session_id"),
    visitorId: uuid("visitor_id"),
    amount: decimal("amount", { precision: 12, scale: 2 }).notNull(),
    currency: varchar("currency", { length: 3 }).notNull(),
    customerEmail: varchar("customer_email", { length: 255 }),
    source: varchar("source", { length: 20 }).notNull(), // webhook, client, api
    deduplicationId: varchar("deduplication_id", { length: 64 }).notNull(),
    metadata: text("metadata"), // JSON metadata
    processedAt: timestamp("processed_at").defaultNow().notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
  },
  (table) => ({
    websiteIdIdx: index("revenue_events_website_id_idx").on(table.websiteId),
    transactionIdIdx: index("revenue_events_transaction_id_idx").on(
      table.transactionId
    ),
    deduplicationIdIdx: unique("revenue_events_deduplication_id_idx").on(
      table.deduplicationId
    ),
    providerTransactionIdx: index("revenue_events_provider_transaction_idx").on(
      table.provider,
      table.transactionId
    ),
  })
);

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  accounts: many(accounts),
  sessions: many(sessions),
  websites: many(websites),
  paymentProviderCredentials: many(paymentProviderCredentials),
  webhookEndpoints: many(webhookEndpoints),
  revenueEvents: many(revenueEvents),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const websitesRelations = relations(websites, ({ one, many }) => ({
  user: one(users, {
    fields: [websites.userId],
    references: [users.id],
  }),
  events: many(events),
  goals: many(goals),
  paymentProviderCredentials: many(paymentProviderCredentials),
  webhookEndpoints: many(webhookEndpoints),
  revenueEvents: many(revenueEvents),
}));

export const paymentProviderCredentialsRelations = relations(
  paymentProviderCredentials,
  ({ one }) => ({
    user: one(users, {
      fields: [paymentProviderCredentials.userId],
      references: [users.id],
    }),
    website: one(websites, {
      fields: [paymentProviderCredentials.websiteId],
      references: [websites.id],
    }),
  })
);

export const webhookEndpointsRelations = relations(
  webhookEndpoints,
  ({ one }) => ({
    user: one(users, {
      fields: [webhookEndpoints.userId],
      references: [users.id],
    }),
    website: one(websites, {
      fields: [webhookEndpoints.websiteId],
      references: [websites.id],
    }),
  })
);

export const revenueEventsRelations = relations(revenueEvents, ({ one }) => ({
  user: one(users, {
    fields: [revenueEvents.userId],
    references: [users.id],
  }),
  website: one(websites, {
    fields: [revenueEvents.websiteId],
    references: [websites.id],
  }),
}));

export const eventsRelations = relations(events, ({ one }) => ({
  website: one(websites, {
    fields: [events.websiteId],
    references: [websites.id],
  }),
}));

export const goalsRelations = relations(goals, ({ one }) => ({
  website: one(websites, {
    fields: [goals.websiteId],
    references: [websites.id],
  }),
}));

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Website = typeof websites.$inferSelect;
export type NewWebsite = typeof websites.$inferInsert;
export type Event = typeof events.$inferSelect;
export type NewEvent = typeof events.$inferInsert;
export type Goal = typeof goals.$inferSelect;
export type NewGoal = typeof goals.$inferInsert;
