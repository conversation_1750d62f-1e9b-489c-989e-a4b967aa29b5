"use server"

import { auth } from "@/lib/auth"
import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { createGoal, createWebsite, getUserWebsites } from "./queries"

// Website actions
export async function createWebsiteAction(formData: FormData) {
  const session = await auth()
  if (!session?.user?.id) {
    redirect("/auth/signin")
  }

  const name = formData.get("name") as string
  const domain = formData.get("domain") as string
  const timezone = formData.get("timezone") as string
  const currency = formData.get("currency") as string

  if (!name || !domain) {
    throw new Error("Name and domain are required")
  }

  // Validate domain format
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/
  if (!domainRegex.test(domain)) {
    throw new Error("Invalid domain format")
  }

  let websiteId: string
  try {
    const website = await createWebsite({
      userId: session.user.id,
      name,
      domain,
      timezone: timezone || "UTC",
      currency: currency || "USD",
    })
    websiteId = website.id
  } catch (error) {
    console.error("Failed to create website:", error)
    throw new Error("Failed to create website")
  }

  revalidatePath("/dashboard")
  redirect(`/dashboard/websites/${websiteId}`)
}

export async function getUserWebsitesAction() {
  const session = await auth()
  if (!session?.user?.id) {
    return []
  }

  try {
    return await getUserWebsites(session.user.id)
  } catch (error) {
    console.error("Failed to fetch websites:", error)
    return []
  }
}

// Goal actions
export async function createGoalAction(formData: FormData) {
  const session = await auth()
  if (!session?.user?.id) {
    redirect("/auth/signin")
  }

  const websiteId = formData.get("websiteId") as string
  const name = formData.get("name") as string
  const eventName = formData.get("eventName") as string
  const targetValue = formData.get("targetValue") as string

  if (!websiteId || !name) {
    throw new Error("Website ID and goal name are required")
  }

  try {
    await createGoal({
      websiteId,
      name,
      eventName: eventName || undefined,
      targetValue: targetValue ? Number.parseFloat(targetValue) : undefined,
    })

    revalidatePath("/dashboard")
    revalidatePath(`/dashboard/websites/${websiteId}`)
  } catch (error) {
    console.error("Failed to create goal:", error)
    throw new Error("Failed to create goal")
  }
}
