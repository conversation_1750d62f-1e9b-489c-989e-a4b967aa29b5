"use server";

import { <PERSON><PERSON><PERSON><PERSON>s, <PERSON>acheTTL, getCachedData } from "@/lib/cache/redis";
import {
  and,
  count,
  desc,
  eq,
  gte,
  inArray,
  lte,
  sql,
  placeholder,
} from "drizzle-orm";
import { alias } from "drizzle-orm/pg-core";
import { db } from "./index";
import { events, websites } from "./schema";
import { DatabaseMonitor, withPerformanceTracking } from "./config";

// Prepared statements for frequently executed queries
export const preparedQueries = {
  // Website metrics prepared statement
  getWebsiteMetrics: db
    .select({
      visitors: sql<number>`count(distinct ${events.visitorId})`,
      pageviews: count(),
      revenue: sql<number>`coalesce(sum(${events.revenue}), 0)`,
    })
    .from(events)
    .where(
      and(
        eq(events.websiteId, placeholder("websiteId")),
        gte(events.timestamp, placeholder("startDate")),
        lte(events.timestamp, placeholder("endDate"))
      )
    )
    .prepare(),

  // Active visitors prepared statement
  getActiveVisitors: db
    .select({
      count: sql<number>`count(distinct ${events.visitorId})`,
    })
    .from(events)
    .where(
      and(
        eq(events.websiteId, placeholder("websiteId")),
        eq(events.eventType, "pageview"),
        gte(events.timestamp, placeholder("thirtyMinutesAgo"))
      )
    )
    .prepare(),

  // Top pages prepared statement
  getTopPagesQuery: db
    .select({
      url: events.url,
      pageviews: count(),
      visitors: sql<number>`count(distinct ${events.visitorId})`,
    })
    .from(events)
    .where(
      and(
        eq(events.websiteId, placeholder("websiteId")),
        eq(events.eventType, "pageview"),
        gte(events.timestamp, placeholder("startDate")),
        lte(events.timestamp, placeholder("endDate"))
      )
    )
    .groupBy(events.url)
    .orderBy(desc(count()))
    .limit(placeholder("limit"))
    .prepare(),
};

// Optimized query with proper indexing hints and materialized views simulation
export async function getOptimizedOverviewMetrics(
  websiteId: string,
  dateRange: { from: Date; to: Date }
) {
  const cacheKey = CacheKeys.dashboardOverview(
    websiteId,
    dateRange.from.toISOString(),
    dateRange.to.toISOString()
  );

  return getCachedData(
    cacheKey,
    async () => {
      // Use a single optimized query with subqueries for better performance
      const result = await db
        .select({
          // Visitors
          totalVisitors: sql<number>`
            count(distinct case when e.event_type = 'pageview' then e.visitor_id end)
          `,
          // Pageviews
          totalPageviews: sql<number>`
            count(case when e.event_type = 'pageview' then 1 end)
          `,
          // Revenue
          totalRevenue: sql<number>`
            coalesce(sum(e.revenue), 0)
          `,
          // Bounce rate (simplified calculation)
          bounceRate: sql<number>`
            round(
              (count(distinct case when session_pageviews.pageview_count = 1 then e.session_id end) * 100.0 / 
               nullif(count(distinct case when e.event_type = 'pageview' then e.session_id end), 0)), 2
            )
          `,
          // Average session duration (in minutes)
          avgSessionDuration: sql<number>`
            round(avg(session_durations.duration_minutes), 2)
          `,
        })
        .from(alias(events, "e"))
        .leftJoin(
          // Subquery for session pageview counts
          db
            .select({
              sessionId: events.sessionId,
              pageviewCount: sql<number>`count(*)`.as("pageview_count"),
            })
            .from(events)
            .where(
              and(
                eq(events.websiteId, websiteId),
                eq(events.eventType, "pageview"),
                gte(events.timestamp, dateRange.from),
                lte(events.timestamp, dateRange.to)
              )
            )
            .groupBy(events.sessionId)
            .as("session_pageviews"),
          eq(sql`e.session_id`, sql`session_pageviews.session_id`)
        )
        .leftJoin(
          // Subquery for session durations
          db
            .select({
              sessionId: events.sessionId,
              durationMinutes: sql<number>`
                extract(epoch from (max(timestamp) - min(timestamp))) / 60
              `.as("duration_minutes"),
            })
            .from(events)
            .where(
              and(
                eq(events.websiteId, websiteId),
                gte(events.timestamp, dateRange.from),
                lte(events.timestamp, dateRange.to)
              )
            )
            .groupBy(events.sessionId)
            .having(sql`count(*) > 1`)
            .as("session_durations"),
          eq(sql`e.session_id`, sql`session_durations.session_id`)
        )
        .where(
          and(
            eq(sql`e.website_id`, websiteId),
            gte(sql`e.timestamp`, dateRange.from),
            lte(sql`e.timestamp`, dateRange.to)
          )
        );

      return {
        visitors: result[0]?.totalVisitors || 0,
        pageviews: result[0]?.totalPageviews || 0,
        totalRevenue: result[0]?.totalRevenue || 0,
        bounceRate: result[0]?.bounceRate || 0,
        avgSessionDuration: result[0]?.avgSessionDuration || 0,
      };
    },
    CacheTTL.DASHBOARD_OVERVIEW
  );
}

// Optimized time series query with proper date truncation
export async function getOptimizedTimeSeriesData(
  websiteId: string,
  dateRange: { from: Date; to: Date },
  metric: "visitors" | "pageviews" | "revenue",
  interval: "hour" | "day" | "week" | "month" = "day"
) {
  const cacheKey = CacheKeys.timeSeriesData(
    websiteId,
    metric,
    interval,
    dateRange.from.toISOString(),
    dateRange.to.toISOString()
  );

  return getCachedData(
    cacheKey,
    async () => {
      let dateFormat: string;
      let selectExpression: any;

      // Optimize date formatting based on interval
      switch (interval) {
        case "hour":
          dateFormat = "YYYY-MM-DD HH24:00:00";
          break;
        case "week":
          dateFormat = 'YYYY-"W"WW';
          break;
        case "month":
          dateFormat = "YYYY-MM";
          break;
        default:
          dateFormat = "YYYY-MM-DD";
      }

      // Optimize aggregation based on metric
      switch (metric) {
        case "visitors":
          selectExpression = sql<number>`count(distinct ${events.visitorId})`;
          break;
        case "pageviews":
          selectExpression = count();
          break;
        case "revenue":
          selectExpression = sql<number>`coalesce(sum(${events.revenue}), 0)`;
          break;
      }

      const conditions = [
        eq(events.websiteId, websiteId),
        gte(events.timestamp, dateRange.from),
        lte(events.timestamp, dateRange.to),
      ];

      if (metric === "pageviews") {
        conditions.push(eq(events.eventType, "pageview"));
      }

      if (metric === "revenue") {
        conditions.push(sql`${events.revenue} is not null`);
      }

      const result = await db
        .select({
          date: sql<string>`to_char(${events.timestamp}, '${dateFormat}')`,
          value: selectExpression,
        })
        .from(events)
        .where(and(...conditions))
        .groupBy(sql`to_char(${events.timestamp}, '${dateFormat}')`)
        .orderBy(sql`to_char(${events.timestamp}, '${dateFormat}')`);

      return result;
    },
    CacheTTL.TIME_SERIES
  );
}

// Batch query for multiple websites (for admin dashboard)
export async function getBatchWebsiteStats(websiteIds: string[]) {
  if (websiteIds.length === 0) return [];

  const cacheKey = `batch_stats:${websiteIds.sort().join(",")}`;

  return getCachedData(
    cacheKey,
    async () => {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const result = await db
        .select({
          websiteId: events.websiteId,
          visitors: sql<number>`count(distinct case when ${events.eventType} = 'pageview' then ${events.visitorId} end)`,
          pageviews: sql<number>`count(case when ${events.eventType} = 'pageview' then 1 end)`,
          revenue: sql<number>`coalesce(sum(${events.revenue}), 0)`,
        })
        .from(events)
        .where(
          and(
            inArray(events.websiteId, websiteIds),
            gte(events.timestamp, thirtyDaysAgo)
          )
        )
        .groupBy(events.websiteId);

      return result;
    },
    CacheTTL.WEBSITE_STATS
  );
}

// Optimized top pages query with proper indexing
export async function getOptimizedTopPages(
  websiteId: string,
  dateRange: { from: Date; to: Date },
  limit = 10
) {
  const cacheKey = CacheKeys.topPages(
    websiteId,
    dateRange.from.toISOString(),
    dateRange.to.toISOString(),
    limit
  );

  return getCachedData(
    cacheKey,
    async () => {
      const result = await db
        .select({
          url: events.url,
          pageviews: count(),
          visitors: sql<number>`count(distinct ${events.visitorId})`,
          // Simplified bounce rate calculation for performance
          bounceRate: sql<number>`
            round(avg(case when page_sessions.session_pageviews = 1 then 100.0 else 0 end), 2)
          `,
        })
        .from(events)
        .leftJoin(
          // Subquery for session pageview counts
          db
            .select({
              sessionId: events.sessionId,
              sessionPageviews: sql<number>`count(*)`.as("session_pageviews"),
            })
            .from(events)
            .where(
              and(
                eq(events.websiteId, websiteId),
                eq(events.eventType, "pageview"),
                gte(events.timestamp, dateRange.from),
                lte(events.timestamp, dateRange.to)
              )
            )
            .groupBy(events.sessionId)
            .as("page_sessions"),
          eq(events.sessionId, sql`page_sessions.session_id`)
        )
        .where(
          and(
            eq(events.websiteId, websiteId),
            eq(events.eventType, "pageview"),
            gte(events.timestamp, dateRange.from),
            lte(events.timestamp, dateRange.to)
          )
        )
        .groupBy(events.url)
        .orderBy(desc(count()))
        .limit(limit);

      return result;
    },
    CacheTTL.TOP_PAGES
  );
}

// Materialized view simulation for real-time analytics with performance tracking
export const getActiveVisitorCount = withPerformanceTracking(
  "getActiveVisitorCount",
  async (websiteId: string) => {
    const cacheKey = CacheKeys.activeVisitors(websiteId);

    return getCachedData(
      cacheKey,
      async () => {
        // Count unique visitors in the last 30 minutes using prepared statement
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

        const result = await preparedQueries.getActiveVisitors.execute({
          websiteId,
          thirtyMinutesAgo,
        });

        return result[0]?.count || 0;
      },
      CacheTTL.ACTIVE_VISITORS
    );
  }
);

// Query plan optimization hints (for PostgreSQL)
export async function addQueryOptimizationHints() {
  // These would be actual PostgreSQL indexes in a real implementation
  const suggestedIndexes = [
    "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_website_timestamp ON events(website_id, timestamp)",
    "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_visitor_timestamp ON events(visitor_id, timestamp)",
    "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_session_type ON events(session_id, event_type)",
    "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_url_timestamp ON events(url, timestamp) WHERE event_type = 'pageview'",
    "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_revenue ON events(revenue) WHERE revenue IS NOT NULL",
    "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_utm_source ON events(utm_source) WHERE utm_source IS NOT NULL",
  ];

  console.log("Suggested database indexes for optimal performance:");
  suggestedIndexes.forEach((index, i) => {
    console.log(`${i + 1}. ${index}`);
  });
}
