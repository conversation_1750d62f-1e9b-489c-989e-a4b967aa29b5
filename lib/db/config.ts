// Database configuration for performance optimization
export const dbConfig = {
  // Use read replicas for analytics queries in production
  readReplica: process.env.DATABASE_READ_REPLICA_URL || process.env.DATABASE_URL,
  
  // Connection limits
  maxConnections: process.env.NODE_ENV === 'production' ? 20 : 5,
  
  // Query timeouts
  queryTimeout: 30000, // 30 seconds
  
  // Enable query logging in development
  enableLogging: process.env.NODE_ENV === 'development',
  
  // Performance thresholds
  slowQueryThreshold: 1000, // 1 second
  verySlowQueryThreshold: 5000, // 5 seconds
  
  // Cache settings
  defaultCacheTTL: 300, // 5 minutes
  longCacheTTL: 3600, // 1 hour
  shortCacheTTL: 60, // 1 minute
};

// Database performance monitoring
export class DatabaseMonitor {
  private static queryTimes = new Map<string, number[]>();
  private static queryCount = new Map<string, number>();
  
  static trackQuery(queryName: string, duration: number) {
    // Track query times
    const times = this.queryTimes.get(queryName) || [];
    times.push(duration);
    
    // Keep only last 100 measurements
    if (times.length > 100) {
      times.shift();
    }
    
    this.queryTimes.set(queryName, times);
    
    // Track query count
    const count = this.queryCount.get(queryName) || 0;
    this.queryCount.set(queryName, count + 1);
    
    // Alert on slow queries
    if (duration > dbConfig.verySlowQueryThreshold) {
      console.warn(`Very slow Neon query: ${queryName} took ${duration}ms`);
    } else if (duration > dbConfig.slowQueryThreshold) {
      console.warn(`Slow Neon query: ${queryName} took ${duration}ms`);
    }
    
    // Log performance in development
    if (dbConfig.enableLogging && duration > 100) {
      console.log(`Query ${queryName}: ${duration}ms`);
    }
  }
  
  static getAverageQueryTime(queryName: string): number {
    const times = this.queryTimes.get(queryName) || [];
    if (times.length === 0) return 0;
    return times.reduce((a, b) => a + b, 0) / times.length;
  }
  
  static getQueryCount(queryName: string): number {
    return this.queryCount.get(queryName) || 0;
  }
  
  static getMetrics(): Record<string, { count: number; avgTime: number; totalTime: number }> {
    const metrics: Record<string, { count: number; avgTime: number; totalTime: number }> = {};
    
    for (const [queryName, times] of this.queryTimes.entries()) {
      const count = this.queryCount.get(queryName) || 0;
      const totalTime = times.reduce((a, b) => a + b, 0);
      const avgTime = totalTime / times.length;
      
      metrics[queryName] = { count, avgTime, totalTime };
    }
    
    return metrics;
  }
  
  static reset(): void {
    this.queryTimes.clear();
    this.queryCount.clear();
  }
}

// Query performance wrapper
export function withPerformanceTracking<T extends any[], R>(
  queryName: string,
  queryFn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now();
    try {
      const result = await queryFn(...args);
      const duration = Date.now() - startTime;
      DatabaseMonitor.trackQuery(queryName, duration);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      DatabaseMonitor.trackQuery(`${queryName}_ERROR`, duration);
      throw error;
    }
  };
}
