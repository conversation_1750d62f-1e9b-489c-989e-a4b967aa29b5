/**
 * Tinybird 分析数据获取服务
 * 封装所有分析查询逻辑，替换原有的 PostgreSQL 查询
 */

import { type AnalyticsFilters, tinybirdClient } from "./client";

export interface DateRange {
  from: Date;
  to: Date;
}

export interface ExtendedAnalyticsFilters extends AnalyticsFilters {
  dateRange?: DateRange;
}

/**
 * 格式化日期为 Tinybird 查询参数
 */
function formatDateForTinybird(date: Date): string {
  // 格式: YYYY-MM-DD HH:MM:SS（去掉毫秒与 Z）
  return date.toISOString().slice(0, 19).replace("T", " ");
}

/**
 * 转换过滤器格式
 */
function convertFilters(filters: ExtendedAnalyticsFilters): AnalyticsFilters {
  const { dateRange, ...rest } = filters;

  return {
    ...rest,
    start_date: dateRange
      ? formatDateForTinybird(dateRange.from)
      : formatDateForTinybird(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
    end_date: dateRange
      ? formatDateForTinybird(dateRange.to)
      : formatDateForTinybird(new Date()),
  };
}

/**
 * 获取网站概览指标
 */
export async function getWebsiteOverview(filters: ExtendedAnalyticsFilters) {
  const tinybirdFilters = convertFilters(filters);
  return await tinybirdClient.getOverviewMetrics(tinybirdFilters);
}

/**
 * 获取时间序列数据
 */
export async function getTimeSeriesData(
  filters: ExtendedAnalyticsFilters,
  metric: "visitors" | "pageviews" | "revenue",
  interval: "hour" | "day" | "week" | "month" = "day"
) {
  const tinybirdFilters = convertFilters(filters);
  return await tinybirdClient.getTimeSeriesData(
    tinybirdFilters,
    metric,
    interval
  );
}

/**
 * 获取热门页面
 */
export async function getTopPages(
  filters: ExtendedAnalyticsFilters,
  limit = 10
) {
  const tinybirdFilters = convertFilters(filters);
  return await tinybirdClient.getTopPages(tinybirdFilters, limit);
}

/**
 * 获取流量来源
 */
export async function getTrafficSources(
  filters: ExtendedAnalyticsFilters,
  limit = 10
) {
  const tinybirdFilters = convertFilters(filters);
  return await tinybirdClient.getTrafficSources(tinybirdFilters, limit);
}

/**
 * 获取增强的流量来源数据 (Channels, Sources, Campaigns)
 */
export async function getEnhancedTrafficSources(
  filters: ExtendedAnalyticsFilters,
  category: "channels" | "sources" | "campaigns",
  limit = 10
) {
  const tinybirdFilters = convertFilters(filters);
  return await tinybirdClient.getEnhancedTrafficSources(
    tinybirdFilters,
    category,
    limit
  );
}

/**
 * 获取设备统计
 */
export async function getDeviceBreakdown(filters: ExtendedAnalyticsFilters) {
  const tinybirdFilters = convertFilters(filters);

  const [devices, browsers, os] = await Promise.all([
    tinybirdClient.getDeviceBreakdown(tinybirdFilters, "device"),
    tinybirdClient.getDeviceBreakdown(tinybirdFilters, "browser"),
    tinybirdClient.getDeviceBreakdown(tinybirdFilters, "os"),
  ]);

  return { devices, browsers, os };
}

/**
 * 获取实时访客
 */
export async function getRealTimeVisitors(websiteId: string) {
  return await tinybirdClient.getRealtimeVisitors(websiteId);
}

/**
 * 获取地理位置概览数据
 */
export async function getGeographicOverview(filters: ExtendedAnalyticsFilters) {
  const tinybirdFilters = convertFilters(filters);
  return await tinybirdClient.getGeographicOverview(tinybirdFilters);
}

/**
 * 获取国家级地理位置数据
 */
export async function getGeographicCountries(
  filters: ExtendedAnalyticsFilters,
  limit = 50
) {
  const tinybirdFilters = convertFilters(filters);
  return await tinybirdClient.getGeographicCountries(tinybirdFilters, limit);
}

/**
 * 获取地区级地理位置数据
 */
export async function getGeographicRegions(
  filters: ExtendedAnalyticsFilters,
  limit = 50
) {
  const tinybirdFilters = convertFilters(filters);
  return await tinybirdClient.getGeographicRegions(tinybirdFilters, limit);
}

/**
 * 获取城市级地理位置数据
 */
export async function getGeographicCities(
  filters: ExtendedAnalyticsFilters,
  limit = 50
) {
  const tinybirdFilters = convertFilters(filters);
  return await tinybirdClient.getGeographicCities(tinybirdFilters, limit);
}

/**
 * 获取实时地理位置数据
 */
export async function getRealtimeLocations(websiteId: string, limit = 50) {
  return await tinybirdClient.getRealtimeLocations(websiteId, limit);
}

/**
 * 验证网站 ID 格式
 */
export function isValidWebsiteId(websiteId: string): boolean {
  // UUID v4 格式验证
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(websiteId);
}

/**
 * 构建分析过滤器
 */
export function createAnalyticsFilters(params: {
  websiteId: string;
  dateRange?: DateRange;
  country?: string;
  device?: string;
  browser?: string;
  utmSource?: string;
}): ExtendedAnalyticsFilters {
  return {
    website_id: params.websiteId,
    dateRange: params.dateRange,
    country: params.country,
    device: params.device,
    browser: params.browser,
    utm_source: params.utmSource,
    start_date: "", // 将由 convertFilters 填充
    end_date: "", // 将由 convertFilters 填充
  };
}

/**
 * 获取默认日期范围（最近 30 天）
 */
export function getDefaultDateRange(): DateRange {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  return {
    from: thirtyDaysAgo,
    to: now,
  };
}

/**
 * 获取预设日期范围
 */
export function getPresetDateRanges(): Record<string, DateRange> {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  return {
    today: {
      from: today,
      to: now,
    },
    yesterday: {
      from: new Date(today.getTime() - 24 * 60 * 60 * 1000),
      to: today,
    },
    last7days: {
      from: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000),
      to: now,
    },
    last30days: {
      from: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000),
      to: now,
    },
    thisMonth: {
      from: new Date(now.getFullYear(), now.getMonth(), 1),
      to: now,
    },
    lastMonth: {
      from: new Date(now.getFullYear(), now.getMonth() - 1, 1),
      to: new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59),
    },
  };
}

/**
 * 计算增长率
 */
export function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100);
}

/**
 * 格式化数值显示
 */
export function formatMetricValue(
  value: number,
  type: "number" | "percentage" | "currency" | "duration"
): string {
  switch (type) {
    case "number":
      return new Intl.NumberFormat("en-US").format(value);
    case "percentage":
      return `${value.toFixed(1)}%`;
    case "currency":
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(value);
    case "duration":
      if (value < 60) return `${Math.round(value)}s`;
      return `${Math.round(value / 60)}m ${Math.round(value % 60)}s`;
    default:
      return String(value);
  }
}

/**
 * 错误处理装饰器
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | null> => {
    try {
      return await fn(...args);
    } catch (error) {
      console.error("Analytics query failed:", error);
      return null;
    }
  };
}

// 导出带错误处理的分析函数
export const safeGetWebsiteOverview = withErrorHandling(getWebsiteOverview);
export const safeGetTimeSeriesData = withErrorHandling(getTimeSeriesData);
export const safeGetTopPages = withErrorHandling(getTopPages);
export const safeGetTrafficSources = withErrorHandling(getTrafficSources);
export const safeGetDeviceBreakdown = withErrorHandling(getDeviceBreakdown);
export const safeGetRealTimeVisitors = withErrorHandling(getRealTimeVisitors);
