/**
 * Tinybird API 客户端
 * 用于与 Tinybird 实时分析平台交互
 */

/**
 * 检测当前运行环境
 */
export function detectEnvironment(): "local" | "production" {
  // 检查环境变量
  if (process.env.NODE_ENV === "development") {
    return "local";
  }

  // 检查 Tinybird API URL
  const apiUrl = process.env.TINYBIRD_API_URL;
  if (apiUrl?.includes("localhost") || apiUrl?.includes("127.0.0.1")) {
    return "local";
  }

  // 检查特定的本地环境标识
  if (process.env.TINYBIRD_ENVIRONMENT === "local") {
    return "local";
  }

  return "production";
}

/**
 * 根据环境获取正确的 API URL
 */
export function getApiUrl(environment?: "local" | "production"): string {
  const env = environment || detectEnvironment();

  if (env === "local") {
    // 本地开发环境，优先使用环境变量，否则使用默认本地地址
    return process.env.TINYBIRD_API_URL_LOCAL || "http://localhost:7181";
  }

  // 生产环境，使用配置的 API URL 或默认的 Tinybird Cloud
  return process.env.TINYBIRD_API_URL || "https://api.tinybird.co";
}

/**
 * 根据环境获取正确的 Token
 */
export function getToken(environment?: "local" | "production"): string {
  const env = environment || detectEnvironment();

  if (env === "local") {
    // 本地环境优先使用本地 token
    return (
      process.env.TINYBIRD_TOKEN_LOCAL ||
      process.env.TINYBIRD_TOKEN ||
      // 兼容常见本地变量名（CLI 拷贝或用户自定义）
      process.env.TB_LOCAL_TOKEN ||
      process.env.TB_TOKEN ||
      ""
    );
  }

  // 生产环境使用生产 token
  return process.env.TINYBIRD_TOKEN || "";
}

export interface TinybirdConfig {
  apiUrl: string;
  token: string;
  timeout?: number;
  environment?: "local" | "production";
}

export interface EventData {
  id: string;
  website_id: string;
  session_id: string;
  visitor_id: string;
  event_type: "pageview" | "custom" | "payment" | "signup" | "conversion";
  event_name?: string;
  url: string;
  referrer?: string;
  user_agent?: string;
  ip_address?: string;
  country?: string;
  region?: string;
  city?: string;
  device?: string;
  browser?: string;
  os?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_content?: string;
  utm_term?: string;
  custom_data?: string;
  revenue?: number;
  timestamp: string;
}

export interface AnalyticsFilters {
  website_id: string;
  start_date: string;
  end_date: string;
  country?: string;
  device?: string;
  browser?: string;
  utm_source?: string;
}

export interface OverviewMetrics {
  visitors: number;
  pageviews: number;
  bounce_rate: number;
  avg_session_duration: number;
  total_revenue: number;
}

export interface TimeSeriesPoint {
  date: string;
  value: number;
}

export interface TopPage {
  url: string;
  pageviews: number;
  visitors: number;
  bounce_rate: number;
  avg_duration_minutes: number;
}

export interface TrafficSource {
  source: string;
  visitors: number;
  pageviews: number;
  revenue: number;
  conversion_rate: number;
}

export interface EnhancedTrafficSource {
  name: string;
  visitors: number;
  pageviews: number;
  total_revenue: number;
  conversions: number;
  sessions: number;
  conversion_rate: number;
  revenue_per_visitor: number;
  percentage: number;
  utm_source?: string;
  utm_medium?: string;
}

export interface DeviceBreakdown {
  breakdown_type: string;
  category: string;
  visitors: number;
  percentage: number;
}

export interface RealtimeVisitor {
  visitor_id: string;
  url: string;
  country?: string;
  city?: string;
  timezone?: string;
  device?: string;
  browser?: string;
  referrer?: string;
  timestamp: string;
}

export interface CampaignRevenue {
  utm_campaign: string;
  visitors: number;
  revenue: number;
  conversions: number;
  conversion_rate: number;
  revenue_per_visitor: number;
  cost_per_acquisition?: number;
}

export interface ChannelRevenue {
  channel: string;
  visitors: number;
  revenue: number;
  conversions: number;
  conversion_rate: number;
  revenue_per_visitor: number;
  sessions: number;
}

export interface CustomerJourney {
  visitor_id: string;
  journey_steps: Array<{
    step: number;
    url: string;
    timestamp: string;
    event_type: string;
    revenue?: number;
  }>;
  total_revenue: number;
  first_touch_source: string;
  last_touch_source: string;
  conversion_path: string[];
}

export interface CustomerLTV {
  visitor_id: string;
  first_seen: string;
  last_seen: string;
  total_revenue: number;
  total_sessions: number;
  total_pageviews: number;
  avg_session_duration: number;
  days_since_first_visit: number;
  predicted_ltv?: number;
}

export interface GeographicCountry {
  country: string;
  total_events: number;
  unique_visitors: number;
  sessions: number;
  pageviews: number;
  total_revenue: number;
  pageview_rate: number;
  last_activity: string;
  first_activity: string;
  percentage: number;
}

export interface GeographicRegion {
  country: string;
  region: string;
  total_events: number;
  unique_visitors: number;
  sessions: number;
  pageviews: number;
  total_revenue: number;
  pageview_rate: number;
  last_activity: string;
  first_activity: string;
  percentage: number;
}

export interface GeographicCity {
  country: string;
  region: string;
  city: string;
  total_events: number;
  unique_visitors: number;
  sessions: number;
  pageviews: number;
  total_revenue: number;
  pageview_rate: number;
  last_activity: string;
  first_activity: string;
  percentage: number;
}

export interface GeographicOverview {
  total_countries: number;
  total_regions: number;
  total_cities: number;
  total_unique_visitors: number;
  total_events: number;
  total_pageviews: number;
  total_revenue: number;
  top_countries: string[];
  top_countries_visitors: number[];
}

export class TinybirdClient {
  private config: TinybirdConfig;

  constructor(config: TinybirdConfig) {
    this.config = {
      timeout: 10000,
      ...config,
    };
  }

  /**
   * 发送事件到 Tinybird Events API
   */
  async sendEvent(event: EventData): Promise<void> {
    try {
      const response = await fetch(
        `${this.config.apiUrl}/v0/events?name=events`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${this.config.token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(event),
          signal: AbortSignal.timeout(this.config.timeout!),
        }
      );

      if (!response.ok) {
        throw new Error(
          `Tinybird Events API error: ${response.status} ${response.statusText}`
        );
      }
    } catch (error) {
      console.error("Failed to send event to Tinybird:", error);
      throw error;
    }
  }

  /**
   * 批量发送事件
   */
  async sendEvents(events: EventData[]): Promise<void> {
    try {
      const ndjsonData = events
        .map((event) => JSON.stringify(event))
        .join("\n");

      const response = await fetch(
        `${this.config.apiUrl}/v0/events?name=events`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${this.config.token}`,
            "Content-Type": "application/json",
          },
          body: ndjsonData,
          signal: AbortSignal.timeout(this.config.timeout!),
        }
      );

      if (!response.ok) {
        throw new Error(
          `Tinybird Events API error: ${response.status} ${response.statusText}`
        );
      }
    } catch (error) {
      console.error("Failed to send events to Tinybird:", error);
      throw error;
    }
  }

  /**
   * 获取概览指标
   * the filters includes:
   * - website_id
   * - start_date
   * - end_date
   * - country
   * - device
   * - browser
   * - utm_source
   */
  async getOverviewMetrics(
    filters: AnalyticsFilters
  ): Promise<OverviewMetrics> {
    const data = await this.queryEndpoint("overview_metrics", filters);
    if (Array.isArray(data)) {
      const first = data[0];
      if (first) return first as OverviewMetrics;
      const empty: OverviewMetrics = {
        visitors: 0,
        pageviews: 0,
        bounce_rate: 0,
        avg_session_duration: 0,
        total_revenue: 0,
      };
      return empty;
    }
    return (
      (data as OverviewMetrics) || {
        visitors: 0,
        pageviews: 0,
        bounce_rate: 0,
        avg_session_duration: 0,
        total_revenue: 0,
      }
    );
  }

  /**
   * 获取时间序列数据
   */
  async getTimeSeriesData(
    filters: AnalyticsFilters,
    metric: "visitors" | "pageviews" | "revenue",
    interval: "hour" | "day" | "week" | "month" = "day"
  ): Promise<TimeSeriesPoint[]> {
    return this.queryEndpoint("timeseries_data", {
      ...filters,
      metric,
      interval,
    });
  }

  /**
   * 获取热门页面
   */
  async getTopPages(filters: AnalyticsFilters, limit = 10): Promise<TopPage[]> {
    return this.queryEndpoint("top_pages", {
      ...filters,
      limit,
    });
  }

  /**
   * 获取流量来源
   */
  async getTrafficSources(
    filters: AnalyticsFilters,
    limit = 10
  ): Promise<TrafficSource[]> {
    return this.queryEndpoint("traffic_sources", {
      ...filters,
      limit,
    });
  }

  /**
   * 获取增强的流量来源数据 (Channels, Sources, Campaigns, UTM Sources, UTM Mediums, UTM Contents, UTM Terms)
   */
  async getEnhancedTrafficSources(
    filters: AnalyticsFilters,
    category:
      | "channels"
      | "sources"
      | "campaigns"
      | "utm_sources"
      | "utm_mediums"
      | "utm_contents"
      | "utm_terms",
    limit = 10
  ): Promise<EnhancedTrafficSource[]> {
    return this.queryEndpoint("enhanced_traffic_sources", {
      ...filters,
      category,
      limit,
    });
  }

  /**
   * 获取设备/浏览器分析
   */
  async getDeviceBreakdown(
    filters: AnalyticsFilters,
    breakdownType: "device" | "browser" | "os" = "device"
  ): Promise<DeviceBreakdown[]> {
    return this.queryEndpoint("device_breakdown", {
      ...filters,
      breakdown_type: breakdownType,
    });
  }

  /**
   * 获取UTM分析数据 (UTM Sources, UTM Mediums, UTM Campaigns, UTM Contents, UTM Terms)
   */
  async getUTMAnalytics(
    filters: AnalyticsFilters,
    category:
      | "utm_sources"
      | "utm_mediums"
      | "utm_campaigns"
      | "utm_contents"
      | "utm_terms",
    limit = 10
  ): Promise<EnhancedTrafficSource[]> {
    return this.queryEndpoint("utm_analytics", {
      website_id: filters.website_id,
      start_date: filters.start_date,
      end_date: filters.end_date,
      category,
      limit,
    });
  }

  /**
   * 获取实时访客
   */
  async getRealtimeVisitors(
    websiteId: string,
    limit = 50
  ): Promise<RealtimeVisitor[]> {
    return this.queryEndpoint("realtime_visitors", {
      website_id: websiteId,
      limit,
    });
  }

  /**
   * 获取营销活动收入归因
   */
  async getCampaignRevenue(
    filters: AnalyticsFilters,
    limit = 10
  ): Promise<CampaignRevenue[]> {
    return this.queryEndpoint("campaign_revenue", {
      ...filters,
      limit,
    });
  }

  /**
   * 获取营销渠道收入归因
   */
  async getChannelRevenue(
    filters: AnalyticsFilters,
    limit = 10
  ): Promise<ChannelRevenue[]> {
    return this.queryEndpoint("channel_revenue", {
      ...filters,
      limit,
    });
  }

  /**
   * 获取客户旅程分析
   */
  async getCustomerJourney(
    filters: AnalyticsFilters,
    limit = 10
  ): Promise<CustomerJourney[]> {
    return this.queryEndpoint("customer_journey", {
      ...filters,
      limit,
    });
  }

  /**
   * 获取客户生命周期价值分析
   */
  async getCustomerLTV(
    filters: AnalyticsFilters,
    limit = 100
  ): Promise<CustomerLTV[]> {
    return this.queryEndpoint("customer_ltv", {
      ...filters,
      limit,
    });
  }

  /**
   * 获取收入漏斗分析
   */
  async getRevenueFunnel(filters: AnalyticsFilters): Promise<
    Array<{
      step: string;
      visitors: number;
      conversion_rate: number;
      revenue: number;
      drop_off_rate: number;
    }>
  > {
    return this.queryEndpoint("revenue_funnel", filters);
  }

  /**
   * 获取付费客户分析
   */
  async getPayingCustomers(filters: AnalyticsFilters): Promise<{
    total_customers: number;
    new_customers: number;
    returning_customers: number;
    average_order_value: number;
    total_revenue: number;
    customer_retention_rate: number;
  }> {
    const result = await this.queryEndpoint("paying_customers", filters);
    return result[0] || {};
  }

  /**
   * 获取地理位置概览数据
   */
  async getGeographicOverview(
    filters: AnalyticsFilters
  ): Promise<GeographicOverview> {
    const result = await this.queryEndpoint("geographic_overview", filters);
    return (
      result[0] || {
        total_countries: 0,
        total_regions: 0,
        total_cities: 0,
        total_unique_visitors: 0,
        total_events: 0,
        total_pageviews: 0,
        total_revenue: 0,
        top_countries: [],
        top_countries_visitors: [],
      }
    );
  }

  /**
   * 获取国家级地理位置数据
   */
  async getGeographicCountries(
    filters: AnalyticsFilters,
    limit = 50
  ): Promise<GeographicCountry[]> {
    return this.queryEndpoint("geographic_countries", {
      ...filters,
      limit,
    });
  }

  /**
   * 获取地区级地理位置数据
   */
  async getGeographicRegions(
    filters: AnalyticsFilters,
    limit = 50
  ): Promise<GeographicRegion[]> {
    return this.queryEndpoint("geographic_regions", {
      ...filters,
      limit,
    });
  }

  /**
   * 获取城市级地理位置数据
   */
  async getGeographicCities(
    filters: AnalyticsFilters,
    limit = 50
  ): Promise<GeographicCity[]> {
    return this.queryEndpoint("geographic_cities", {
      ...filters,
      limit,
    });
  }

  /**
   * 获取实时地理位置数据
   */
  async getRealtimeLocations(
    websiteId: string,
    limit = 50
  ): Promise<
    Array<{
      country: string;
      city: string;
      visitor_count: number;
      unique_visitors: number;
      last_seen: string;
    }>
  > {
    return this.queryEndpoint("realtime_locations", {
      website_id: websiteId,
      limit,
    });
  }

  /**
   * 通用端点查询方法
   */
  async queryEndpoint(
    endpoint: string,
    params: Record<string, any>
  ): Promise<any> {
    try {
      const url = new URL(`${this.config.apiUrl}/v0/pipes/${endpoint}.json`);

      // 添加查询参数
      for (const [key, value] of Object.entries(params)) {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      }

      // Tinybird Local(7181) 通常允许/偏好使用 URL 上的 token 查询参数
      // 为增强兼容性：在本地环境将 token 追加到查询参数（与 Authorization 头并存）
      if (
        this.config.apiUrl.includes("localhost") ||
        this.config.environment === "local"
      ) {
        if (!url.searchParams.has("token")) {
          url.searchParams.set("token", this.config.token);
        }
      }

      const response = await fetch(url.toString(), {
        method: "GET",
        headers: {
          Authorization: `Bearer ${this.config.token}`,
        },
        signal: AbortSignal.timeout(this.config.timeout!),
      });

      if (!response.ok) {
        console.error(
          `Tinybird API error: ${response.status} ${response.statusText}`,
          url.toString()
        );
        throw new Error(
          `Tinybird API error: ${response.status} ${response.statusText}`
        );
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error(`Failed to query Tinybird endpoint ${endpoint}:`, error);
      throw error;
    }
  }
}

/**
 * 创建 Tinybird 客户端实例
 */
export function createTinybirdClient(
  environment?: "local" | "production"
): TinybirdClient {
  const env = environment || detectEnvironment();
  const apiUrl = getApiUrl(env);
  const token = getToken(env);
  console.log(token, "token");

  const config: TinybirdConfig = {
    apiUrl,
    token,
    timeout: Number.parseInt(process.env.TINYBIRD_TIMEOUT || "10000"),
    environment: env,
  };

  console.log("config", config);

  if (!config.token) {
    console.log("token is missing");
    const checked =
      env === "local"
        ? "TINYBIRD_TOKEN_LOCAL | TINYBIRD_TOKEN | TB_LOCAL_TOKEN | TB_TOKEN"
        : "TINYBIRD_TOKEN";
    const message = [
      `Tinybird token missing for ${env}. Please set one of: ${checked}.`,
      "Local quickstart:",
      '  tb token copy "<your user>" && echo "TINYBIRD_TOKEN_LOCAL=$(pbpaste)" >> .env.local',
    ].join("\n");
    throw new Error(message);
  }

  console.log(
    `🚀 Tinybird Client initialized for ${env} environment: ${apiUrl}`
  );

  return new TinybirdClient(config);
}

// 懒加载全局客户端实例
let _tinybirdClient: TinybirdClient | null = null;

export const tinybirdClient = new Proxy({} as TinybirdClient, {
  get(target, prop) {
    if (!_tinybirdClient) {
      _tinybirdClient = createTinybirdClient();
    }
    return (_tinybirdClient as any)[prop];
  },
});
