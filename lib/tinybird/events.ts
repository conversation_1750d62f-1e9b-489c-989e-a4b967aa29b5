/**
 * Tinybird 事件处理工具
 * 处理事件数据的格式化、验证和发送
 */

import { v4 as uuidv4 } from "uuid"
import { type EventData, tinybirdClient } from "./client"

export interface TrackingEventInput {
  websiteId: string
  sessionId: string
  visitorId: string
  eventType?: "pageview" | "custom" | "payment" | "signup" | "conversion"
  eventName?: string
  url: string
  referrer?: string
  userAgent?: string
  country?: string
  region?: string
  city?: string
  device?: string
  browser?: string
  os?: string
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
  utm_content?: string
  utm_term?: string
  customData?: Record<string, any>
  revenue?: number
  timestamp?: Date
}

/**
 * 格式化事件数据以发送到 Tinybird
 */
export function formatEventForTinybird(input: TrackingEventInput): EventData {
  return {
    id: uuidv4(),
    website_id: input.websiteId,
    session_id: input.sessionId,
    visitor_id: input.visitorId,
    event_type: input.eventType || "pageview",
    event_name: input.eventName || undefined,
    url: input.url,
    referrer: input.referrer || undefined,
    user_agent: input.userAgent || undefined,
    ip_address: undefined, // IP 将由服务器端设置
    country: input.country || undefined,
    region: input.region || undefined,
    city: input.city || undefined,
    device: input.device || undefined,
    browser: input.browser || undefined,
    os: input.os || undefined,
    utm_source: input.utm_source || undefined,
    utm_medium: input.utm_medium || undefined,
    utm_campaign: input.utm_campaign || undefined,
    utm_content: input.utm_content || undefined,
    utm_term: input.utm_term || undefined,
    custom_data: input.customData ? JSON.stringify(input.customData) : undefined,
    revenue: input.revenue || undefined,
    timestamp: (input.timestamp || new Date()).toISOString(),
  }
}

/**
 * 发送事件到 Tinybird
 */
export async function trackEvent(input: TrackingEventInput): Promise<void> {
  const eventData = formatEventForTinybird(input)

  try {
    await tinybirdClient.sendEvent(eventData)
  } catch (error) {
    console.error("Failed to track event:", error)
    // 在生产环境中，可以考虑将失败的事件存储到本地队列中重试
    throw error
  }
}

/**
 * 批量发送事件到 Tinybird
 */
export async function trackEvents(inputs: TrackingEventInput[]): Promise<void> {
  const eventsData = inputs.map(formatEventForTinybird)

  try {
    await tinybirdClient.sendEvents(eventsData)
  } catch (error) {
    console.error("Failed to track events:", error)
    throw error
  }
}

/**
 * 事件队列管理器
 * 用于缓存和批量发送事件，提高性能
 */
export class EventQueue {
  private queue: EventData[] = []
  private batchSize: number
  private flushInterval: number
  private timer: NodeJS.Timeout | null = null

  constructor(batchSize = 10, flushIntervalMs = 5000) {
    this.batchSize = batchSize
    this.flushInterval = flushIntervalMs
    this.startFlushTimer()
  }

  /**
   * 添加事件到队列
   */
  enqueue(input: TrackingEventInput): void {
    const eventData = formatEventForTinybird(input)
    this.queue.push(eventData)

    // 如果队列达到批量大小，立即发送
    if (this.queue.length >= this.batchSize) {
      this.flush()
    }
  }

  /**
   * 发送队列中的所有事件
   */
  async flush(): Promise<void> {
    if (this.queue.length === 0) return

    const eventsToSend = [...this.queue]
    this.queue = []

    try {
      await tinybirdClient.sendEvents(eventsToSend)
    } catch (error) {
      console.error("Failed to flush event queue:", error)
      // 将失败的事件重新加入队列（可选）
      this.queue.unshift(...eventsToSend)
      throw error
    }
  }

  /**
   * 启动定期刷新定时器
   */
  private startFlushTimer(): void {
    this.timer = setInterval(() => {
      this.flush().catch(console.error)
    }, this.flushInterval)
  }

  /**
   * 停止队列并发送剩余事件
   */
  async stop(): Promise<void> {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    await this.flush()
  }
}

// 全局事件队列实例
export const eventQueue = new EventQueue()

/**
 * 使用队列发送事件
 */
export function queueEvent(input: TrackingEventInput): void {
  eventQueue.enqueue(input)
}

/**
 * 优雅关闭时清空队列
 */
process.on("SIGTERM", async () => {
  await eventQueue.stop()
})

process.on("SIGINT", async () => {
  await eventQueue.stop()
})
