/**
 * Enhanced Tinybird Analytics
 * Provides comprehensive traffic source analysis using server-side API calls
 */

import type {
  ExtendedAnalyticsFilters,
  EnhancedTrafficSource,
} from "@/lib/analytics/enhanced";

/**
 * Get enhanced traffic sources from Tinybird with detailed breakdown
 * This function should only be called server-side
 */
export async function getEnhancedTrafficSourcesFromTinybird(
  filters: ExtendedAnalyticsFilters,
  category:
    | "channels"
    | "sources"
    | "campaigns"
    | "utm_sources"
    | "utm_mediums"
    | "utm_contents"
    | "utm_terms",
  limit = 10
): Promise<EnhancedTrafficSource[]> {
  // Check if we're running on the server
  if (typeof window !== "undefined") {
    throw new Error(
      "Enhanced Tinybird analytics should only be called server-side"
    );
  }

  try {
    // Import the client only on server-side
    const { tinybirdClient } = await import("./client");

    // Convert ExtendedAnalyticsFilters to AnalyticsFilters
    const analyticsFilters = {
      website_id: filters.websiteId,
      start_date: filters.dateRange.from.toISOString().split("T")[0],
      end_date: filters.dateRange.to.toISOString().split("T")[0],
    };

    // Use the new enhanced traffic sources endpoint
    const data = await tinybirdClient.getEnhancedTrafficSources(
      analyticsFilters,
      category,
      limit
    );

    if (!data || data.length === 0) {
      return [];
    }

    // The endpoint already returns the data in the correct format
    return data.map((row: any) => ({
      name: row.name || "Unknown",
      visitors: row.visitors || 0,
      pageviews: row.pageviews || 0,
      total_revenue: row.total_revenue || 0,
      conversions: row.conversions || 0,
      sessions: row.sessions || 0,
      conversion_rate: row.conversion_rate || 0,
      revenue_per_visitor: row.revenue_per_visitor || 0,
      percentage: row.percentage || 0,
      utm_source: row.utm_source || "",
      utm_medium: row.utm_medium || "",
      utm_campaign: row.utm_campaign || "",
      utm_content: row.utm_content || "",
      utm_term: row.utm_term || "",
    }));
  } catch (error) {
    console.error("Enhanced Tinybird query error:", error);
    throw error;
  }
}
