"use client";

import { useEffect, useRef, useState } from "react";

interface RealtimeVisitor {
  visitor_id: string;
  url: string;
  country?: string;
  city?: string;
  device?: string;
  browser?: string;
  referrer?: string;
  timestamp: string;
}

interface Overview {
  visitors?: number;
  pageviews?: number;
  bounce_rate?: number;
  avg_session_duration?: number;
  total_revenue?: number;
}

interface RealtimeData {
  timestamp: string;
  realtimeVisitors: RealtimeVisitor[];
  overview: Overview;
  onlineCount: number;
}

interface UseRealtimeDataProps {
  websiteId: string;
  enabled?: boolean;
}

export function useRealtimeData({
  websiteId,
  enabled = true,
}: UseRealtimeDataProps) {
  const [data, setData] = useState<RealtimeData | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const timerRef = useRef<number | null>(null);
  const defaultInterval = 50000; // 50s fallback，与服务端默认一致

  useEffect(() => {
    if (!enabled || !websiteId) {
      return;
    }

    const fetchOnce = async () => {
      try {
        const res = await fetch(
          `/api/stream?websiteId=${encodeURIComponent(websiteId)}`,
          {
            method: "GET",
            headers: { Accept: "application/json" },
            cache: "no-store",
          }
        );
        if (!res.ok) {
          throw new Error(`Realtime API ${res.status}`);
        }
        const json = (await res.json()) as RealtimeData & {
          refreshIntervalMs?: number;
        };
        if ((json as any).error) {
          setError((json as any).error);
          setIsConnected(false);
        } else {
          setData(json);
          setError(null);
          setIsConnected(true);
        }
        const next = Number.parseInt(
          String((json as any).refreshIntervalMs || defaultInterval)
        );
        schedule(next);
      } catch (err) {
        console.error("Realtime fetch error:", err);
        setError("Connection to real-time data failed");
        setIsConnected(false);
        schedule(defaultInterval);
      }
    };

    const schedule = (ms: number) => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      timerRef.current = window.setTimeout(fetchOnce, Math.max(1000, ms));
    };

    // initial
    fetchOnce();

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      setIsConnected(false);
    };
  }, [websiteId, enabled]);

  const reconnect = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    setError(null);
    setIsConnected(false);
    // trigger immediate refresh
    void (async () => {
      try {
        const res = await fetch(
          `/api/stream?websiteId=${encodeURIComponent(websiteId)}`,
          {
            method: "GET",
            headers: { Accept: "application/json" },
            cache: "no-store",
          }
        );
        const json = (await res.json()) as RealtimeData & {
          refreshIntervalMs?: number;
        };
        setData(json);
        setIsConnected(true);
        const next = Number.parseInt(
          String((json as any).refreshIntervalMs || defaultInterval)
        );
        timerRef.current = window.setTimeout(
          () => reconnect(),
          Math.max(1000, next)
        );
      } catch (err) {
        console.error("Realtime fetch error:", err);
        setError("Connection to real-time data failed");
      }
    })();
  };

  return {
    data,
    isConnected,
    error,
    reconnect,
    visitors: data?.realtimeVisitors || [],
    onlineCount: data?.onlineCount || 0,
    overview: data?.overview || {},
    lastUpdate: data?.timestamp,
  };
}
