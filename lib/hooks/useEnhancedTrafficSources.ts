"use client";

import { useState, useEffect } from "react";
import type { EnhancedTrafficSource } from "@/lib/analytics/enhanced";

interface UseEnhancedTrafficSourcesProps {
  websiteId: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  category:
    | "channels"
    | "sources"
    | "campaigns"
    | "utm_sources"
    | "utm_mediums"
    | "utm_contents"
    | "utm_terms";
  limit?: number;
}

interface UseEnhancedTrafficSourcesReturn {
  data: EnhancedTrafficSource[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useEnhancedTrafficSources({
  websiteId,
  dateRange,
  category,
  limit = 10,
}: UseEnhancedTrafficSourcesProps): UseEnhancedTrafficSourcesReturn {
  const [data, setData] = useState<EnhancedTrafficSource[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build API URL with query parameters
      const params = new URLSearchParams({
        category,
        limit: limit?.toString() || "10",
        from: dateRange.from.toISOString(),
        to: dateRange.to.toISOString(),
      });

      const response = await fetch(
        `/api/analytics/enhanced-traffic-sources/${websiteId}?${params}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setData(result.data || []);
    } catch (err) {
      console.error("Failed to fetch enhanced traffic sources:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch data");

      // Set empty data so component can show "no data" placeholder
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (websiteId && dateRange.from && dateRange.to) {
      fetchData();
    }
  }, [websiteId, dateRange.from, dateRange.to, category, limit]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
}
