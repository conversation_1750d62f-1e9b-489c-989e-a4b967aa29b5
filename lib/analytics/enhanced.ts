/**
 * Enhanced Analytics Service
 * Uses Tinybird for comprehensive analytics with enhanced traffic source analysis
 */

// Import types from Tinybird analytics
import type {
  DateRange as TinybirdDateRange,
  ExtendedAnalyticsFilters as TinybirdExtendedAnalyticsFilters,
} from "@/lib/tinybird/analytics";

// Common interfaces
export interface DateRange {
  from: Date;
  to: Date;
}

export interface ExtendedAnalyticsFilters {
  websiteId: string;
  dateRange: DateRange;
  country?: string;
  device?: string;
  browser?: string;
  utm_source?: string;
}

// Re-export common types from Tinybird client
export type {
  OverviewMetrics as WebsiteOverview,
  TimeSeriesPoint as TimeSeriesData,
  TrafficSource,
  TopPage,
  DeviceBreakdown,
} from "@/lib/tinybird/client";

// Enhanced traffic source interface for detailed analysis
export interface EnhancedTrafficSource {
  name: string;
  visitors: number;
  pageviews: number;
  total_revenue: number;
  conversions: number;
  sessions: number;
  conversion_rate: number;
  revenue_per_visitor: number;
  percentage: number;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_content?: string;
  utm_term?: string;
}

/**
 * Get default date range (last 30 days)
 */
export function getDefaultDateRange(): DateRange {
  const to = new Date();
  const from = new Date();
  from.setDate(from.getDate() - 30);
  return { from, to };
}

/**
 * Convert enhanced analytics filters to Tinybird analytics filters
 */
function convertToTinybirdFilters(
  filters: ExtendedAnalyticsFilters
): TinybirdExtendedAnalyticsFilters {
  return {
    website_id: filters.websiteId,
    dateRange: filters.dateRange,
    country: filters.country,
    device: filters.device,
    browser: filters.browser,
    utm_source: filters.utm_source,
    start_date: "", // Will be filled by Tinybird's convertFilters
    end_date: "", // Will be filled by Tinybird's convertFilters
  };
}

/**
 * Create analytics filters (for backward compatibility)
 */
export function createAnalyticsFilters(params: {
  websiteId: string;
  dateRange?: DateRange;
  country?: string;
  device?: string;
  browser?: string;
  utmSource?: string;
}): ExtendedAnalyticsFilters {
  return {
    websiteId: params.websiteId,
    dateRange: params.dateRange || getDefaultDateRange(),
    country: params.country,
    device: params.device,
    browser: params.browser,
    utm_source: params.utmSource,
  };
}

/**
 * Get website overview metrics
 */
export async function getWebsiteOverview(filters: ExtendedAnalyticsFilters) {
  const { getWebsiteOverview: getTinybirdOverview } = await import(
    "@/lib/tinybird/analytics"
  );

  const tinybirdFilters = convertToTinybirdFilters(filters);
  return await getTinybirdOverview(tinybirdFilters);
}

/**
 * Get time series data for charts
 */
export async function getTimeSeriesData(
  filters: ExtendedAnalyticsFilters,
  metric: "visitors" | "pageviews" | "revenue", // Remove "sessions" as it's not supported
  granularity: "hour" | "day" | "week" | "month"
) {
  const { getTimeSeriesData: getTinybirdTimeSeries } = await import(
    "@/lib/tinybird/analytics"
  );

  const tinybirdFilters = convertToTinybirdFilters(filters);
  return await getTinybirdTimeSeries(tinybirdFilters, metric, granularity);
}

/**
 * Get traffic sources
 */
export async function getTrafficSources(
  filters: ExtendedAnalyticsFilters,
  limit = 10
) {
  const { getTrafficSources: getTinybirdSources } = await import(
    "@/lib/tinybird/analytics"
  );

  const tinybirdFilters = convertToTinybirdFilters(filters);
  return await getTinybirdSources(tinybirdFilters, limit);
}

/**
 * Get enhanced traffic sources with detailed breakdown
 * Uses advanced Tinybird queries for comprehensive traffic source analysis
 */
export async function getEnhancedTrafficSources(
  filters: ExtendedAnalyticsFilters,
  category:
    | "channels"
    | "sources"
    | "campaigns"
    | "utm_sources"
    | "utm_mediums"
    | "utm_contents"
    | "utm_terms",
  limit = 10
): Promise<EnhancedTrafficSource[]> {
  const tinybirdFilters = convertToTinybirdFilters(filters);

  try {
    // Import enhanced Tinybird functions
    const { getEnhancedTrafficSourcesFromTinybird } = await import(
      "@/lib/tinybird/enhanced-analytics"
    );

    // Use enhanced Tinybird queries for detailed breakdown
    return await getEnhancedTrafficSourcesFromTinybird(
      filters, // Use original filters since the function expects our ExtendedAnalyticsFilters
      category,
      limit
    );
  } catch (error) {
    console.error("Enhanced traffic sources error:", error);

    // Fallback to regular traffic sources with enhancement
    const { getTrafficSources: getTinybirdSources } = await import(
      "@/lib/tinybird/analytics"
    );
    const sources = await getTinybirdSources(tinybirdFilters, limit);

    // Convert to enhanced format with calculated metrics
    const totalVisitors = sources.reduce(
      (sum, source) => sum + source.visitors,
      0
    );

    return sources.map((source) => ({
      name: source.source,
      visitors: source.visitors,
      pageviews: source.pageviews,
      total_revenue: source.revenue,
      conversions: Math.round(source.visitors * (source.conversion_rate / 100)),
      sessions: Math.round(source.visitors * 1.2), // Estimate sessions from visitors
      conversion_rate: source.conversion_rate,
      revenue_per_visitor:
        source.visitors > 0 ? source.revenue / source.visitors : 0,
      percentage:
        totalVisitors > 0 ? (source.visitors / totalVisitors) * 100 : 0,
      utm_source: category === "sources" ? source.source : undefined,
      utm_medium: category === "channels" ? source.source : undefined,
      utm_campaign: category === "campaigns" ? source.source : undefined,
    }));
  }
}

/**
 * Get top pages
 */
export async function getTopPages(
  filters: ExtendedAnalyticsFilters,
  limit = 10
) {
  const { getTopPages: getTinybirdPages } = await import(
    "@/lib/tinybird/analytics"
  );

  const tinybirdFilters = convertToTinybirdFilters(filters);
  return await getTinybirdPages(tinybirdFilters, limit);
}

/**
 * Get device breakdown
 */
export async function getDeviceBreakdown(filters: ExtendedAnalyticsFilters) {
  const { getDeviceBreakdown: getTinybirdDevices } = await import(
    "@/lib/tinybird/analytics"
  );

  const tinybirdFilters = convertToTinybirdFilters(filters);
  return await getTinybirdDevices(tinybirdFilters);
}

/**
 * Get real-time visitors (uses Convex)
 */
export async function getRealTimeVisitors(websiteId: string) {
  const { getRealTimeVisitors: getConvexRealTime } = await import(
    "@/lib/tinybird/analytics"
  );
  return await getConvexRealTime(websiteId);
}

/**
 * Get analytics provider info
 */
export function getAnalyticsProviderInfo() {
  return {
    provider: "tinybird",
    supportsEnhancedSources: true,
    supportsRealTime: true, // Via Convex
    requiresUserId: false,
  };
}

/**
 * Health check for analytics system
 */
export async function healthCheck() {
  try {
    return {
      status: "healthy",
      provider: "tinybird",
      convex: "enabled",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: "unhealthy",
      provider: "tinybird",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    };
  }
}
