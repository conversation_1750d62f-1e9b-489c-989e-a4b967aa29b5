import { createC<PERSON><PERSON><PERSON>, createDecipheriv, randomBytes } from "crypto";

/**
 * PaymentCredentialsVault - Secure encryption/decryption for payment provider credentials
 * Uses AES-256-GCM for authenticated encryption
 */
export class PaymentCredentialsVault {
  private static readonly ALGORITHM = "aes-256-gcm";
  private static readonly KEY_LENGTH = 32;
  private static readonly IV_LENGTH = 16;
  private static readonly TAG_LENGTH = 16;

  private static getEncryptionKey(): Buffer {
    const key = process.env.PAYMENT_CREDENTIALS_ENCRYPTION_KEY;
    if (!key) {
      throw new Error(
        "PAYMENT_CREDENTIALS_ENCRYPTION_KEY environment variable is required"
      );
    }

    // Ensure key is exactly 32 bytes for AES-256
    if (key.length !== 64) {
      // 32 bytes = 64 hex chars
      throw new Error(
        "PAYMENT_CREDENTIALS_ENCRYPTION_KEY must be 64 hex characters (32 bytes)"
      );
    }

    return Buffer.from(key, "hex");
  }

  /**
   * Encrypt a plaintext credential
   * @param plaintext The credential to encrypt
   * @returns Base64 encoded encrypted data (iv + tag + encrypted)
   */
  static encrypt(plaintext: string): string {
    try {
      const key = this.getEncryptionKey();
      const iv = randomBytes(this.IV_LENGTH);
      const cipher = createCipheriv(this.ALGORITHM, key, iv);

      let encrypted = cipher.update(plaintext, "utf8", "hex");
      encrypted += cipher.final("hex");

      const tag = cipher.getAuthTag();

      // Combine iv + tag + encrypted data
      const combined = Buffer.concat([iv, tag, Buffer.from(encrypted, "hex")]);
      return combined.toString("base64");
    } catch (error) {
      console.error("Encryption failed:", error);
      throw new Error("Failed to encrypt credential");
    }
  }

  /**
   * Decrypt an encrypted credential
   * @param encryptedData Base64 encoded encrypted data
   * @returns Decrypted plaintext credential
   */
  static decrypt(encryptedData: string): string {
    try {
      const key = this.getEncryptionKey();
      const combined = Buffer.from(encryptedData, "base64");

      if (combined.length < this.IV_LENGTH + this.TAG_LENGTH) {
        throw new Error("Invalid encrypted data format");
      }

      const iv = combined.subarray(0, this.IV_LENGTH);
      const tag = combined.subarray(
        this.IV_LENGTH,
        this.IV_LENGTH + this.TAG_LENGTH
      );
      const encrypted = combined.subarray(this.IV_LENGTH + this.TAG_LENGTH);

      const decipher = createDecipheriv(this.ALGORITHM, key, iv);
      decipher.setAuthTag(tag);

      let decrypted = decipher.update(encrypted, undefined, "utf8");
      decrypted += decipher.final("utf8");

      return decrypted;
    } catch (error) {
      console.error("Decryption failed:", error);
      throw new Error("Failed to decrypt credential");
    }
  }

  /**
   * Generate a secure encryption key for environment variable
   * @returns 64-character hex string (32 bytes)
   */
  static generateEncryptionKey(): string {
    return randomBytes(this.KEY_LENGTH).toString("hex");
  }

  /**
   * Get the first few characters of a credential for identification
   * @param credential The credential to get prefix from
   * @param length Number of characters to include (default: 6)
   * @returns Prefix string for identification
   */
  static getCredentialPrefix(credential: string, length: number = 6): string {
    if (!credential || credential.length < length) {
      return credential || "";
    }
    return credential.substring(0, length);
  }

  /**
   * Validate that the encryption key is properly configured
   * @returns true if key is valid, throws error otherwise
   */
  static validateEncryptionKey(): boolean {
    try {
      const key = this.getEncryptionKey();

      // Test encryption/decryption with a sample value
      const testValue = "test_credential_validation";
      const encrypted = this.encrypt(testValue);
      const decrypted = this.decrypt(encrypted);

      if (decrypted !== testValue) {
        throw new Error("Encryption key validation failed");
      }

      return true;
    } catch (error) {
      console.error("Encryption key validation failed:", error);
      throw error;
    }
  }
}

/**
 * Security utilities for payment credentials
 */
export class PaymentCredentialsSecurity {
  static readonly CONFIG = {
    encryptionAtRest: true,
    keyRotationDays: 90,
    auditLogging: true,
    accessLogging: true,
  };

  /**
   * Sanitize credential data for logging (removes sensitive values)
   */
  static sanitizeForLogging(credential: any): any {
    return {
      ...credential,
      encryptedValue: "[REDACTED]",
      keyPrefix: credential.keyPrefix || "[REDACTED]",
    };
  }

  /**
   * Validate credential format based on provider and type
   */
  static validateCredentialFormat(
    provider: string,
    credentialType: string,
    value: string
  ): boolean {
    const patterns: Record<string, Record<string, RegExp>> = {
      stripe: {
        secret_key: /^sk_(test_|live_)[a-zA-Z0-9]{24,}$/,
        publishable_key: /^pk_(test_|live_)[a-zA-Z0-9]{24,}$/,
        webhook_secret: /^whsec_[a-zA-Z0-9]{32,}$/,
      },
      paypal: {
        client_id: /^[A-Za-z0-9_-]{80}$/,
        client_secret: /^[A-Za-z0-9_-]{80}$/,
      },
      lemonsqueezy: {
        api_key: /^lmsq_api_[a-f0-9]{40}$/,
        webhook_secret: /^[a-f0-9]{64}$/,
      },
      polar: {
        access_token: /^polar_[a-zA-Z0-9]{40,}$/,
        webhook_secret: /^[a-f0-9]{64}$/,
      },
    };

    const providerPatterns = patterns[provider];
    if (!providerPatterns) {
      return true; // Allow unknown providers
    }

    const pattern = providerPatterns[credentialType];
    return pattern ? pattern.test(value) : true; // Allow unknown credential types
  }

  /**
   * Generate audit log entry for credential operations
   */
  static generateAuditLog(
    operation: string,
    userId: string,
    provider: string,
    credentialType: string
  ): any {
    return {
      timestamp: new Date().toISOString(),
      operation,
      userId,
      provider,
      credentialType,
      ip: "server", // Would be actual IP in real implementation
      userAgent: "server", // Would be actual user agent in real implementation
    };
  }

  /**
   * Check if credential needs rotation based on age
   */
  static needsRotation(lastValidated: Date | null): boolean {
    if (!lastValidated) return true;

    const rotationInterval = this.CONFIG.keyRotationDays * 24 * 60 * 60 * 1000;
    const now = new Date().getTime();
    const lastValidatedTime = lastValidated.getTime();

    return now - lastValidatedTime > rotationInterval;
  }

  /**
   * Generate a secure random string for webhook secrets
   */
  static generateWebhookSecret(length: number = 64): string {
    return randomBytes(length / 2).toString("hex");
  }
}
