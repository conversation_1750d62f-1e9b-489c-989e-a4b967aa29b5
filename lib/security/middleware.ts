import crypto from "node:crypto";
// Security middleware for API routes
import { NextRequest, NextResponse } from "next/server";
import { validateAndParseIP, validateRateLimit } from "./validation";

interface SecurityConfig {
  rateLimiting?: {
    windowMs: number;
    maxRequests: number;
  };
  cors?: {
    origins: readonly string[];
    methods: readonly string[];
    headers: readonly string[];
  };
  contentSecurityPolicy?: boolean;
  blockSuspiciousIPs?: boolean;
}

// Default security configuration
const DEFAULT_CONFIG: SecurityConfig = {
  rateLimiting: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
  },
  cors: {
    origins: ["*"], // In production, specify exact origins
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    headers: ["Content-Type", "Authorization", "X-Requested-With"],
  },
  contentSecurityPolicy: true,
  blockSuspiciousIPs: true,
};

// Suspicious IP patterns (basic implementation)
const SUSPICIOUS_IP_PATTERNS = [
  /^0\./, // Reserved
  /^127\./, // Loopback (except for development)
  /^169\.254\./, // Link-local
  /^192\.0\.2\./, // TEST-NET
  /^224\./, // Multicast
];

// Known malicious user agents
const MALICIOUS_USER_AGENTS = [
  /sqlmap/i,
  /nikto/i,
  /nessus/i,
  /burpsuite/i,
  /acunetix/i,
  /w3af/i,
  /masscan/i,
  /nmap/i,
];

// Security middleware function
export function withSecurity(config: Partial<SecurityConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  return function securityMiddleware(
    handler: (req: NextRequest) => Promise<NextResponse> | NextResponse
  ) {
    return async (req: NextRequest): Promise<NextResponse> => {
      try {
        // 1. IP Validation and Blocking
        const clientIP = getClientIP(req);
        if (finalConfig.blockSuspiciousIPs && isSuspiciousIP(clientIP)) {
          console.warn(`Blocked suspicious IP: ${clientIP}`);
          return NextResponse.json({ error: "Access denied" }, { status: 403 });
        }

        // 2. User Agent Validation
        const userAgent = req.headers.get("user-agent") || "";
        if (isMaliciousUserAgent(userAgent)) {
          console.warn(`Blocked malicious user agent: ${userAgent}`);
          return NextResponse.json({ error: "Access denied" }, { status: 403 });
        }

        // 3. Rate Limiting
        if (finalConfig.rateLimiting) {
          const identifier = `${clientIP}-${req.url}`;
          const isAllowed = validateRateLimit(
            identifier,
            finalConfig.rateLimiting.windowMs,
            finalConfig.rateLimiting.maxRequests
          );

          if (!isAllowed) {
            console.warn(`Rate limit exceeded for IP: ${clientIP}`);
            return NextResponse.json(
              { error: "Too many requests" },
              {
                status: 429,
                headers: {
                  "Retry-After": String(
                    Math.ceil(finalConfig.rateLimiting.windowMs / 1000)
                  ),
                  "X-RateLimit-Limit": String(
                    finalConfig.rateLimiting.maxRequests
                  ),
                  "X-RateLimit-Remaining": "0",
                },
              }
            );
          }
        }

        // 4. Handle CORS
        if (req.method === "OPTIONS") {
          return handleCORS(req, finalConfig.cors!);
        }

        // 5. Content Length Validation
        const contentLength = req.headers.get("content-length");
        if (
          contentLength &&
          Number.parseInt(contentLength) > 10 * 1024 * 1024
        ) {
          // 10MB limit
          return NextResponse.json(
            { error: "Request too large" },
            { status: 413 }
          );
        }

        // 6. Request Method Validation
        const allowedMethods =
          finalConfig.cors?.methods ||
          (["GET", "POST", "PUT", "DELETE"] as readonly string[]);
        if (!allowedMethods.includes(req.method)) {
          return NextResponse.json(
            { error: "Method not allowed" },
            { status: 405 }
          );
        }

        // 7. Execute the actual handler
        const response = await handler(req);

        // 8. Add CORS headers to response
        const responseWithCors = addCorsHeaders(
          req,
          response,
          finalConfig.cors!
        );

        // 9. Add security headers to response
        return addSecurityHeaders(responseWithCors, finalConfig);
      } catch (error) {
        console.error("Security middleware error:", error);
        return NextResponse.json(
          { error: "Internal server error" },
          { status: 500 }
        );
      }
    };
  };
}

// Extract client IP address
function getClientIP(req: NextRequest): string {
  // Try multiple headers for IP detection
  const forwarded = req.headers.get("x-forwarded-for");
  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }

  return (
    req.headers.get("x-real-ip") ||
    req.headers.get("cf-connecting-ip") ||
    req.headers.get("x-client-ip") ||
    "unknown"
  );
}

// Check if IP is suspicious
function isSuspiciousIP(ip: string): boolean {
  if (ip === "unknown") return false;

  // Skip validation in development
  if (process.env.NODE_ENV === "development") {
    return false;
  }

  const { isValid } = validateAndParseIP(ip);
  if (!isValid) return true;

  return SUSPICIOUS_IP_PATTERNS.some((pattern) => pattern.test(ip));
}

// Check if user agent is malicious
function isMaliciousUserAgent(userAgent: string): boolean {
  if (!userAgent) return true; // Block empty user agents

  return MALICIOUS_USER_AGENTS.some((pattern) => pattern.test(userAgent));
}

// Handle CORS preflight requests
function handleCORS(
  req: NextRequest,
  corsConfig: NonNullable<SecurityConfig["cors"]>
): NextResponse {
  const origin = req.headers.get("origin");
  const allowedOrigins = corsConfig.origins;

  // Check if origin is allowed
  const isOriginAllowed =
    allowedOrigins.includes("*") || (origin && allowedOrigins.includes(origin));

  const headers: Record<string, string> = {
    "Access-Control-Allow-Methods": [...corsConfig.methods].join(", "),
    "Access-Control-Allow-Headers": [...corsConfig.headers].join(", "),
    "Access-Control-Max-Age": "86400", // 24 hours
  };

  if (isOriginAllowed && origin) {
    headers["Access-Control-Allow-Origin"] = origin;
    headers["Access-Control-Allow-Credentials"] = "true";
  }

  return new NextResponse(null, { status: 200, headers });
}

// Add CORS headers to response
function addCorsHeaders(
  req: NextRequest,
  response: NextResponse,
  corsConfig: NonNullable<SecurityConfig["cors"]>
): NextResponse {
  const origin = req.headers.get("origin");
  const allowedOrigins = corsConfig.origins;

  // Check if origin is allowed
  const isOriginAllowed =
    allowedOrigins.includes("*") || (origin && allowedOrigins.includes(origin));

  const headers = new Headers(response.headers);

  if (isOriginAllowed) {
    if (origin) {
      headers.set("Access-Control-Allow-Origin", origin);
    } else if (allowedOrigins.includes("*")) {
      headers.set("Access-Control-Allow-Origin", "*");
    }
    headers.set(
      "Access-Control-Allow-Methods",
      [...corsConfig.methods].join(", ")
    );
    headers.set(
      "Access-Control-Allow-Headers",
      [...corsConfig.headers].join(", ")
    );
    headers.set("Vary", "Origin");
  }

  return new NextResponse(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  });
}

// Add security headers to response
function addSecurityHeaders(
  response: NextResponse,
  config: SecurityConfig
): NextResponse {
  const headers = new Headers(response.headers);

  // Content Security Policy
  if (config.contentSecurityPolicy) {
    headers.set(
      "Content-Security-Policy",
      "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
        "style-src 'self' 'unsafe-inline'; " +
        "img-src 'self' data: https:; " +
        "font-src 'self' data:; " +
        "connect-src 'self' ws: wss:; " +
        "frame-ancestors 'none';"
    );
  }

  // Security headers
  headers.set("X-Frame-Options", "DENY");
  headers.set("X-Content-Type-Options", "nosniff");
  headers.set("X-XSS-Protection", "1; mode=block");
  headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()");

  // HSTS (only in production with HTTPS)
  if (process.env.NODE_ENV === "production") {
    headers.set(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains; preload"
    );
  }

  console.log("headers", headers);

  return new NextResponse(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  });
}

// Honeypot middleware to catch bots
export function honeypotMiddleware(req: NextRequest): boolean {
  // Check for honeypot field in request body
  const honeypotField = req.headers.get("x-honeypot");
  if (honeypotField) {
    console.warn(`Honeypot triggered by IP: ${getClientIP(req)}`);
    return false; // Block request
  }

  return true; // Allow request
}

// Request signature validation (for webhook endpoints)
export function validateRequestSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const expectedSignature = crypto
      .createHmac("sha256", secret)
      .update(payload, "utf8")
      .digest("hex");

    const providedSignature = signature.replace("sha256=", "");

    // Constant-time comparison
    let result = 0;
    for (let i = 0; i < expectedSignature.length; i++) {
      result |=
        expectedSignature.charCodeAt(i) ^ providedSignature.charCodeAt(i);
    }

    return (
      result === 0 && expectedSignature.length === providedSignature.length
    );
  } catch (error) {
    console.error("Signature validation error:", error);
    return false;
  }
}

// Export commonly used security configurations
export const SECURITY_CONFIGS = {
  // API endpoints
  api: {
    rateLimiting: { windowMs: 60 * 1000, maxRequests: 100 },
    cors: {
      origins: ["*"],
      methods: ["GET", "POST"],
      headers: ["Content-Type", "Authorization"],
    },
    contentSecurityPolicy: true,
    blockSuspiciousIPs: true,
  },

  // Public tracking endpoints (more permissive)
  tracking: {
    rateLimiting: { windowMs: 60 * 1000, maxRequests: 1000 },
    cors: {
      origins: ["*"],
      methods: ["POST", "OPTIONS"],
      headers: ["Content-Type"],
    },
    contentSecurityPolicy: false,
    blockSuspiciousIPs: false,
  },

  // Presence endpoints (optimized for sendBeacon)
  presence: {
    rateLimiting: { windowMs: 60 * 1000, maxRequests: 2000 },
    cors: {
      origins: ["*"],
      methods: ["POST", "OPTIONS"],
      headers: ["Content-Type", "X-Requested-With", "Accept"],
    },
    contentSecurityPolicy: false,
    blockSuspiciousIPs: false,
  },

  // Authentication endpoints (strict)
  auth: {
    rateLimiting: { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 attempts per 15 minutes
    cors: { origins: [], methods: ["POST"], headers: ["Content-Type"] },
    contentSecurityPolicy: true,
    blockSuspiciousIPs: true,
  },
} as const;
