// Security audit and logging utilities
interface SecurityEvent {
  type:
    | "authentication"
    | "authorization"
    | "data_access"
    | "suspicious_activity"
    | "rate_limit"
    | "validation_error";
  severity: "low" | "medium" | "high" | "critical";
  userId?: string;
  ip: string;
  userAgent: string;
  resource: string;
  action: string;
  details: any;
  timestamp: Date;
}

// In-memory audit log (in production, use a proper logging service)
const auditLog: SecurityEvent[] = [];
const MAX_LOG_SIZE = 10000;

// Log security events
export function logSecurityEvent(event: Omit<SecurityEvent, "timestamp">) {
  const securityEvent: SecurityEvent = {
    ...event,
    timestamp: new Date(),
  };

  // Add to audit log
  auditLog.push(securityEvent);

  // Trim log if too large
  if (auditLog.length > MAX_LOG_SIZE) {
    auditLog.splice(0, auditLog.length - MAX_LOG_SIZE);
  }

  // Log to console based on severity
  const logMessage = `[SECURITY ${event.severity.toUpperCase()}] ${
    event.type
  }: ${event.action} on ${event.resource} by ${event.ip}`;

  switch (event.severity) {
    case "critical":
      console.error(logMessage, event.details);
      // In production, send alert to security team
      break;
    case "high":
      console.warn(logMessage, event.details);
      break;
    case "medium":
      console.log(logMessage);
      break;
    case "low":
      if (process.env.NODE_ENV === "development") {
        console.log(logMessage);
      }
      break;
  }

  // In production, send to external logging service
  if (process.env.NODE_ENV === "production") {
    // Example: Send to logging service
    // await sendToLogService(securityEvent);
  }
}

// Get security events for analysis
export function getSecurityEvents(filters?: {
  type?: SecurityEvent["type"];
  severity?: SecurityEvent["severity"];
  userId?: string;
  ip?: string;
  since?: Date;
  limit?: number;
}): SecurityEvent[] {
  let filteredEvents = [...auditLog];

  if (filters) {
    if (filters.type) {
      filteredEvents = filteredEvents.filter(
        (event) => event.type === filters.type
      );
    }

    if (filters.severity) {
      filteredEvents = filteredEvents.filter(
        (event) => event.severity === filters.severity
      );
    }

    if (filters.userId) {
      filteredEvents = filteredEvents.filter(
        (event) => event.userId === filters.userId
      );
    }

    if (filters.ip) {
      filteredEvents = filteredEvents.filter(
        (event) => event.ip === filters.ip
      );
    }

    if (filters.since) {
      filteredEvents = filteredEvents.filter(
        (event) => event.timestamp >= filters.since!
      );
    }

    if (filters.limit) {
      filteredEvents = filteredEvents.slice(-filters.limit);
    }
  }

  return filteredEvents.sort(
    (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
  );
}

// Analyze security patterns
export function analyzeSecurityPatterns(): {
  suspiciousIPs: string[];
  frequentFailures: { ip: string; count: number }[];
  recentThreats: SecurityEvent[];
  recommendations: string[];
} {
  const now = new Date();
  const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const recentEvents = getSecurityEvents({ since: last24Hours });

  // Count events by IP
  const ipCounts: Record<string, number> = {};
  const ipFailures: Record<string, number> = {};

  for (const event of recentEvents) {
    ipCounts[event.ip] = (ipCounts[event.ip] || 0) + 1;

    if (
      event.type === "authentication" ||
      event.type === "authorization" ||
      event.severity === "high"
    ) {
      ipFailures[event.ip] = (ipFailures[event.ip] || 0) + 1;
    }
  }

  // Find suspicious IPs (high failure rate or high volume)
  const suspiciousIPs = Object.entries(ipFailures)
    .filter(
      ([ip, failures]) =>
        failures > 10 || (ipCounts[ip] && failures / ipCounts[ip] > 0.5)
    )
    .map(([ip]) => ip);

  // Find frequent failures
  const frequentFailures = Object.entries(ipFailures)
    .map(([ip, count]) => ({ ip, count }))
    .filter(({ count }) => count > 5)
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);

  // Find recent high-severity threats
  const recentThreats = recentEvents
    .filter(
      (event) => event.severity === "high" || event.severity === "critical"
    )
    .slice(0, 20);

  // Generate recommendations
  const recommendations = [];

  if (suspiciousIPs.length > 0) {
    recommendations.push(
      `Consider blocking ${suspiciousIPs.length} suspicious IP addresses`
    );
  }

  if (frequentFailures.length > 0) {
    recommendations.push(
      `Investigate ${frequentFailures.length} IPs with frequent failures`
    );
  }

  if (recentThreats.length > 0) {
    recommendations.push(
      `Review ${recentThreats.length} recent high-severity security events`
    );
  }

  const authFailures = recentEvents.filter(
    (e) => e.type === "authentication"
  ).length;
  if (authFailures > 100) {
    recommendations.push(
      "High number of authentication failures - consider implementing CAPTCHA"
    );
  }

  return {
    suspiciousIPs,
    frequentFailures,
    recentThreats,
    recommendations,
  };
}

// Security health check
export function performSecurityHealthCheck(): {
  status: "healthy" | "warning" | "critical";
  checks: Array<{
    name: string;
    status: "pass" | "fail" | "warning";
    message: string;
  }>;
} {
  const checks: Array<{
    name: string;
    status: "pass" | "fail" | "warning";
    message: string;
  }> = [];
  let overallStatus: "healthy" | "warning" | "critical" = "healthy";

  // Check recent security events
  const recentCritical = getSecurityEvents({
    severity: "critical",
    since: new Date(Date.now() - 60 * 60 * 1000), // Last hour
  });

  if (recentCritical.length > 0) {
    checks.push({
      name: "Critical Security Events",
      status: "fail",
      message: `${recentCritical.length} critical security events in the last hour`,
    });
    overallStatus = "critical";
  } else {
    checks.push({
      name: "Critical Security Events",
      status: "pass",
      message: "No critical security events in the last hour",
    });
  }

  // Check rate limiting effectiveness
  const rateLimitEvents = getSecurityEvents({
    type: "rate_limit",
    since: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
  });

  if (rateLimitEvents.length > 100) {
    checks.push({
      name: "Rate Limiting",
      status: "warning",
      message: `High rate limiting activity: ${rateLimitEvents.length} events`,
    });
    if (overallStatus === "healthy") overallStatus = "warning";
  } else {
    checks.push({
      name: "Rate Limiting",
      status: "pass",
      message: "Rate limiting operating normally",
    });
  }

  // Check authentication failures
  const authFailures = getSecurityEvents({
    type: "authentication",
    since: new Date(Date.now() - 24 * 60 * 60 * 1000),
  });

  if (authFailures.length > 500) {
    checks.push({
      name: "Authentication Security",
      status: "warning",
      message: `High authentication failure rate: ${authFailures.length} failures`,
    });
    if (overallStatus === "healthy") overallStatus = "warning";
  } else {
    checks.push({
      name: "Authentication Security",
      status: "pass",
      message: "Authentication failure rate within normal range",
    });
  }

  // Check suspicious activity
  const analysis = analyzeSecurityPatterns();
  if (analysis.suspiciousIPs.length > 10) {
    checks.push({
      name: "Suspicious Activity",
      status: "warning",
      message: `${analysis.suspiciousIPs.length} suspicious IP addresses detected`,
    });
    if (overallStatus === "healthy") overallStatus = "warning";
  } else {
    checks.push({
      name: "Suspicious Activity",
      status: "pass",
      message: "Suspicious activity within normal range",
    });
  }

  return {
    status: overallStatus,
    checks,
  };
}

// Export audit log for external analysis
export function exportAuditLog(format: "json" | "csv" = "json"): string {
  if (format === "csv") {
    const headers = [
      "timestamp",
      "type",
      "severity",
      "userId",
      "ip",
      "userAgent",
      "resource",
      "action",
      "details",
    ];
    const rows = auditLog.map((event) => [
      event.timestamp.toISOString(),
      event.type,
      event.severity,
      event.userId || "",
      event.ip,
      event.userAgent,
      event.resource,
      event.action,
      JSON.stringify(event.details),
    ]);

    return [headers, ...rows].map((row) => row.join(",")).join("\n");
  }

  return JSON.stringify(auditLog, null, 2);
}

// Clear audit log (for maintenance)
export function clearAuditLog(): void {
  auditLog.length = 0;
  logSecurityEvent({
    type: "data_access",
    severity: "medium",
    ip: "system",
    userAgent: "system",
    resource: "audit_log",
    action: "clear",
    details: { reason: "maintenance" },
  });
}
