// Enhanced validation and sanitization utilities
import DOMPurify from "isomorphic-dompurify";

// Extend global type
declare global {
  var rateLimitStore: Map<string, number> | undefined;
}

// Input validation schemas
export interface ValidationSchema {
  [key: string]: {
    type:
      | "string"
      | "number"
      | "boolean"
      | "email"
      | "url"
      | "uuid"
      | "array"
      | "object";
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    min?: number;
    max?: number;
    sanitize?: boolean;
  };
}

// Validation result
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedData?: any;
}

// Common validation patterns
const PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  url: /^https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*)?(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?$/,
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  domain:
    /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/,
  ipAddress:
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
};

// Sanitize HTML content
export function sanitizeHtml(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ["b", "i", "em", "strong", "a", "p", "br"],
    ALLOWED_ATTR: ["href"],
  });
}

// Sanitize user input
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, "") // Remove angle brackets
    .replace(/javascript:/gi, "") // Remove javascript: protocol
    .replace(/on\w+=/gi, "") // Remove event handlers
    .slice(0, 10000); // Limit length
}

// Validate single field
export function validateField(
  value: any,
  fieldName: string,
  rules: ValidationSchema[string]
): string[] {
  const errors: string[] = [];

  // Check required
  if (
    rules.required &&
    (value === undefined || value === null || value === "")
  ) {
    errors.push(`${fieldName} is required`);
    return errors;
  }

  // Skip further validation if value is empty and not required
  if (
    !rules.required &&
    (value === undefined || value === null || value === "")
  ) {
    return errors;
  }

  // Type validation
  switch (rules.type) {
    case "string":
      if (typeof value !== "string") {
        errors.push(`${fieldName} must be a string`);
      } else {
        if (rules.minLength && value.length < rules.minLength) {
          errors.push(
            `${fieldName} must be at least ${rules.minLength} characters`
          );
        }
        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push(
            `${fieldName} must be no more than ${rules.maxLength} characters`
          );
        }
        if (rules.pattern && !rules.pattern.test(value)) {
          errors.push(`${fieldName} format is invalid`);
        }
      }
      break;

    case "number": {
      const num = Number(value);
      if (Number.isNaN(num)) {
        errors.push(`${fieldName} must be a number`);
      } else {
        if (rules.min !== undefined && num < rules.min) {
          errors.push(`${fieldName} must be at least ${rules.min}`);
        }
        if (rules.max !== undefined && num > rules.max) {
          errors.push(`${fieldName} must be no more than ${rules.max}`);
        }
      }
      break;
    }

    case "boolean":
      if (typeof value !== "boolean" && value !== "true" && value !== "false") {
        errors.push(`${fieldName} must be a boolean`);
      }
      break;

    case "email":
      if (typeof value !== "string" || !PATTERNS.email.test(value)) {
        errors.push(`${fieldName} must be a valid email address`);
      }
      break;

    case "url":
      if (typeof value !== "string" || !PATTERNS.url.test(value)) {
        errors.push(`${fieldName} must be a valid URL`);
      }
      break;

    case "uuid":
      if (typeof value !== "string" || !PATTERNS.uuid.test(value)) {
        errors.push(`${fieldName} must be a valid UUID`);
      }
      break;

    case "array":
      if (!Array.isArray(value)) {
        errors.push(`${fieldName} must be an array`);
      }
      break;

    case "object":
      if (typeof value !== "object" || value === null || Array.isArray(value)) {
        errors.push(`${fieldName} must be an object`);
      }
      break;
  }

  return errors;
}

// Validate object against schema
export function validate(
  data: any,
  schema: ValidationSchema
): ValidationResult {
  const errors: string[] = [];
  const sanitizedData: any = {};

  // Validate each field in schema
  for (const [fieldName, rules] of Object.entries(schema)) {
    const value = data[fieldName];
    const fieldErrors = validateField(value, fieldName, rules);
    errors.push(...fieldErrors);

    // Sanitize if no errors and sanitization is enabled
    if (fieldErrors.length === 0 && value !== undefined && value !== null) {
      if (rules.sanitize && typeof value === "string") {
        sanitizedData[fieldName] = sanitizeInput(value);
      } else {
        sanitizedData[fieldName] = value;
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedData: errors.length === 0 ? sanitizedData : undefined,
  };
}

// Predefined schemas for common data types
export const VALIDATION_SCHEMAS = {
  website: {
    name: {
      type: "string",
      required: true,
      minLength: 1,
      maxLength: 100,
      sanitize: true,
    },
    domain: {
      type: "string",
      required: true,
      pattern: PATTERNS.domain,
      sanitize: true,
    },
    timezone: { type: "string", required: false, maxLength: 50 },
    currency: {
      type: "string",
      required: false,
      maxLength: 3,
      pattern: /^[A-Z]{3}$/,
    },
  } as ValidationSchema,

  event: {
    websiteId: { type: "uuid", required: true },
    visitorId: {
      type: "string",
      required: true,
      maxLength: 100,
      sanitize: true,
    },
    sessionId: {
      type: "string",
      required: true,
      maxLength: 100,
      sanitize: true,
    },
    eventType: {
      type: "string",
      required: true,
      pattern: /^(pageview|custom|payment|signup|conversion)$/,
    },
    eventName: {
      type: "string",
      required: false,
      maxLength: 100,
      sanitize: true,
    },
    url: { type: "url", required: true },
    referrer: {
      type: "string",
      required: false,
      maxLength: 1000,
      sanitize: true,
    },
    revenue: { type: "number", required: false, min: 0, max: 1000000 },
    utm_source: {
      type: "string",
      required: false,
      maxLength: 100,
      sanitize: true,
    },
    utm_medium: {
      type: "string",
      required: false,
      maxLength: 100,
      sanitize: true,
    },
    utm_campaign: {
      type: "string",
      required: false,
      maxLength: 100,
      sanitize: true,
    },
    utm_content: {
      type: "string",
      required: false,
      maxLength: 100,
      sanitize: true,
    },
    utm_term: {
      type: "string",
      required: false,
      maxLength: 100,
      sanitize: true,
    },
  } as ValidationSchema,

  user: {
    name: { type: "string", required: false, maxLength: 100, sanitize: true },
    email: { type: "email", required: true },
  } as ValidationSchema,

  goal: {
    websiteId: { type: "uuid", required: true },
    name: {
      type: "string",
      required: true,
      minLength: 1,
      maxLength: 100,
      sanitize: true,
    },
    eventName: {
      type: "string",
      required: false,
      maxLength: 100,
      sanitize: true,
    },
    targetValue: { type: "number", required: false, min: 0 },
  } as ValidationSchema,
};

// Rate limiting validation
export function validateRateLimit(
  identifier: string,
  windowMs: number,
  maxRequests: number
): boolean {
  // This is a simple in-memory implementation
  // In production, use Redis or a proper rate limiting service
  const now = Date.now();
  const windowStart = Math.floor(now / windowMs) * windowMs;

  if (typeof global === "undefined") return true; // Skip in browser

  if (!global.rateLimitStore) {
    global.rateLimitStore = new Map();
  }

  const key = `${identifier}-${windowStart}`;
  const current = global.rateLimitStore.get(key) || 0;

  if (current >= maxRequests) {
    return false;
  }

  global.rateLimitStore.set(key, current + 1);

  // Cleanup old entries
  setTimeout(() => {
    global.rateLimitStore?.delete(key);
  }, windowMs);

  return true;
}

// IP address validation and geolocation
export function validateAndParseIP(ip: string): {
  isValid: boolean;
  type: "ipv4" | "ipv6" | null;
} {
  // IPv4 validation
  if (PATTERNS.ipAddress.test(ip)) {
    return { isValid: true, type: "ipv4" };
  }

  // IPv6 validation (simplified)
  const ipv6Pattern = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::/;
  if (ipv6Pattern.test(ip)) {
    return { isValid: true, type: "ipv6" };
  }

  return { isValid: false, type: null };
}

// SQL injection prevention
export function preventSQLInjection(input: string): string {
  const sqlPatterns = [
    /(\b(ALTER|CREATE|DELETE|DROP|EXEC(UTE)?|INSERT( +INTO)?|MERGE|SELECT|UPDATE|UNION( +ALL)?)\b)/gi,
    /(\')|(\-\-)|(\;)|(\b(OR|AND)\b.*\b(TRUE|FALSE)\b)/gi,
    /\b(SCRIPT|JAVASCRIPT|VBSCRIPT|ONLOAD|ONERROR|ONCLICK)\b/gi,
  ];

  let sanitized = input;
  for (const pattern of sqlPatterns) {
    sanitized = sanitized.replace(pattern, "");
  }

  return sanitized.trim();
}

// Content Security Policy helpers
export function generateCSPNonce(): string {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
    ""
  );
}

// CSRF token generation and validation
export function generateCSRFToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, (byte) => byte.toString(16).padStart(2, "0")).join(
    ""
  );
}

export function validateCSRFToken(
  token: string,
  expectedToken: string
): boolean {
  if (!token || !expectedToken) return false;
  if (token.length !== expectedToken.length) return false;

  // Constant-time comparison to prevent timing attacks
  let result = 0;
  for (let i = 0; i < token.length; i++) {
    result |= token.charCodeAt(i) ^ expectedToken.charCodeAt(i);
  }

  return result === 0;
}
