import { fetchQuery } from "convex/nextjs";
import { api } from "@/convex/_generated/api";
import { getRealtimeVisitorsFromPresence } from "./client";

interface PresenceConfig {
  provider: "convex" | "tinybird" | "none";
}

function getPresenceConfig(): PresenceConfig {
  const provider = (process.env.PRESENCE_PROVIDER ||
    "tinybird") as PresenceConfig["provider"];
  return {
    provider,
  };
}

export async function getOnlineCountFromPresenceProvider(
  websiteId: string
): Promise<number | null> {
  const cfg = getPresenceConfig();

  if (cfg.provider !== "convex") {
    return null;
  }

  try {
    // 使用 convex/nextjs 的 fetchQuery 来获取在线数量
    const presenceList = await fetchQuery(api.presence.listByRoomId, {
      roomId: websiteId,
    });

    console.log("presenceList", presenceList);

    // 只计算在线用户数量
    if (!Array.isArray(presenceList)) return 0;
    const onlineUsers = presenceList.filter(
      (item: any) => item.online === true
    );
    return onlineUsers.length;
  } catch (err) {
    console.error("Convex presence online count failed:", err);
    return null;
  }
}

// 获取实时访客详细信息
export async function getRealtimeVisitorsFromProvider(
  websiteId: string
): Promise<any[] | null> {
  const cfg = getPresenceConfig();

  if (cfg.provider !== "convex") {
    return null;
  }

  try {
    return await getRealtimeVisitorsFromPresence(websiteId);
  } catch (err) {
    console.error("Convex presence visitor list failed:", err);
    return null;
  }
}
