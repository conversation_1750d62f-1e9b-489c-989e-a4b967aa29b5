import { fetchQuery, fetchMutation } from "convex/nextjs";
import { api } from "@/convex/_generated/api";

export interface PresenceHeartbeatArgs {
  roomId: string;
  userId: string;
  sessionId: string;
  interval: number;
  // 访客详细信息
  url?: string;
  referrer?: string;
  device?: string;
  browser?: string;
  os?: string;
  screen?: string;
  country?: string; // 从服务端 headers 获取
  city?: string; // 从服务端 headers 获取
  timestamp?: string;
}

export interface PresenceDisconnectArgs {
  sessionToken: string;
}

export async function presenceList(roomToken: string): Promise<any[]> {
  try {
    const result = await fetchQuery(api.presence.list, { roomToken });
    return Array.isArray(result) ? result : [];
  } catch (e) {
    console.error("presenceList failed", e);
    return [];
  }
}

// 专门用于获取实时访客详细信息的函数
export async function getRealtimeVisitorsFromPresence(
  websiteId: string
): Promise<any[]> {
  try {
    // 使用新的 listWithDetailsByRoomId 查询来获取包含详细信息的访客数据
    const presenceData = await fetchQuery(
      api.presence.listWithDetailsByRoomId,
      {
        roomId: websiteId,
      }
    );

    if (!Array.isArray(presenceData)) {
      return [];
    }

    // 转换 Convex Presence 数据格式为与 Tinybird 兼容的格式
    // 只返回在线用户
    return presenceData
      .filter((item: any) => item.online === true)
      .map((item: any) => ({
        visitor_id: item.user || item.userId,
        url: item.details?.url || "",
        country: item.details?.country || "",
        city: item.details?.city || "",
        timezone: item.details?.timezone || "",
        device: item.details?.device || "",
        browser: item.details?.browser || "",
        os: item.details?.os || "",
        screen: item.details?.screen || "",
        referrer: item.details?.referrer || "",
        timestamp:
          item.details?.timestamp ||
          new Date(item.updated || Date.now()).toISOString(),
      }));
  } catch (e) {
    console.error("getRealtimeVisitorsFromPresence failed", e);
    return [];
  }
}

export async function presenceHeartbeat(
  args: PresenceHeartbeatArgs
): Promise<{ roomToken?: string; sessionToken?: string } | null> {
  try {
    const result = await fetchMutation(api.presence.heartbeat, args);

    return result || null;
  } catch (e) {
    console.error("presenceHeartbeat failed", e);
    return null;
  }
}

export async function presenceDisconnect(
  args: PresenceDisconnectArgs
): Promise<void> {
  try {
    await fetchMutation(api.presence.disconnect, args);
  } catch (e) {
    console.error("presenceDisconnect failed", e);
  }
}
