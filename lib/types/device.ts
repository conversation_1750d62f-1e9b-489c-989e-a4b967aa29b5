/**
 * Device Analytics Types
 * Comprehensive TypeScript interfaces for device analytics components
 */

// Re-export Tinybird device interfaces
export type { DeviceBreakdown } from "@/lib/tinybird/client";

// Define DeviceOverview locally
export interface DeviceOverview {
  total_devices: number;
  total_browsers: number;
  total_os: number;
  total_unique_visitors: number;
  total_events: number;
  total_pageviews: number;
  total_revenue: number;
  top_devices: string[];
  top_devices_visitors: number[];
  top_browsers: string[];
  top_browsers_visitors: number[];
  top_os: string[];
  top_os_visitors: number[];
}

// Component-specific interfaces
export interface DeviceAnalyticsProps {
  websiteId: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  className?: string;
}

export interface DeviceTabsProps {
  activeTab: DeviceTab;
  onTabChange: (tab: DeviceTab) => void;
}

export type DeviceTab = "device" | "browser" | "os";

export interface DeviceTableProps<T> {
  data: T[];
  loading?: boolean;
  error?: string | null;
  onRowClick?: (item: T) => void;
  className?: string;
}

export interface DeviceTableItem {
  category: string;
  displayName: string;
  icon: string;
  visitors: number;
  sessions: number;
  pageviews: number;
  total_revenue: number;
  percentage: number;
  last_activity: string;
}

export interface BrowserTableItem {
  browser: string;
  displayName: string;
  icon: string;
  visitors: number;
  sessions: number;
  pageviews: number;
  total_revenue: number;
  percentage: number;
  last_activity: string;
}

export interface OSTableItem {
  os: string;
  displayName: string;
  icon: string;
  visitors: number;
  sessions: number;
  pageviews: number;
  total_revenue: number;
  percentage: number;
  last_activity: string;
}

export interface DeviceStatsProps {
  overview: DeviceOverview;
  loading?: boolean;
  className?: string;
}

// Utility types for device/browser/OS mapping
export interface DeviceInfo {
  name: string;
  displayName: string;
  icon: string;
  category: "desktop" | "mobile" | "tablet" | "other";
}

export interface BrowserInfo {
  name: string;
  displayName: string;
  icon: string;
  vendor: string;
}

export interface OSInfo {
  name: string;
  displayName: string;
  icon: string;
  category: "desktop" | "mobile" | "server" | "other";
}

// Data processing types
export interface ProcessedDeviceData {
  devices: DeviceTableItem[];
  browsers: BrowserTableItem[];
  os: OSTableItem[];
  overview: DeviceOverview;
}

// Error handling
export interface DeviceError {
  type: "fetch" | "processing" | "validation";
  message: string;
  details?: any;
}

// Loading states
export interface DeviceLoadingState {
  overview: boolean;
  devices: boolean;
  browsers: boolean;
  os: boolean;
  realtime: boolean;
}

// Filter types
export interface DeviceFilters {
  dateRange: {
    from: Date;
    to: Date;
  };
  device?: string;
  browser?: string;
  os?: string;
  minVisitors?: number;
  sortBy?: "visitors" | "revenue" | "percentage";
  sortOrder?: "asc" | "desc";
}

// Chart data types for device visualizations
export interface DeviceChartData {
  name: string;
  value: number;
  percentage: number;
  color?: string;
  icon?: string;
}

// Real-time device data
export interface RealtimeDeviceData {
  device: string;
  browser: string;
  os: string;
  visitor_count: number;
  unique_visitors: number;
  last_seen: string;
  displayName: string;
  icon: string;
}

// Component state management
export interface DeviceState {
  activeTab: DeviceTab;
  data: ProcessedDeviceData | null;
  loading: DeviceLoadingState;
  error: DeviceError | null;
  filters: DeviceFilters;
  selectedDevice: string | null;
  selectedBrowser: string | null;
  selectedOS: string | null;
}

// Action types for state management
export type DeviceAction =
  | { type: "SET_ACTIVE_TAB"; payload: DeviceTab }
  | { type: "SET_DATA"; payload: ProcessedDeviceData }
  | { type: "SET_LOADING"; payload: Partial<DeviceLoadingState> }
  | { type: "SET_ERROR"; payload: DeviceError | null }
  | { type: "SET_FILTERS"; payload: Partial<DeviceFilters> }
  | { type: "SET_SELECTED_DEVICE"; payload: string | null }
  | { type: "SET_SELECTED_BROWSER"; payload: string | null }
  | { type: "SET_SELECTED_OS"; payload: string | null }
  | { type: "RESET_STATE" };

// Hook return types
export interface UseDeviceDataReturn {
  state: DeviceState;
  actions: {
    setActiveTab: (tab: DeviceTab) => void;
    setFilters: (filters: Partial<DeviceFilters>) => void;
    setSelectedDevice: (device: string | null) => void;
    setSelectedBrowser: (browser: string | null) => void;
    setSelectedOS: (os: string | null) => void;
    refreshData: () => Promise<void>;
    resetState: () => void;
  };
}

// Configuration types
export interface DeviceConfig {
  defaultTab: DeviceTab;
  enableRealtime: boolean;
  refreshInterval: number;
  maxDevices: number;
  maxBrowsers: number;
  maxOS: number;
  colorScheme: string[];
  showRevenue: boolean;
  showPercentages: boolean;
}

// API response types
export interface DeviceAnalyticsResponse {
  overview: DeviceOverview;
  devices: DeviceTableItem[];
  browsers: BrowserTableItem[];
  os: OSTableItem[];
}

// Raw data types from Tinybird
export interface RawDeviceData {
  breakdown_type: string;
  category: string;
  visitors: number;
  percentage: number;
  sessions?: number;
  pageviews?: number;
  total_revenue?: number;
  last_activity?: string;
}

// Breakdown type mapping
export interface DeviceBreakdownMapping {
  device: DeviceTableItem[];
  browser: BrowserTableItem[];
  os: OSTableItem[];
}

// Export utility type for tab content
export type DeviceTabContent = DeviceTableItem[] | BrowserTableItem[] | OSTableItem[];

// Export type guards
export const isDeviceTableItem = (item: any): item is DeviceTableItem => {
  return item && typeof item.category === "string";
};

export const isBrowserTableItem = (item: any): item is BrowserTableItem => {
  return item && typeof item.browser === "string";
};

export const isOSTableItem = (item: any): item is OSTableItem => {
  return item && typeof item.os === "string";
};
