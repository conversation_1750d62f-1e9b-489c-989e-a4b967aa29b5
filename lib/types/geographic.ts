/**
 * Geographic Analytics Types
 * Comprehensive TypeScript interfaces for geographic data visualization components
 */

// Re-export Tinybird geographic interfaces
export type {
  GeographicCountry,
  GeographicRegion,
  GeographicCity,
} from "@/lib/tinybird/client";

// Define GeographicOverview locally to avoid import issues
export interface GeographicOverview {
  total_countries: number;
  total_regions: number;
  total_cities: number;
  total_unique_visitors: number;
  total_events: number;
  total_pageviews: number;
  total_revenue: number;
  top_countries: string[];
  top_countries_visitors: number[];
}

// Component-specific interfaces
export interface GeographicAnalyticsProps {
  websiteId: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  className?: string;
}

export interface GeographicTabsProps {
  activeTab: GeographicTab;
  onTabChange: (tab: GeographicTab) => void;
}

export type GeographicTab = "map" | "countries" | "regions" | "cities";

export interface WorldMapProps {
  data: CountryMapData[];
  onCountryClick?: (countryCode: string) => void;
  onCountryHover?: (countryCode: string | null) => void;
  className?: string;
}

export interface CountryMapData {
  countryCode: string;
  countryName: string;
  visitors: number;
  percentage: number;
  revenue?: number;
}

export interface GeographicTableProps<T> {
  data: T[];
  loading?: boolean;
  error?: string | null;
  onRowClick?: (item: T) => void;
  className?: string;
}

export interface CountryTableItem {
  country: string;
  countryName: string;
  flagEmoji: string;
  unique_visitors: number;
  sessions: number;
  pageviews: number;
  total_revenue: number;
  percentage: number;
  last_activity: string;
}

export interface RegionTableItem {
  country: string;
  countryName: string;
  flagEmoji: string;
  region: string;
  unique_visitors: number;
  sessions: number;
  pageviews: number;
  total_revenue: number;
  percentage: number;
  last_activity: string;
}

export interface CityTableItem {
  country: string;
  countryName: string;
  flagEmoji: string;
  region: string;
  city: string;
  unique_visitors: number;
  sessions: number;
  pageviews: number;
  total_revenue: number;
  percentage: number;
  last_activity: string;
}

export interface GeographicStatsProps {
  overview: GeographicOverview;
  loading?: boolean;
  className?: string;
}

// Utility types for country code mapping
export interface CountryInfo {
  code: string;
  name: string;
  flag: string;
}

// Map visualization types
export interface MapTooltipData {
  countryName: string;
  visitors: number;
  percentage: number;
  revenue?: number;
}

export interface MapLegendProps {
  minValue: number;
  maxValue: number;
  colorScale: string[];
  className?: string;
}

// Data processing types
export interface ProcessedGeographicData {
  countries: CountryTableItem[];
  regions: RegionTableItem[];
  cities: CityTableItem[];
  mapData: CountryMapData[];
  overview: GeographicOverview;
}

// Error handling
export interface GeographicError {
  type: "fetch" | "processing" | "validation";
  message: string;
  details?: any;
}

// Loading states
export interface GeographicLoadingState {
  overview: boolean;
  countries: boolean;
  regions: boolean;
  cities: boolean;
  realtime: boolean;
}

// Filter types
export interface GeographicFilters {
  dateRange: {
    from: Date;
    to: Date;
  };
  country?: string;
  region?: string;
  minVisitors?: number;
  sortBy?: "visitors" | "revenue" | "percentage";
  sortOrder?: "asc" | "desc";
}

// Chart data types for geographic visualizations
export interface GeographicChartData {
  name: string;
  value: number;
  percentage: number;
  color?: string;
}

// Real-time location data
export interface RealtimeLocationData {
  country: string;
  city: string;
  visitor_count: number;
  unique_visitors: number;
  last_seen: string;
  countryName: string;
  flagEmoji: string;
}

// Component state management
export interface GeographicState {
  activeTab: GeographicTab;
  data: ProcessedGeographicData | null;
  loading: GeographicLoadingState;
  error: GeographicError | null;
  filters: GeographicFilters;
  selectedCountry: string | null;
  selectedRegion: string | null;
}

// Action types for state management
export type GeographicAction =
  | { type: "SET_ACTIVE_TAB"; payload: GeographicTab }
  | { type: "SET_DATA"; payload: ProcessedGeographicData }
  | { type: "SET_LOADING"; payload: Partial<GeographicLoadingState> }
  | { type: "SET_ERROR"; payload: GeographicError | null }
  | { type: "SET_FILTERS"; payload: Partial<GeographicFilters> }
  | { type: "SET_SELECTED_COUNTRY"; payload: string | null }
  | { type: "SET_SELECTED_REGION"; payload: string | null }
  | { type: "RESET_STATE" };

// Hook return types
export interface UseGeographicDataReturn {
  state: GeographicState;
  actions: {
    setActiveTab: (tab: GeographicTab) => void;
    setFilters: (filters: Partial<GeographicFilters>) => void;
    setSelectedCountry: (country: string | null) => void;
    setSelectedRegion: (region: string | null) => void;
    refreshData: () => Promise<void>;
    resetState: () => void;
  };
}

// Configuration types
export interface GeographicConfig {
  defaultTab: GeographicTab;
  enableRealtime: boolean;
  refreshInterval: number;
  maxCountries: number;
  maxRegions: number;
  maxCities: number;
  colorScheme: string[];
  showRevenue: boolean;
  showPercentages: boolean;
}
