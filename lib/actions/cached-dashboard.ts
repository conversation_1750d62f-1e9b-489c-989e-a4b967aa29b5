"use server"

import { C<PERSON>Keys, CacheTTL, getCachedData } from "@/lib/cache/redis"
import type { DateRange } from "@/lib/db/analytics"
import {
  getChartData as getOriginalChartData,
  getDashboardOverview as getOriginalDashboardOverview,
  getDeviceBreakdownData as getOriginalDeviceBreakdownData,
  getRealTimeVisitorsData as getOriginalRealTimeVisitorsData,
  getTopPagesData as getOriginalTopPagesData,
  getTrafficSourcesData as getOriginalTrafficSourcesData,
  getUserWebsitesWithStats as getOriginalUserWebsitesWithStats,
} from "./dashboard"

// Cached version of getDashboardOverview
export async function getDashboardOverview(
  websiteId: string,
  dateRange: DateRange,
  filters?: {
    country?: string
    device?: string
    browser?: string
    utmSource?: string
  }
) {
  const cacheKey = CacheKeys.dashboardOverview(
    websiteId,
    dateRange.from.toISOString(),
    dateRange.to.toISOString()
  )

  return getCachedData(
    cacheKey,
    () => getOriginalDashboardOverview(websiteId, dateRange, filters),
    CacheTTL.DASHBOARD_OVERVIEW
  )
}

// Cached version of getChartData
export async function getChartData(
  websiteId: string,
  dateRange: DateRange,
  metric: "visitors" | "pageviews" | "revenue",
  interval: "hour" | "day" | "week" | "month" = "day"
) {
  const cacheKey = CacheKeys.timeSeriesData(
    websiteId,
    metric,
    interval,
    dateRange.from.toISOString(),
    dateRange.to.toISOString()
  )

  return getCachedData(
    cacheKey,
    () => getOriginalChartData(websiteId, dateRange, metric, interval),
    CacheTTL.TIME_SERIES
  )
}

// Cached version of getTopPagesData
export async function getTopPagesData(websiteId: string, dateRange: DateRange, limit = 10) {
  const cacheKey = CacheKeys.topPages(
    websiteId,
    dateRange.from.toISOString(),
    dateRange.to.toISOString(),
    limit
  )

  return getCachedData(
    cacheKey,
    () => getOriginalTopPagesData(websiteId, dateRange, limit),
    CacheTTL.TOP_PAGES
  )
}

// Cached version of getTrafficSourcesData
export async function getTrafficSourcesData(websiteId: string, dateRange: DateRange, limit = 10) {
  const cacheKey = CacheKeys.trafficSources(
    websiteId,
    dateRange.from.toISOString(),
    dateRange.to.toISOString(),
    limit
  )

  return getCachedData(
    cacheKey,
    () => getOriginalTrafficSourcesData(websiteId, dateRange, limit),
    CacheTTL.TRAFFIC_SOURCES
  )
}

// Cached version of getDeviceBreakdownData
export async function getDeviceBreakdownData(websiteId: string, dateRange: DateRange) {
  const cacheKey = CacheKeys.deviceBreakdown(
    websiteId,
    dateRange.from.toISOString(),
    dateRange.to.toISOString()
  )

  return getCachedData(
    cacheKey,
    () => getOriginalDeviceBreakdownData(websiteId, dateRange),
    CacheTTL.DEVICE_BREAKDOWN
  )
}

// Cached version of getRealTimeVisitorsData (shorter cache)
export async function getRealTimeVisitorsData(websiteId: string) {
  const cacheKey = CacheKeys.realTimeVisitors(websiteId)

  return getCachedData(
    cacheKey,
    () => getOriginalRealTimeVisitorsData(websiteId),
    CacheTTL.REAL_TIME
  )
}

// Cached version of getUserWebsitesWithStats
export async function getUserWebsitesWithStats() {
  // Note: This should include user ID in the cache key in a real implementation
  // For now, we'll use a shorter cache time to ensure data freshness
  return getCachedData(
    "user_websites_temp", // Should be user-specific
    () => getOriginalUserWebsitesWithStats(),
    CacheTTL.USER_WEBSITES
  )
}
