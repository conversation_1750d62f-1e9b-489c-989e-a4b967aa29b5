"use server";

import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import {
  getUserWebsites,
  softDeleteWebsite,
  getWebsiteDeletionInfo,
} from "@/lib/db/queries";
import { websites } from "@/lib/db/schema";
import {
  type DateRange,
  type ExtendedAnalyticsFilters,
  createAnalyticsFilters,
  getDeviceBreakdown,
  getRealTimeVisitors,
  getTimeSeriesData,
  getTopPages,
  getTrafficSources,
  getWebsiteOverview,
} from "@/lib/tinybird/analytics";
import { and, eq, isNull } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";

// Verify user owns the website (excludes soft-deleted websites)
async function verifyWebsiteAccess(websiteId: string, userId: string) {
  const website = await db
    .select()
    .from(websites)
    .where(
      and(
        eq(websites.id, websiteId),
        eq(websites.userId, userId),
        isNull(websites.deletedAt)
      )
    )
    .limit(1);

  if (!website.length) {
    throw new Error("Website not found or access denied");
  }

  return website[0];
}

// Get dashboard overview data
export async function getDashboardOverview(
  websiteId: string,
  dateRange: DateRange,
  filters?: {
    country?: string;
    device?: string;
    browser?: string;
    utmSource?: string;
  }
) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  try {
    // Verify access
    const website = await verifyWebsiteAccess(websiteId, session.user.id);

    // Get overview metrics using Tinybird
    const analyticsFilters = createAnalyticsFilters({
      websiteId: website.trackingId, // Use tracking ID for Tinybird
      dateRange,
      country: filters?.country,
      device: filters?.device,
      browser: filters?.browser,
      utmSource: filters?.utmSource,
    });

    const overview = await getWebsiteOverview(analyticsFilters);

    // Get comparison data (previous period)
    const periodLength = dateRange.to.getTime() - dateRange.from.getTime();
    const previousPeriod: DateRange = {
      from: new Date(dateRange.from.getTime() - periodLength),
      to: new Date(dateRange.to.getTime() - periodLength),
    };

    const previousAnalyticsFilters = createAnalyticsFilters({
      websiteId: website.trackingId,
      dateRange: previousPeriod,
      country: filters?.country,
      device: filters?.device,
      browser: filters?.browser,
      utmSource: filters?.utmSource,
    });

    const previousOverview = await getWebsiteOverview(previousAnalyticsFilters);

    // Calculate percentage changes
    const calculateChange = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    return {
      current: overview,
      previous: previousOverview,
      changes: {
        visitors: calculateChange(overview.visitors, previousOverview.visitors),
        pageviews: calculateChange(
          overview.pageviews,
          previousOverview.pageviews
        ),
        bounceRate: calculateChange(
          overview.bounce_rate,
          previousOverview.bounce_rate
        ),
        avgSessionDuration: calculateChange(
          overview.avg_session_duration,
          previousOverview.avg_session_duration
        ),
        totalRevenue: calculateChange(
          overview.total_revenue,
          previousOverview.total_revenue
        ),
      },
    };
  } catch (error) {
    console.error("Dashboard overview error:", error);
    throw new Error("Failed to load dashboard data");
  }
}

// Get chart data for analytics
export async function getChartData(
  websiteId: string,
  dateRange: DateRange,
  metric: "visitors" | "pageviews" | "revenue",
  interval: "hour" | "day" | "week" | "month" = "day"
) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  try {
    const website = await verifyWebsiteAccess(websiteId, session.user.id);

    const analyticsFilters = createAnalyticsFilters({
      websiteId: website.trackingId, // Use tracking ID for Tinybird
      dateRange,
    });

    const data = await getTimeSeriesData(analyticsFilters, metric, interval);

    return data;
  } catch (error) {
    console.error("Chart data error:", error);
    throw new Error("Failed to load chart data");
  }
}

// Get top pages data
export async function getTopPagesData(
  websiteId: string,
  dateRange: DateRange,
  limit = 10
) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  try {
    const website = await verifyWebsiteAccess(websiteId, session.user.id);

    const analyticsFilters = createAnalyticsFilters({
      websiteId: website.trackingId,
      dateRange,
    });

    return await getTopPages(analyticsFilters, limit);
  } catch (error) {
    console.error("Top pages error:", error);
    throw new Error("Failed to load top pages data");
  }
}

// Get traffic sources data
export async function getTrafficSourcesData(
  websiteId: string,
  dateRange: DateRange,
  limit = 10
) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  try {
    const website = await verifyWebsiteAccess(websiteId, session.user.id);

    const analyticsFilters = createAnalyticsFilters({
      websiteId: website.trackingId,
      dateRange,
    });

    return await getTrafficSources(analyticsFilters, limit);
  } catch (error) {
    console.error("Traffic sources error:", error);
    throw new Error("Failed to load traffic sources data");
  }
}

// Get device breakdown data
export async function getDeviceBreakdownData(
  websiteId: string,
  dateRange: DateRange
) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  try {
    const website = await verifyWebsiteAccess(websiteId, session.user.id);

    const analyticsFilters = createAnalyticsFilters({
      websiteId: website.trackingId,
      dateRange,
    });

    return await getDeviceBreakdown(analyticsFilters);
  } catch (error) {
    console.error("Device breakdown error:", error);
    throw new Error("Failed to load device breakdown data");
  }
}

// Get real-time visitors
export async function getRealTimeVisitorsData(websiteId: string) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  try {
    const website = await verifyWebsiteAccess(websiteId, session.user.id);

    return await getRealTimeVisitors(website.trackingId);
  } catch (error) {
    console.error("Real-time visitors error:", error);
    throw new Error("Failed to load real-time visitors");
  }
}

// Get user's websites with basic stats
export async function getUserWebsitesWithStats() {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  try {
    const userWebsites = await getUserWebsites(session.user.id);

    // Get basic stats for each website (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const now = new Date();

    const websitesWithStats = await Promise.all(
      userWebsites.map(async (website) => {
        try {
          const analyticsFilters = createAnalyticsFilters({
            websiteId: website.trackingId,
            dateRange: { from: thirtyDaysAgo, to: now },
          });

          const stats = await getWebsiteOverview(analyticsFilters);

          return {
            ...website,
            stats: {
              visitors: stats.visitors,
              pageviews: stats.pageviews,
              revenue: stats.total_revenue,
            },
          };
        } catch (error) {
          // If stats fail, return website without stats
          return {
            ...website,
            stats: {
              visitors: 0,
              pageviews: 0,
              revenue: 0,
            },
          };
        }
      })
    );

    return websitesWithStats;
  } catch (error) {
    console.error("User websites error:", error);
    throw new Error("Failed to load websites");
  }
}

// Update website settings
export async function updateWebsiteSettings(
  prevState: any,
  formData: FormData
) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  const websiteId = formData.get("websiteId") as string;
  const name = formData.get("name") as string;
  const domain = formData.get("domain") as string;
  const timezone = formData.get("timezone") as string;
  const currency = formData.get("currency") as string;
  const isActive =
    formData.get("isActive") === "on" || formData.get("isActive") === "true";

  if (!websiteId || !name || !domain) {
    return {
      success: false,
      error: "Missing required fields",
    };
  }

  try {
    // Verify access
    await verifyWebsiteAccess(websiteId, session.user.id);

    // Update website
    await db
      .update(websites)
      .set({
        name,
        domain,
        timezone,
        currency,
        isActive,
        updatedAt: new Date(),
      })
      .where(eq(websites.id, websiteId));

    revalidatePath("/dashboard");
    revalidatePath(`/dashboard/websites/${websiteId}`);
    revalidatePath(`/dashboard/websites/${websiteId}/settings`);

    return {
      success: true,
      message: "Website settings updated successfully",
    };
  } catch (error) {
    console.error("Update website error:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update website",
    };
  }
}

// Get website deletion info (events count, goals count)
export async function getWebsiteDeletionInfoAction(websiteId: string) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  try {
    // Verify access (this ensures website exists and user owns it)
    await verifyWebsiteAccess(websiteId, session.user.id);

    // Get deletion info from database
    return await getWebsiteDeletionInfo(websiteId);
  } catch (error) {
    console.error("Get website deletion info error:", error);
    throw new Error("Failed to load website deletion info");
  }
}

// Soft delete website (preserves data for potential recovery)
export async function deleteWebsite(websiteId: string) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  try {
    // Verify access (this ensures website exists and user owns it)
    await verifyWebsiteAccess(websiteId, session.user.id);

    // Soft delete website (preserves all related data)
    await softDeleteWebsite(websiteId);

    revalidatePath("/dashboard");
    revalidatePath("/dashboard/websites");
  } catch (error) {
    console.error("Delete website error:", error);
    throw new Error("Failed to delete website");
  }
}
