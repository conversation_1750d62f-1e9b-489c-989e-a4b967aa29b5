{"name": "datafast", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check .", "format": "biome format --write .", "format:check": "biome format .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:optimize": "psql $DATABASE_URL -f drizzle/migrations/0001_performance_indexes.sql", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:seed": "tsx scripts/seed.ts", "setup:env": "node scripts/setup-env.js", "test:env": "npx tsx scripts/test-environment-detection.ts", "security:audit": "npm audit --audit-level=moderate", "security:check": "tsx scripts/security-check.ts", "build:analyze": "ANALYZE=true npm run build", "docker:build": "docker build -t datafast .", "docker:run": "docker run -p 3000:3000 datafast", "docker:stop": "docker stop datafast", "upgrade": "npx @next/codemod@latest upgrade latest", "deploy:vercel": "vercel deploy --prod", "deploy:docker": "docker-compose up -d", "check:tb": "tb build && tb --cloud deploy --check", "deploy:tb": "tb build && tb --cloud deploy", "build:scripts": "terser public/script.js -c toplevel -m -o public/script.min.js", "mcp": "npx @agentdeskai/browser-tools-server@1.2.0"}, "dependencies": {"@auth/drizzle-adapter": "^1.7.2", "@convex-dev/presence": "^0.1.5", "@hookform/resolvers": "^3.10.0", "@neondatabase/serverless": "^0.10.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@react-three/drei": "^10.7.4", "@react-three/fiber": "^9.3.0", "@types/mapbox-gl": "^3.4.1", "@upstash/redis": "1.35.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "convex": "^1.25.4", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.36.4", "drizzle-zod": "^0.5.1", "duckdb": "^1.3.2", "framer-motion": "^12.23.12", "isomorphic-dompurify": "^2.18.0", "lucide-react": "^0.460.0", "mapbox-gl": "^3.14.0", "next": "15.4.6", "next-auth": "5.0.0-beta.25", "node-fetch": "^3.3.2", "react": "19.1.1", "react-day-picker": "^9.8.0", "react-dom": "19.1.1", "react-globe.gl": "^2.35.0", "react-hook-form": "^7.54.0", "react-simple-maps": "^3.0.0", "recharts": "^2.13.3", "resend": "^4.0.1", "sonner": "^2.0.7", "stripe": "^18.4.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "three": "^0.179.1", "uuid": "^11.0.3", "ws": "^8.18.0", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@playwright/test": "^1.49.0", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/maplibre-gl": "^1.13.2", "@types/node": "^22.10.1", "@types/react": "19.1.9", "@types/react-dom": "19.1.7", "@types/react-simple-maps": "^3.0.6", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.30.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "tsx": "^4.19.2", "typescript": "^5.7.2"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "testPathIgnorePatterns": ["<rootDir>/.next/", "<rootDir>/node_modules/"], "collectCoverageFrom": ["**/*.{js,jsx,ts,tsx}", "!**/*.d.ts", "!**/node_modules/**", "!**/.next/**", "!**/coverage/**"]}, "overrides": {"react-simple-maps": {"react": "^16.8.0 || 17.x || 18.x || 19.x", "react-dom": "^16.8.0 || 17.x || 18.x || 19.x"}, "@types/react": "19.1.9", "@types/react-dom": "19.1.7"}}