{"framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "functions": {"app/api/events/route.ts": {"maxDuration": 30}, "app/api/analytics/[websiteId]/route.ts": {"maxDuration": 30}}, "regions": ["iad1"], "headers": [{"source": "/api/events", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type"}, {"key": "Cache-Control", "value": "s-maxage=0"}]}, {"source": "/script.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Content-Type", "value": "application/javascript"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "redirects": [{"source": "/analytics", "destination": "/dashboard", "permanent": true}], "rewrites": [{"source": "/js/script.js", "destination": "/script.js"}, {"source": "/analytics.js", "destination": "/script.js"}]}