<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Disconnect</title>
</head>
<body>
    <h1>Test Disconnect Page</h1>
    <p>Open browser console and then close this tab or navigate away to test disconnect functionality.</p>
    
    <button onclick="testDisconnect()">Test Manual Disconnect</button>
    <button onclick="checkSessionToken()">Check Session Token</button>
    <button onclick="testForcedDisconnect()">Test Forced Disconnect</button>
    
    <script>
        function testDisconnect() {
            if (window.sendPresenceDisconnect) {
                window.sendPresenceDisconnect();
            } else {
                console.log("sendPresenceDisconnect not available");
            }
        }
        
        function checkSessionToken() {
            if (window.getCurrentSessionToken) {
                console.log("currentSessionToken:", window.getCurrentSessionToken());
            } else {
                console.log("getCurrentSessionToken not available");
            }
        }

        function testForcedDisconnect() {
            if (window.testDisconnectForced) {
                window.testDisconnectForced();
            } else {
                console.log("testDisconnectForced not available");
            }
        }
    </script>
    
    <!-- InstaSight Tracking Script -->
    <script 
        src="http://localhost:3000/script.js"
        data-website-id="c534ccfc-25a9-4a5a-b86e-e9831924af96"
        data-domain="localhost"
        data-debug="true"
        defer>
    </script>
</body>
</html>
</script>
