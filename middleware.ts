import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";

export default auth((req) => {
  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;

  // Always skip Auth.js internal API routes to avoid HTML redirects breaking JSON consumers
  if (nextUrl.pathname.startsWith("/api/auth")) {
    return NextResponse.next();
  }

  // Public routes that don't require authentication
  const isPublicRoute =
    nextUrl.pathname === "/" ||
    nextUrl.pathname.startsWith("/auth") ||
    nextUrl.pathname.startsWith("/api/events") ||
    nextUrl.pathname.startsWith("/api/presence") ||
    nextUrl.pathname.startsWith("/api/script") ||
    nextUrl.pathname.startsWith("/script.js") ||
    nextUrl.pathname.startsWith("/terms") ||
    nextUrl.pathname.startsWith("/privacy") ||
    nextUrl.pathname.startsWith("/support");

  // Protected routes that require authentication
  const isProtectedRoute =
    nextUrl.pathname.startsWith("/dashboard") ||
    nextUrl.pathname.startsWith("/settings") ||
    (nextUrl.pathname.startsWith("/api") &&
      !nextUrl.pathname.startsWith("/api/events") &&
      !nextUrl.pathname.startsWith("/api/presence") &&
      !nextUrl.pathname.startsWith("/api/script") &&
      !nextUrl.pathname.startsWith("/api/auth"));

  // If user is on a protected route and not logged in, redirect to sign in
  if (isProtectedRoute && !isLoggedIn) {
    return NextResponse.redirect(new URL("/auth/signin", nextUrl));
  }

  // If user is logged in and trying to access auth pages, redirect to dashboard
  if (
    isLoggedIn &&
    nextUrl.pathname.startsWith("/auth") &&
    nextUrl.pathname !== "/auth/signout"
  ) {
    return NextResponse.redirect(new URL("/dashboard", nextUrl));
  }

  return NextResponse.next();
});

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
