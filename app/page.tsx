import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  BarChart,
  Code,
  Globe,
  Rocket,
  Shield,
  ShieldCheck,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react"
import Link from "next/link"

// This page is statically generated at build time
export const revalidate = 3600 // Revalidate every hour

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white via-blue-50/40 to-purple-50/30">
      {/* Navigation */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="mx-auto max-w-7xl px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <BarChart className="h-7 w-7 text-blue-600" />
              <span className="text-xl md:text-2xl font-bold tracking-tight text-gray-900">
                InstaSight
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth/signin" className="text-gray-600 hover:text-gray-900 font-medium">
                Sign In
              </Link>
              <Link
                href="/dashboard"
                className="hidden sm:inline text-gray-600 hover:text-gray-900 font-medium"
              >
                View Demo
              </Link>
              <Button asChild className="shadow-sm hover:shadow md:px-5">
                <Link href="/auth/signin">Get Started</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-24 px-4 overflow-hidden">
        <div className="pointer-events-none absolute inset-0 opacity-40">
          <div className="absolute -top-32 -left-24 h-[400px] w-[400px] rounded-full bg-blue-200 blur-3xl" />
          <div className="absolute -bottom-32 -right-24 h-[420px] w-[420px] rounded-full bg-purple-200 blur-3xl" />
        </div>
        <div className="mx-auto max-w-7xl">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center rounded-full border border-blue-200 bg-white px-3 py-1 text-sm text-blue-700 shadow-sm">
              No credit card required • 14-day free trial
            </div>
            <h1 className="mt-6 text-4xl md:text-6xl font-extrabold tracking-tight text-gray-900">
              Transform Website Visitors Into
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                Revenue Insights
              </span>
            </h1>
            <p className="mt-5 text-lg md:text-xl text-gray-600 mx-auto max-w-2xl">
              Real-time analytics that focus on outcomes, not vanity metrics. Powered by a 4KB
              script and Tinybird streaming.
            </p>
            <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                asChild
                className="text-base md:text-lg px-7 py-3 shadow-sm hover:shadow-md"
              >
                <Link href="/auth/signin">Start Free Trial</Link>
              </Button>
              <Button variant="outline" size="lg" className="text-base md:text-lg px-7 py-3">
                <Link href="/dashboard">View Demo</Link>
              </Button>
            </div>
            <p className="text-sm text-gray-500 mt-5">Cancel anytime</p>
          </div>
        </div>
      </section>

      {/* Trusted logos */}
      <section className="px-4 -mt-6">
        <div className="mx-auto max-w-7xl">
          <div className="flex items-center justify-center gap-8 md:gap-12 opacity-60">
            <img src="/vercel.svg" alt="Vercel" className="h-6" />
            <img src="/next.svg" alt="Next.js" className="h-5" />
            <img src="/globe.svg" alt="Global" className="h-6" />
            <img src="/window.svg" alt="Window" className="h-6" />
          </div>
        </div>
      </section>

      {/* Install in 30 seconds */}
      <section className="py-16 px-4 bg-white">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 items-stretch">
            <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-all">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Code className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle>Install in 30 seconds</CardTitle>
                    <CardDescription>Add one snippet to your site</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-xl border bg-gray-50 p-4 text-sm overflow-auto">
                  <pre className="whitespace-pre-wrap leading-6 text-gray-800">{`<script src="/script-4kb.js" data-website-id="YOUR_WEBSITE_ID" defer></script>`}</pre>
                </div>
                <div className="mt-4 text-sm text-gray-500">Optional: track custom events</div>
                <div className="mt-2 rounded-xl border bg-gray-50 p-4 text-sm overflow-auto">
                  <pre className="whitespace-pre-wrap leading-6 text-gray-800">{`<script>
  window.instasight.track('signup_click', { plan: 'pro' })
</script>`}</pre>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-all">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <ShieldCheck className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <CardTitle>Privacy‑First by Design</CardTitle>
                    <CardDescription>Lightweight, secure, and compliant</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-red-500 mt-0.5" />
                    GDPR‑friendly collection with strict field limits
                  </li>
                  <li className="flex items-start gap-3">
                    <Rocket className="h-5 w-5 text-purple-600 mt-0.5" />
                    4KB script, SPA aware, bot filtered
                  </li>
                  <li className="flex items-start gap-3">
                    <BarChart className="h-5 w-5 text-blue-600 mt-0.5" />
                    Revenue attribution, journeys, and real‑time visitors
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything you need to grow your business
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Powerful analytics that don't slow down your website. Get insights that actually
              matter.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-all">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle>Real-time Visitor Tracking</CardTitle>
                <CardDescription>
                  Watch visitors browse your site in real‑time with live counters and activity
                  streams.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-all">
              <CardHeader>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle>Revenue Attribution</CardTitle>
                <CardDescription>
                  Attribute revenue to sources and campaigns so you double‑down on what works.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-all">
              <CardHeader>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                  <BarChart className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle>Goal Tracking</CardTitle>
                <CardDescription>
                  Set up goals and follow journeys from first visit to purchase.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-all">
              <CardHeader>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="h-6 w-6 text-orange-600" />
                </div>
                <CardTitle>4KB Tracking Script</CardTitle>
                <CardDescription>
                  Ultra‑lightweight script that won’t slow your site. Loads blazing fast.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-all">
              <CardHeader>
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle>Privacy-First Analytics</CardTitle>
                <CardDescription>
                  GDPR‑friendly by design. Gather insights while respecting user privacy.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border border-gray-100 shadow-sm hover:shadow-md transition-all">
              <CardHeader>
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                  <Globe className="h-6 w-6 text-indigo-600" />
                </div>
                <CardTitle>Global Performance</CardTitle>
                <CardDescription>
                  Edge‑optimized infrastructure for fast loads worldwide.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* How it works */}
      <section className="py-16 px-4">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900">How it works</h3>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto">
              From snippet to insights in minutes. Designed for modern web stacks and SPA
              navigation.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
            <Card className="border border-gray-100 shadow-sm">
              <CardHeader>
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                  <Code className="h-5 w-5 text-blue-600" />
                </div>
                <CardTitle>1. Add Script</CardTitle>
                <CardDescription>
                  Drop the 4KB snippet in your <code>&lt;head&gt;</code>.
                </CardDescription>
              </CardHeader>
            </Card>
            <Card className="border border-gray-100 shadow-sm">
              <CardHeader>
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
                  <Globe className="h-5 w-5 text-purple-600" />
                </div>
                <CardTitle>2. Stream Events</CardTitle>
                <CardDescription>Events stream to Tinybird in real‑time.</CardDescription>
              </CardHeader>
            </Card>
            <Card className="border border-gray-100 shadow-sm">
              <CardHeader>
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-3">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <CardTitle>3. See Insights</CardTitle>
                <CardDescription>Open the dashboard for revenue‑driven metrics.</CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="mx-auto max-w-7xl text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-12">
            Trusted by thousands of businesses
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
            <div className="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur">
              <div className="text-3xl md:text-4xl font-bold mb-1">50K+</div>
              <div className="text-blue-100">Websites Tracked</div>
            </div>
            <div className="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur">
              <div className="text-3xl md:text-4xl font-bold mb-1">2B+</div>
              <div className="text-blue-100">Events Processed</div>
            </div>
            <div className="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur">
              <div className="text-3xl md:text-4xl font-bold mb-1">99.9%</div>
              <div className="text-blue-100">Uptime Guaranteed</div>
            </div>
            <div className="rounded-xl border border-white/20 bg-white/10 p-6 backdrop-blur">
              <div className="text-3xl md:text-4xl font-bold mb-1">4KB</div>
              <div className="text-blue-100">Script Size</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4">
        <div className="mx-auto max-w-7xl">
          <div className="mx-auto max-w-3xl text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Ready to understand your visitors?
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Join thousands of businesses using InstaSight to make data-driven decisions.
            </p>
            <Button
              size="lg"
              asChild
              className="text-base md:text-lg px-8 py-3 shadow-sm hover:shadow-md"
            >
              <Link href="/auth/signin">Start Your Free Trial</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <BarChart className="h-6 w-6" />
              <span className="text-xl font-bold">InstaSight</span>
            </div>
            <div className="text-gray-400 text-sm">© 2025 InstaSight. All rights reserved.</div>
          </div>
        </div>
      </footer>
    </div>
  )
}
