import { Suspense } from "react";
import { notFound } from "next/navigation";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { websites } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import FullscreenGlobe from "@/components/3d/fullscreen-globe";
import { ArrowLeft, Globe, Users, MapPin } from "lucide-react";
import Link from "next/link";

interface PageProps {
  params: Promise<{ websiteId: string }>;
}

export default async function ThreeDGlobePage({ params }: PageProps) {
  const session = await auth();
  if (!session?.user?.id) {
    notFound();
  }

  const { websiteId: trackingId } = await params;

  // Get website data by tracking ID
  const website = await db
    .select()
    .from(websites)
    .where(eq(websites.trackingId, trackingId))
    .limit(1);

  if (!website.length || website[0].userId !== session.user.id) {
    notFound();
  }

  const websiteData = website[0];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href={`/dashboard/websites/${websiteData.id}`}
              className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Dashboard</span>
            </Link>
            <div className="h-6 w-px bg-white/20" />
            <div className="flex items-center space-x-2 text-white">
              <Globe className="h-5 w-5" />
              <span className="font-semibold">{websiteData.name}</span>
              <span className="text-white/60">•</span>
              <span className="text-white/80">{websiteData.domain}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Fullscreen Globe */}
      <Suspense
        fallback={
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center text-white">
              <Globe className="h-12 w-12 animate-spin mx-auto mb-4" />
              <p className="text-lg">Loading 3D Globe...</p>
            </div>
          </div>
        }
      >
        <FullscreenGlobe websiteId={trackingId} />
      </Suspense>
    </div>
  );
}

export async function generateMetadata({ params }: PageProps) {
  const { websiteId: trackingId } = await params;
  
  // Get website data for metadata
  const website = await db
    .select()
    .from(websites)
    .where(eq(websites.trackingId, trackingId))
    .limit(1);

  const websiteName = website[0]?.name || "Website";

  return {
    title: `3D Globe - ${websiteName} | InstaSight`,
    description: `Real-time 3D globe visualization showing visitors for ${websiteName}`,
  };
}
