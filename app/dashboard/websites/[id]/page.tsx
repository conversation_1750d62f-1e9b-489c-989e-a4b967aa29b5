import WebsiteStatsPanel from "@/components/dashboard/website-stats-panel";
import MetricsCard from "@/components/dashboard/metrics-card";
import RealTimeActivity from "@/components/dashboard/real-time-activity";
import RealTimeVisitors from "@/components/dashboard/real-time-visitors";

import UTMAnalytics from "@/components/dashboard/utm-analytics";
import GeographicAnalytics from "@/components/dashboard/geographic-analytics";
import DeviceAnalytics from "@/components/dashboard/device-analytics";
import PagesAnalytics from "@/components/dashboard/pages-analytics";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { getDashboardOverview } from "@/lib/actions/dashboard";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { websites } from "@/lib/db/schema";
import {
  createAnalyticsFilters,
  getDefaultDateRange,
  getTimeSeriesData,
  getTopPages,
  getWebsiteOverview,
} from "@/lib/analytics/enhanced";
import { eq } from "drizzle-orm";
import {
  Activity,
  ArrowLeft,
  BarChart3,
  DollarSign,
  Globe,
  Settings,
  TrendingUp,
  Users,
} from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

interface PageProps {
  params: Promise<{ id: string }>;
}

export default async function WebsiteAnalyticsPage({ params }: PageProps) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  const { id: websiteId } = await params;

  // Get website data
  const website = await db
    .select()
    .from(websites)
    .where(eq(websites.id, websiteId))
    .limit(1);

  if (!website.length || website[0].userId !== session.user.id) {
    notFound();
  }

  const websiteData = website[0];

  // Get analytics data from Tinybird for last 30 days
  const dateRange = getDefaultDateRange();

  let overviewData: any;
  let chartData: Array<{
    date: string;
    visitors: number;
    pageviews: number;
    revenue: number;
  }>;
  let topPages: Array<{
    url: string;
    pageviews: number;
    visitors: number;
    bounce_rate: number;
    avg_duration_minutes: number;
  }>;

  try {
    const filters = createAnalyticsFilters({
      websiteId: websiteData.trackingId,
      dateRange,
    });

    // getwebsiteOverview is a function , which do the following：
    // - load the @/lib/tinybird/analytics.ts
    // - call convertToTinybirdFilters to convert to Tinybird format, this function will append start_date and end_date in the object
    // - call tinybirdClient.getOverviewMetrics to get the data
    // - it query the tinybird endpoint overview_metrics and applied the filters （e.g website_id, start_date, end_date, country, device, browser, utm_source） to get the data for the current website owner
    // gettimeSeriesData is a function , which do the following：
    // - load the @/lib/tinybird/analytics.ts
    // - call convertToTinybirdFilters to convert to Tinybird format, this function will append start_date and end_date in the object
    // - call tinybirdClient.getTimeSeriesData to get the data
    // - it query the tinybird endpoint time_series_data and applied the filters （e.g website_id, start_date, end_date, country, device, browser, utm_source） to get the data for the current website owner
    // getTopPages is a function , which do the following：
    // - load the @/lib/tinybird/analytics.ts
    // - call convertToTinybirdFilters to convert to Tinybird format, this function will append start_date and end_date in the object
    // - call tinybirdClient.getTopPages to get the data
    // - it query the tinybird endpoint top_pages and applied the filters （e.g website_id, start_date, end_date, country, device, browser, utm_source） to get the data for the current website owner
    const [tinybirdOverview, timeSeriesData, pagesData] = await Promise.all([
      getWebsiteOverview(filters),
      getTimeSeriesData(filters, "visitors", "day"),
      getTopPages(filters, 5),
    ]);

    overviewData = {
      current: tinybirdOverview,
      changes: {
        visitors: 10.2, // Mock for now - would need previous period data
        pageviews: 15.3,
        totalRevenue: 25.1,
        avgSessionDuration: -2.1,
      },
    };

    chartData = timeSeriesData.map((point: any) => ({
      date: new Date(point.date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
      visitors: point.visitors || point.value || 0,
      pageviews: point.pageviews || Math.round((point.value || 0) * 1.8),
      revenue: point.revenue || Math.round((point.value || 0) * 0.5),
    }));

    topPages = pagesData;
  } catch (error) {
    console.error("Failed to fetch Tinybird analytics data:", error);

    // Fall back to existing PostgreSQL data for overview
    const fallbackOverview = await getDashboardOverview(websiteData.id, {
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      to: new Date(),
    });

    overviewData = fallbackOverview;

    // Mock data for other components
    chartData = [
      { date: "Dec 1", visitors: 1200, pageviews: 2400, revenue: 1800 },
      { date: "Dec 2", visitors: 1400, pageviews: 2800, revenue: 2200 },
      { date: "Dec 3", visitors: 1100, pageviews: 2200, revenue: 1600 },
      { date: "Dec 4", visitors: 1600, pageviews: 3200, revenue: 2800 },
      { date: "Dec 5", visitors: 1300, pageviews: 2600, revenue: 2000 },
      { date: "Dec 6", visitors: 1500, pageviews: 3000, revenue: 2400 },
      { date: "Dec 7", visitors: 1700, pageviews: 3400, revenue: 3000 },
    ];

    topPages = [
      {
        url: "/",
        pageviews: 2847,
        visitors: 1823,
        bounce_rate: 65.2,
        avg_duration_minutes: 3.2,
      },
      {
        url: "/pricing",
        pageviews: 1234,
        visitors: 892,
        bounce_rate: 45.1,
        avg_duration_minutes: 5.1,
      },
      {
        url: "/features",
        pageviews: 987,
        visitors: 654,
        bounce_rate: 52.3,
        avg_duration_minutes: 4.2,
      },
    ];
  }

  return (
    <div className="container mx-auto px-3 py-3 space-y-4">
      {/* Compact Header */}
      <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 shadow-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              asChild
              className="hover:bg-blue-50 dark:hover:bg-slate-700"
            >
              <Link href="/dashboard/websites">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
            <div>
              <div className="flex items-center space-x-2">
                <div className="p-1.5 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-md">
                  <Globe className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                    {websiteData.name}
                  </h1>
                  <div className="flex items-center space-x-2">
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      {websiteData.domain}
                    </p>
                    <Badge
                      variant={websiteData.isActive ? "default" : "secondary"}
                      className={`text-xs ${
                        websiteData.isActive
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 border-green-200 dark:border-green-800"
                          : "bg-slate-100 text-slate-600 dark:bg-slate-800 dark:text-slate-400"
                      }`}
                    >
                      {websiteData.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              asChild
              className="bg-white/50 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 hover:bg-white dark:hover:bg-slate-700 shadow-sm"
            >
              <Link href={`/dashboard/websites/${websiteData.id}/realtime`}>
                <Activity className="h-3 w-3 mr-1" />
                Real-time
              </Link>
            </Button>
            <Button
              variant="default"
              size="sm"
              asChild
              className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
            >
              <Link href={`/3d/${websiteData.trackingId}`}>
                <Globe className="h-3 w-3 mr-1" />
                3D Globe
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              asChild
              className="bg-white/50 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600 hover:bg-white dark:hover:bg-slate-700 shadow-sm"
            >
              <Link href={`/dashboard/websites/${websiteData.id}/settings`}>
                <Settings className="h-3 w-3 mr-1" />
                Settings
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Compact Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <MetricsCard
          title="Total Visitors"
          value={overviewData.current.visitors}
          change={{
            value: overviewData.changes.visitors,
            label: "vs last period",
          }}
          icon={Users}
          className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/20 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
        />
        <MetricsCard
          title="Page Views"
          value={overviewData.current.pageviews}
          change={{
            value: overviewData.changes.pageviews,
            label: "vs last period",
          }}
          icon={BarChart3}
          className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/20 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
        />
        <MetricsCard
          title="Revenue"
          value={overviewData.current?.total_revenue || 0}
          change={{
            value: (overviewData.changes as any)?.totalRevenue || 0,
            label: "vs last period",
          }}
          icon={DollarSign}
          prefix="$"
          className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/20 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
        />
        <MetricsCard
          title="Avg. Session Duration"
          value={`${Math.round(overviewData.current?.avg_session_duration || 0)}m`}
          change={{
            value: (overviewData.changes as any)?.avgSessionDuration || 0,
            label: "vs last period",
          }}
          icon={TrendingUp}
          className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/20 dark:border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
        />
      </div>

      {/* Compact Charts and Real-time Data */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
        {/* Analytics Chart */}
        <div className="lg:col-span-2">
          <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 shadow-lg overflow-hidden">
            <WebsiteStatsPanel
              websiteId={websiteData.id}
              initialFromISO={dateRange.from.toISOString()}
              initialToISO={dateRange.to.toISOString()}
            />
          </div>
        </div>

        {/* Real-time Visitors */}
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 shadow-lg overflow-hidden">
          <RealTimeVisitors websiteId={websiteData.trackingId} />
        </div>
      </div>

      {/* Compact Real-time Activity and Top Sources */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 shadow-lg overflow-hidden">
          <RealTimeActivity websiteId={websiteData.trackingId} />
        </div>
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 shadow-lg overflow-hidden">
          <UTMAnalytics websiteId={websiteData.id} dateRange={dateRange} />
        </div>
      </div>

      {/* Compact Analytics Components */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 shadow-lg overflow-hidden">
          <GeographicAnalytics
            websiteId={websiteData.trackingId}
            dateRange={dateRange}
          />
        </div>
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 shadow-lg overflow-hidden">
          <DeviceAnalytics
            websiteId={websiteData.trackingId}
            dateRange={dateRange}
          />
        </div>
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl border border-white/20 dark:border-slate-700/50 shadow-lg overflow-hidden">
          <PagesAnalytics topPages={topPages || []} />
        </div>
      </div>

      {/* Enhanced Goals & Conversions */}
      <div className="grid grid-cols-1 gap-6">
        <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/20 dark:border-slate-700/50 shadow-xl">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
              Goals & Conversions
            </CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-400">
              Conversion tracking overview
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-100 dark:border-green-800/30">
                <div>
                  <p className="font-semibold text-green-900 dark:text-green-100">
                    Newsletter Signup
                  </p>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    Goal completion
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                    23
                  </p>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    conversions
                  </p>
                </div>
              </div>
              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl border border-purple-100 dark:border-purple-800/30">
                <div>
                  <p className="font-semibold text-purple-900 dark:text-purple-100">
                    Purchase
                  </p>
                  <p className="text-sm text-purple-700 dark:text-purple-300">
                    Revenue goal
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    8
                  </p>
                  <p className="text-sm text-purple-700 dark:text-purple-300">
                    conversions
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
