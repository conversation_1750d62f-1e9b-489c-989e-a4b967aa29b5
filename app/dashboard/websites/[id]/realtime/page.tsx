import RealtimeAnalytics from "@/components/dashboard/realtime-analytics"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { websites } from "@/lib/db/schema"
import { eq } from "drizzle-orm"
import { ArrowLeft, Globe, Zap } from "lucide-react"
import Link from "next/link"
import { notFound, redirect } from "next/navigation"

interface PageProps {
  params: Promise<{ id: string }>
}

export default async function RealtimeAnalyticsPage({ params }: PageProps) {
  const session = await auth()
  if (!session?.user?.id) {
    redirect("/auth/signin")
  }

  const { id: websiteId } = await params

  // Get website data
  const website = await db.select().from(websites).where(eq(websites.id, websiteId)).limit(1)

  if (!website.length || website[0].userId !== session.user.id) {
    notFound()
  }

  const websiteData = website[0]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/dashboard/websites/${websiteData.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Analytics
            </Link>
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <Zap className="h-6 w-6 text-yellow-500" />
              <h1 className="text-3xl font-bold text-gray-900">Real-time Analytics</h1>
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                <div className="h-2 w-2 bg-yellow-500 rounded-full animate-pulse mr-1" />
                Live
              </Badge>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <Globe className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">{websiteData.name}</span>
              <span className="text-muted-foreground">•</span>
              <span className="text-muted-foreground">{websiteData.domain}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Real-time Analytics Component */}
      <RealtimeAnalytics websiteId={websiteData.trackingId} />

      {/* Information Card */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-semibold text-blue-900">Powered by Tinybird</h3>
            <p className="text-blue-700 text-sm mt-1">
              This real-time dashboard streams live data directly from Tinybird's columnar analytics
              database. Data is updated every 30 seconds with sub-second query performance,
              providing you with instant insights into your website's visitor activity and
              engagement metrics.
            </p>
            <div className="flex items-center space-x-4 mt-3 text-xs text-blue-600">
              <span>• Sub-second query latency</span>
              <span>• Real-time data ingestion</span>
              <span>• Scalable to millions of events</span>
              <span>• 10x compression ratio</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
