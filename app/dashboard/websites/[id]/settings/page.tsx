import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { PaymentProviderSetupWrapper } from "@/components/dashboard/payment-provider-setup-wrapper";
import { CopyButton } from "@/components/dashboard/copy-button";
import { WebsiteDeletionDialog } from "@/components/dashboard/website-deletion-dialog";
import { updateWebsiteSettings } from "@/lib/actions/dashboard";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { websites } from "@/lib/db/schema";
import {
  generateMinimalScript,
  generateTrackingScript,
} from "@/lib/utils/tracking";
import { eq } from "drizzle-orm";
import {
  ArrowLeft,
  Code,
  Copy,
  Eye,
  EyeOff,
  Globe,
  Webhook,
} from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

interface PageProps {
  params: Promise<{ id: string }>;
}

export default async function WebsiteSettingsPage({ params }: PageProps) {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/auth/signin");
  }

  const { id: websiteId } = await params;

  // Get website data
  const website = await db
    .select()
    .from(websites)
    .where(eq(websites.id, websiteId))
    .limit(1);

  if (!website.length || website[0].userId !== session.user.id) {
    notFound();
  }

  const websiteData = website[0];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/websites">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Websites
          </Link>
        </Button>
      </div>

      <div>
        <h1 className="text-3xl font-bold text-gray-900">Website Settings</h1>
        <p className="text-gray-600 mt-2">
          Configure your website settings and tracking code
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Website Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5" />
              <span>Website Configuration</span>
            </CardTitle>
            <CardDescription>
              Update your website details and tracking preferences.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={updateWebsiteSettings} className="space-y-6">
              <input type="hidden" name="websiteId" value={websiteData.id} />

              <div className="space-y-2">
                <Label htmlFor="name">Website Name</Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={websiteData.name}
                  placeholder="My Website"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="domain">Domain</Label>
                <Input
                  id="domain"
                  name="domain"
                  defaultValue={websiteData.domain}
                  placeholder="example.com"
                  required
                />
                <p className="text-sm text-muted-foreground">
                  Your website's domain without protocol (e.g., example.com).
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select
                    name="timezone"
                    defaultValue={websiteData.timezone || "UTC"}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">
                        Eastern Time
                      </SelectItem>
                      <SelectItem value="America/Chicago">
                        Central Time
                      </SelectItem>
                      <SelectItem value="America/Denver">
                        Mountain Time
                      </SelectItem>
                      <SelectItem value="America/Los_Angeles">
                        Pacific Time
                      </SelectItem>
                      <SelectItem value="Europe/London">London</SelectItem>
                      <SelectItem value="Europe/Paris">Paris</SelectItem>
                      <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                      <SelectItem value="Asia/Shanghai">Shanghai</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select
                    name="currency"
                    defaultValue={websiteData.currency || "USD"}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD - US Dollar</SelectItem>
                      <SelectItem value="EUR">EUR - Euro</SelectItem>
                      <SelectItem value="GBP">GBP - British Pound</SelectItem>
                      <SelectItem value="JPY">JPY - Japanese Yen</SelectItem>
                      <SelectItem value="CNY">CNY - Chinese Yuan</SelectItem>
                      <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                      <SelectItem value="AUD">
                        AUD - Australian Dollar
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  name="isActive"
                  defaultChecked={websiteData.isActive}
                />
                <Label htmlFor="isActive">Enable tracking</Label>
              </div>

              <Button type="submit" className="w-full">
                Save Changes
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Tracking Code */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Code className="h-5 w-5" />
              <span>Tracking Code</span>
            </CardTitle>
            <CardDescription>
              Add this code to your website to start tracking analytics.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Tracking ID */}
            <div className="space-y-2">
              <Label>Tracking ID</Label>
              <div className="flex items-center space-x-2">
                <Input value={websiteData.trackingId} readOnly />
                <CopyButton text={websiteData.trackingId} />
              </div>
            </div>

            {/* Full Tracking Script */}
            <div className="space-y-2">
              <Label>Full Tracking Script</Label>
              <div className="relative">
                <Textarea
                  value={generateTrackingScript(
                    websiteData.trackingId,
                    websiteData.domain
                  )}
                  readOnly
                  rows={8}
                  className="font-mono text-xs"
                />
                <CopyButton
                  text={generateTrackingScript(
                    websiteData.trackingId,
                    websiteData.domain
                  )}
                  className="absolute top-2 right-2"
                />
              </div>
              <p className="text-sm text-muted-foreground">
                Add this code to your website's `&lt;head&gt;` section.
              </p>
            </div>

            {/* Minimal Script */}
            <div className="space-y-2">
              <Label>Minimal Script (Recommended)</Label>
              <div className="relative">
                <Textarea
                  value={generateMinimalScript(
                    websiteData.trackingId,
                    websiteData.domain
                  )}
                  readOnly
                  rows={3}
                  className="font-mono text-xs"
                />
                <CopyButton
                  text={generateMinimalScript(
                    websiteData.trackingId,
                    websiteData.domain
                  )}
                  className="absolute top-2 right-2"
                />
              </div>
              <p className="text-sm text-muted-foreground">
                Lightweight version - only 4KB and loads asynchronously.
              </p>
            </div>

            {/* Test Installation */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">
                Test Your Installation
              </h4>
              <p className="text-sm text-blue-800 mb-3">
                After adding the tracking code, visit your website and check if
                events appear in your analytics dashboard.
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/dashboard/websites/${websiteData.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Analytics
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Provider Integration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Webhook className="h-5 w-5" />
            <span>Payment Provider Integration</span>
          </CardTitle>
          <CardDescription>
            Configure payment providers specifically for this website to track
            revenue and attribution.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PaymentProviderSetupWrapper
            websiteId={websiteData.id}
            websiteName={websiteData.name}
          />
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-600">Danger Zone</CardTitle>
          <CardDescription>
            These actions are irreversible. Please be careful.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg">
              <div>
                <h4 className="font-medium text-red-600">Delete Website</h4>
                <p className="text-sm text-muted-foreground">
                  Permanently delete this website and all its analytics data.
                  This action cannot be undone.
                </p>
              </div>
              <WebsiteDeletionDialog
                websiteId={websiteData.id}
                websiteName={websiteData.name}
                websiteDomain={websiteData.domain}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
