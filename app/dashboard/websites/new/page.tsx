"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PaymentProviderSetup } from "@/components/dashboard/payment-provider-setup";
import { createWebsiteAction } from "@/lib/db/actions";
import {
  ArrowLeft,
  Globe,
  Webhook,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function NewWebsitePage() {
  const [showPaymentSetup, setShowPaymentSetup] = useState(false);
  const [createdWebsiteId, setCreatedWebsiteId] = useState<string | null>(null);

  return (
    <div className="space-y-6">
      {/* <PERSON>er */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <div>
        <h1 className="text-3xl font-bold text-gray-900">Add New Website</h1>
        <p className="text-gray-600 mt-2">
          Start tracking analytics for your website
        </p>
      </div>

      {/* Form */}
      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>Website Information</span>
          </CardTitle>
          <CardDescription>
            Enter your website details to generate your tracking script.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form action={createWebsiteAction} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Website Name</Label>
              <Input
                id="name"
                name="name"
                placeholder="My Awesome Website"
                required
              />
              <p className="text-sm text-muted-foreground">
                A friendly name to identify your website in the dashboard.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="domain">Domain</Label>
              <Input
                id="domain"
                name="domain"
                type="text"
                inputMode="url"
                placeholder="example.com"
                pattern="^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$"
                title="Please enter the domain without protocol (e.g., example.com)."
                required
              />
              <p className="text-sm text-muted-foreground">
                Your website's domain without protocol (e.g., example.com).
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="timezone">Timezone</Label>
                <Select name="timezone" defaultValue="UTC">
                  <SelectTrigger>
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UTC">UTC</SelectItem>
                    <SelectItem value="America/New_York">
                      Eastern Time
                    </SelectItem>
                    <SelectItem value="America/Chicago">
                      Central Time
                    </SelectItem>
                    <SelectItem value="America/Denver">
                      Mountain Time
                    </SelectItem>
                    <SelectItem value="America/Los_Angeles">
                      Pacific Time
                    </SelectItem>
                    <SelectItem value="Europe/London">London</SelectItem>
                    <SelectItem value="Europe/Paris">Paris</SelectItem>
                    <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                    <SelectItem value="Asia/Shanghai">Shanghai</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select name="currency" defaultValue="USD">
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD - US Dollar</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                    <SelectItem value="GBP">GBP - British Pound</SelectItem>
                    <SelectItem value="JPY">JPY - Japanese Yen</SelectItem>
                    <SelectItem value="CNY">CNY - Chinese Yuan</SelectItem>
                    <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                    <SelectItem value="AUD">AUD - Australian Dollar</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">Next Steps</h4>
              <p className="text-sm text-blue-800">
                After creating your website, you'll receive a tracking script to
                add to your website's HTML. The script is only 4KB and won't
                slow down your site.
              </p>
            </div>

            <div className="flex space-x-4">
              <Button type="submit" className="flex-1">
                Create Website
              </Button>
              <Button type="button" variant="outline" asChild>
                <Link href="/dashboard">Cancel</Link>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Optional Payment Provider Setup */}
      <Card className="max-w-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Webhook className="h-5 w-5" />
              <CardTitle>Payment Provider Integration (Optional)</CardTitle>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowPaymentSetup(!showPaymentSetup)}
              className="flex items-center gap-2"
            >
              {showPaymentSetup ? (
                <>
                  <ChevronDown className="h-4 w-4" />
                  Hide
                </>
              ) : (
                <>
                  <ChevronRight className="h-4 w-4" />
                  Setup Payment Tracking
                </>
              )}
            </Button>
          </div>
          <CardDescription>
            Configure payment providers to automatically track revenue from this
            website. You can also set this up later in the website settings.
          </CardDescription>
        </CardHeader>
        {showPaymentSetup && (
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 className="font-medium text-blue-900 mb-2">
                  Revenue Tracking Setup
                </h4>
                <p className="text-sm text-blue-800">
                  Connect your payment providers to automatically track revenue,
                  conversions, and customer attribution. This helps you
                  understand which marketing channels drive the most valuable
                  customers.
                </p>
              </div>

              <PaymentProviderSetup
                websiteId={createdWebsiteId || undefined}
                onProviderConfigured={(provider) => {
                  console.log(`${provider} configured for new website`);
                }}
              />
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
