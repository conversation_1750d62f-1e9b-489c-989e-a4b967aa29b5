import { But<PERSON> } from "@/components/ui/button";
import { auth, signOut } from "@/lib/auth";
import { AppSidebar } from "@/components/app-sidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import {
  BarChart,
  DollarSign,
  Globe,
  LogOut,
  PlusCircle,
  Settings,
  Users,
} from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  if (!session?.user) {
    redirect("/auth/signin");
  }

  return (
    <SidebarProvider defaultOpen={false}>
      <AppSidebar />
      <SidebarInset>
        {/* Compact Header */}
        <header className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-b border-white/20 dark:border-slate-700/50 shadow-sm">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-14">
              {/* Logo and Sidebar Trigger */}
              <div className="flex items-center space-x-3">
                <SidebarTrigger className="mr-2" />
                <Link href="/dashboard" className="flex items-center space-x-2">
                  <BarChart className="h-6 w-6 text-blue-600" />
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    InstaSight
                  </span>
                </Link>
              </div>

              {/* User menu */}
              <div className="flex items-center space-x-3">
                <Button variant="outline" size="sm" asChild>
                  <Link href="/dashboard/websites/new">
                    <PlusCircle className="h-3 w-3 mr-1" />
                    Add Website
                  </Link>
                </Button>

                <div className="flex items-center space-x-2">
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {session.user.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {session.user.email}
                    </p>
                  </div>
                  <form
                    action={async () => {
                      "use server";
                      await signOut({ redirectTo: "/" });
                    }}
                  >
                    <Button type="submit" variant="ghost" size="sm">
                      <LogOut className="h-4 w-4" />
                    </Button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="flex-1 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-12">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
