import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { PaymentProviderManager } from "@/lib/payment-providers/manager";
import { WebhookManager } from "@/lib/payment-providers/webhook-manager";
import { PaymentCredentialsSecurity } from "@/lib/encryption/payment-vault";
import { db } from "@/lib/db";
import { websites } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

interface CredentialValidationResult {
  valid: boolean;
  error?: string;
  details?: string;
  accountInfo?: any;
}

/**
 * Validate payment provider credentials
 * get the account info from the payment provider such as account name, country, currency, etc by using the key from the credentials.
 * return account info from the payment provider to the client
 */
async function validateCredentials(
  provider: string,
  credentials: { type: string; value: string }[]
): Promise<CredentialValidationResult> {
  try {
    // Format validation first
    for (const cred of credentials) {
      if (
        !PaymentCredentialsSecurity.validateCredentialFormat(
          provider,
          cred.type,
          cred.value
        )
      ) {
        return {
          valid: false,
          error: `Invalid format for ${cred.type}`,
          details: `The ${cred.type} format is not valid for ${provider}`,
        };
      }
    }

    // Create temporary client for validation
    const credentialObjects = credentials.map((cred) => ({
      id: "temp",
      provider,
      credentialType: cred.type,
      value: cred.value,
      keyPrefix: "",
      isActive: true,
      lastValidated: null,
      validationStatus: "pending",
    }));

    let client;
    switch (provider) {
      case "stripe":
        const { StripeClient } = await import(
          "@/lib/payment-providers/stripe-client"
        );
        client = new StripeClient(credentialObjects);
        break;
      case "lemonsqueezy":
        // TODO: Implement LemonSqueezy client
        return { valid: true, accountInfo: { provider: "lemonsqueezy" } };
      case "polar":
        // TODO: Implement Polar client
        return { valid: true, accountInfo: { provider: "polar" } };
      default:
        return {
          valid: false,
          error: "Unsupported provider",
          details: `Provider ${provider} is not supported`,
        };
    }

    // Validate credentials with the provider
    const validation = await client.validateCredentials();

    if (!validation.valid) {
      return {
        valid: false,
        error: "API credentials rejected",
        details:
          validation.error ||
          "The provided credentials were rejected by the payment provider",
      };
    }

    return {
      valid: true,
      accountInfo: validation.accountInfo,
    };
  } catch (error: any) {
    console.error("Credential validation failed:", error);
    return {
      valid: false,
      error: "Connection failed",
      details: error.message || "Failed to connect to payment provider",
    };
  }
}

/**
 * Verify website access for the user
 */
async function verifyWebsiteAccess(websiteId: string, userId: string) {
  const [website] = await db
    .select()
    .from(websites)
    .where(eq(websites.id, websiteId))
    .limit(1);

  if (!website || website.userId !== userId) {
    throw new Error("Website not found or access denied");
  }

  return website;
}

/**
 * POST /api/payment-providers
 * Setup payment provider credentials
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { provider, credentials, websiteId } = body;

    // Validate required fields
    if (!provider || !credentials || !Array.isArray(credentials)) {
      return NextResponse.json(
        { error: "Missing required fields: provider, credentials" },
        { status: 400 }
      );
    }

    // Verify website access if websiteId is provided
    let website = null;
    if (websiteId) {
      website = await verifyWebsiteAccess(websiteId, session.user.id);
    }

    // Validate credentials with the payment provider
    // fetch account info from the payment provider if valid
    const validation = await validateCredentials(provider, credentials);
    if (!validation.valid) {
      return NextResponse.json(
        {
          error: validation.error,
          details: validation.details,
        },
        { status: 400 }
      );
    }

    // Store encrypted credentials to neon database, each credential type is a separate row such as secret_key, publishable_key, webhook_secret
    const storedCredentialIds = await PaymentProviderManager.storeCredentials(
      session.user.id,
      provider,
      credentials,
      websiteId
    );

    // Update validation status for stored credentials
    for (const credentialId of storedCredentialIds) {
      // update validation status to valid and last validated time
      await PaymentProviderManager.updateCredentialValidation(
        credentialId,
        "valid"
      );
    }

    // Setup webhook endpoints
    let webhookUrl = null;
    try {
      const webhookResult = await WebhookManager.setupWebhookEndpoints(
        session.user.id,
        provider,
        websiteId
      );
      webhookUrl = webhookResult.webhookUrl;
    } catch (error: any) {
      console.warn(`Webhook setup failed for ${provider}:`, error.message);
      // Don't fail the entire setup if webhook creation fails
    }

    // Generate audit log
    const auditLog = PaymentCredentialsSecurity.generateAuditLog(
      "credentials_stored",
      session.user.id,
      provider,
      credentials.map((c) => c.type).join(",")
    );
    console.log("Payment provider setup audit:", auditLog);

    return NextResponse.json({
      success: true,
      provider,
      credentialsStored: storedCredentialIds.length,
      webhookUrl,
      accountInfo: validation.accountInfo,
      message: "Payment provider configured successfully",
    });
  } catch (error: any) {
    console.error("Payment provider setup error:", error);
    return NextResponse.json(
      {
        error: "Setup failed",
        details: error.message || "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/payment-providers
 * Get configured payment providers for the user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const websiteId = searchParams.get("websiteId");

    // Verify website access if websiteId is provided
    if (websiteId) {
      await verifyWebsiteAccess(websiteId, session.user.id);
    }

    // Get configured providers
    const providers = await PaymentProviderManager.getUserProviders(
      session.user.id,
      websiteId || undefined
    );

    return NextResponse.json({
      providers,
      count: providers.length,
    });
  } catch (error: any) {
    console.error("Failed to get payment providers:", error);
    return NextResponse.json(
      {
        error: "Failed to get providers",
        details: error.message,
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/payment-providers
 * Delete payment provider credentials
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const provider = searchParams.get("provider");
    const websiteId = searchParams.get("websiteId");

    if (!provider) {
      return NextResponse.json(
        { error: "Missing required parameter: provider" },
        { status: 400 }
      );
    }

    // Verify website access if websiteId is provided
    if (websiteId) {
      await verifyWebsiteAccess(websiteId, session.user.id);
    }

    // Delete webhook endpoints first
    try {
      await WebhookManager.deleteWebhookEndpoint(
        session.user.id,
        provider,
        websiteId || undefined
      );
    } catch (error: any) {
      console.warn(`Failed to delete webhook for ${provider}:`, error.message);
    }

    // Delete credentials
    await PaymentProviderManager.deleteCredentials(
      session.user.id,
      provider,
      websiteId || undefined
    );

    // Generate audit log
    const auditLog = PaymentCredentialsSecurity.generateAuditLog(
      "credentials_deleted",
      session.user.id,
      provider,
      "all"
    );
    console.log("Payment provider deletion audit:", auditLog);

    return NextResponse.json({
      success: true,
      message: `${provider} integration removed successfully`,
    });
  } catch (error: any) {
    console.error("Failed to delete payment provider:", error);
    return NextResponse.json(
      {
        error: "Deletion failed",
        details: error.message,
      },
      { status: 500 }
    );
  }
}
