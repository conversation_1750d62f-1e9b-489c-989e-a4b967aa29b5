import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { PaymentProviderManager } from '@/lib/payment-providers/manager';

/**
 * GET /api/payment-providers/status
 * Check if a specific payment provider is configured for a user/website
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider');
    const websiteId = searchParams.get('websiteId');

    if (!provider) {
      return NextResponse.json(
        { error: 'Missing required parameter: provider' },
        { status: 400 }
      );
    }

    // Check provider configuration status
    const status = await PaymentProviderManager.isProviderConfigured(
      session.user.id,
      provider,
      websiteId || undefined
    );

    return NextResponse.json({
      provider,
      websiteId: websiteId || null,
      configured: status.configured,
      level: status.level,
    });
  } catch (error: any) {
    console.error('Failed to check provider status:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check provider status', 
        details: error.message 
      }, 
      { status: 500 }
    );
  }
}
