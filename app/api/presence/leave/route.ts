import { presenceDisconnect } from "@/lib/presence/client";
import { SECURITY_CONFIGS, withSecurity } from "@/lib/security/middleware";
import { NextRequest, NextResponse } from "next/server";

const handler = async (request: NextRequest) => {
  try {
    let sessionToken: string;

    // Handle both application/json and text/plain content types
    const contentType = request.headers.get("content-type") || "";

    if (contentType.includes("application/json")) {
      const body = await request.json();
      sessionToken = body?.sessionToken;
    } else {
      // Handle text/plain from sendBeacon
      const text = await request.text();
      try {
        const body = JSON.parse(text);
        sessionToken = body?.sessionToken;
      } catch {
        // If not JSON, assume the text itself is the sessionToken
        sessionToken = text;
      }
    }

    if (!sessionToken) {
      return NextResponse.json(
        { error: "Missing sessionToken" },
        { status: 400 }
      );
    }

    await presenceDisconnect({ sessionToken });
    return NextResponse.json({ ok: true });
  } catch (e) {
    console.error("presence leave error", e);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
};

export const POST = withSecurity(SECURITY_CONFIGS.presence)(handler);
