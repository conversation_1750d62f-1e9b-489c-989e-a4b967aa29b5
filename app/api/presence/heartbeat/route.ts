import { db } from "@/lib/db";
import { websites } from "@/lib/db/schema";
import { presenceHeartbeat } from "@/lib/presence/client";
import { SECURITY_CONFIGS, withSecurity } from "@/lib/security/middleware";
import { and, eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";

const handler = async (request: NextRequest) => {
  try {
    const body = await request.json();
    const trackingId: string = body?.trackingId;
    const sessionId: string = body?.sessionId;
    const visitorId: string | undefined = body?.visitorId;
    const interval: number = Number.parseInt(String(body?.interval || 600000));

    // 提取访客详细信息
    const visitorDetails = {
      url: body?.url,
      referrer: body?.referrer,
      device: body?.device,
      browser: body?.browser,
      os: body?.os,
      screen: body?.screen,
      timestamp: body?.timestamp,
    };

    // 提取地理位置信息（从请求头）
    const country =
      request.headers.get("cf-ipcountry") ||
      request.headers.get("x-vercel-ip-country") ||
      request.headers.get("x-country-code") ||
      undefined;

    const city =
      request.headers.get("cf-ipcity") ||
      request.headers.get("x-vercel-ip-city") ||
      undefined;

    if (!trackingId || !sessionId) {
      return NextResponse.json(
        { error: "Missing trackingId/sessionId" },
        { status: 400 }
      );
    }
    const site = await db
      .select()
      .from(websites)
      .where(and(eq(websites.trackingId, trackingId)))
      .limit(1);
    if (!site.length) {
      return NextResponse.json({ error: "Website not found" }, { status: 404 });
    }

    // Use trackingId as roomId; userId prefer authed user, else fall back to sessionId
    const roomId = site[0].trackingId;
    const userId = visitorId || sessionId;

    const result = await presenceHeartbeat({
      roomId,
      userId,
      sessionId,
      interval,
      country,
      city,
      ...visitorDetails,
    });
    return NextResponse.json({
      ok: true,
      sessionToken: result?.sessionToken,
      roomToken: result?.roomToken,
    });
  } catch (e) {
    console.error("presence heartbeat error", e);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
};

export const POST = withSecurity(SECURITY_CONFIGS.presence)(handler);

// CORS preflight with enhanced security (copied from events API)
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get("origin");

  // Log CORS requests for monitoring
  console.log("CORS preflight request:", {
    origin,
    userAgent: request.headers.get("user-agent"),
    timestamp: new Date().toISOString(),
  });

  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": origin || "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400",
      "Access-Control-Allow-Credentials": "false",
      Vary: "Origin",
    },
  });
}
