import cache, { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>acheTTL } from "@/lib/cache/redis";
import { db } from "@/lib/db";
import { events, type Website, websites } from "@/lib/db/schema";
// WebSocket/SSE removed in production; realtime uses polling JSON via /api/stream
import { SECURITY_CONFIGS, withSecurity } from "@/lib/security/middleware";
import { VALIDATION_SCHEMAS, validate } from "@/lib/security/validation";
import { trackEvent, formatEventForTinybird } from "@/lib/tinybird/events";
import { tinybirdClient } from "@/lib/tinybird/client";
import {
  isBot,
  parseUserAgent,
  validateTrackingData,
} from "@/lib/utils/tracking";
import { eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";

// Configurable write mode for events: "tinybird" | "dual" | "neon"
const EVENT_WRITE_MODE = (process.env.EVENT_WRITE_MODE || "tinybird") as
  | "tinybird"
  | "dual"
  | "neon";

// Apply security middleware with tracking-specific configuration
const securityHandler = withSecurity(SECURITY_CONFIGS.tracking);

// CORS helper: attach CORS headers to all responses
function withCors(request: NextRequest, response: NextResponse) {
  const origin = request.headers.get("origin") || "*";
  response.headers.set("Access-Control-Allow-Origin", origin);
  response.headers.set("Vary", "Origin");
  response.headers.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization"
  );
  response.headers.set("Access-Control-Allow-Credentials", "false");
  return response;
}

async function handlePOST(request: NextRequest) {
  try {
    // Robust body parsing: prefer JSON, fallback to text -> JSON (beacon/text/plain)
    const contentType = request.headers.get("content-type") || "";
    let data: any;
    try {
      if (contentType.includes("application/json")) {
        data = await request.json();
      } else {
        const textBody = await request.text();
        data = textBody ? JSON.parse(textBody) : {};
      }
    } catch (parseError) {
      console.warn("Event body parse failed, attempting fallback:", parseError);
      const textBody = await request.text();
      try {
        data = textBody ? JSON.parse(textBody) : {};
      } catch {
        return withCors(
          request,
          NextResponse.json({ error: "Invalid request body" }, { status: 400 })
        );
      }
    }

    // Enhanced validation using security schemas
    const validationResult = validate(data, VALIDATION_SCHEMAS.event);
    if (!validationResult.isValid) {
      console.warn("Event validation failed:", validationResult.errors);
      return withCors(
        request,
        NextResponse.json(
          { error: "Validation failed", details: validationResult.errors },
          { status: 400 }
        )
      );
    }

    // Use sanitized data
    const sanitizedData = validationResult.sanitizedData!;

    // Additional tracking data validation
    const trackingErrors = validateTrackingData(sanitizedData);
    if (trackingErrors.length > 0) {
      return NextResponse.json(
        { error: "Tracking validation failed", details: trackingErrors },
        { status: 400 }
      );
    }

    // Get user agent and check for bots with enhanced detection
    const userAgent = request.headers.get("user-agent") || "";
    if (isBot(userAgent)) {
      // Enhanced bot logging
      console.log("Bot request blocked:", {
        userAgent,
        ip: request.headers.get("x-forwarded-for") || "unknown",
        timestamp: new Date().toISOString(),
      });
      return withCors(request, NextResponse.json({ success: true })); // Return success to avoid detection
    }

    // Verify website exists and is active with cache to avoid DB on hot path
    const cacheKey = CacheKeys.websiteByTrackingId(sanitizedData.websiteId);
    let cachedWebsite: Website | null | undefined;

    // 先判断缓存是否存在，再读取，避免将“未命中”误判为“负缓存(null)”
    const hasCache = await cache.exists(cacheKey);
    if (hasCache) {
      cachedWebsite = await cache.get<Website | null>(cacheKey);
      if (cachedWebsite === null) {
        // negative cached: short-circuit 404
        console.log("Invalid or inactive website (negative cached)", cacheKey);
        return withCors(
          request,
          NextResponse.json(
            { error: "Invalid or inactive website" },
            { status: 404 }
          )
        );
      }
    } else {
      const result = await db
        .select()
        .from(websites)
        .where(eq(websites.trackingId, sanitizedData.websiteId))
        .limit(1);
      cachedWebsite = (result?.length ? result[0] : null) as Website | null;

      // populate positive/negative cache
      if (cachedWebsite?.isActive) {
        await cache.set(cacheKey, cachedWebsite, {
          ttl: CacheTTL.WEBSITE_LOOKUP,
        });
      } else {
        await cache.set(cacheKey, null, { ttl: CacheTTL.WEBSITE_NEGATIVE });
        return withCors(
          request,
          NextResponse.json(
            { error: "Invalid or inactive website" },
            { status: 404 }
          )
        );
      }
    }

    if (!cachedWebsite?.isActive) {
      // Log suspicious tracking attempts
      console.warn("Invalid tracking attempt:", {
        trackingId: sanitizedData.websiteId,
        ip: request.headers.get("x-forwarded-for") || "unknown",
        timestamp: new Date().toISOString(),
      });

      return withCors(
        request,
        NextResponse.json(
          { error: "Invalid or inactive website" },
          { status: 404 }
        )
      );
    }

    const websiteRow = cachedWebsite as Website;

    // Enhanced geographic data extraction with validation
    const ip =
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip") ||
      "unknown";

    const country =
      request.headers.get("cf-ipcountry") ||
      request.headers.get("x-vercel-ip-country") ||
      request.headers.get("x-country-code") ||
      null;

    const region =
      request.headers.get("cf-region") ||
      request.headers.get("x-vercel-ip-country-region") ||
      null;

    const city =
      request.headers.get("cf-ipcity") ||
      request.headers.get("x-vercel-ip-city") ||
      null;

    const timezone = request.headers.get("x-vercel-ip-timezone") || null;

    // Enhanced user agent parsing with security checks
    const { device, browser, os } = parseUserAgent(userAgent);

    // Additional security: Check for suspicious patterns in custom data
    if (sanitizedData.customData) {
      try {
        const customDataStr = JSON.stringify(sanitizedData.customData);

        // Check for suspicious patterns in custom data
        const suspiciousPatterns = [
          /<script/i,
          /javascript:/i,
          /on\w+=/i,
          /eval\(/i,
          /document\./i,
          /window\./i,
        ];

        const hasSuspiciousContent = suspiciousPatterns.some((pattern) =>
          pattern.test(customDataStr)
        );

        if (hasSuspiciousContent) {
          console.warn("Suspicious custom data detected:", {
            websiteId: sanitizedData.websiteId,
            ip,
            customData: sanitizedData.customData,
          });

          // Remove custom data but continue processing
          sanitizedData.customData = null;
        }
      } catch (error) {
        console.warn("Custom data validation error:", error);
        sanitizedData.customData = null;
      }
    }

    // Merge server-derived timezone into custom data (do not trust client-provided geo)
    const mergedCustomData =
      sanitizedData.customData && typeof sanitizedData.customData === "object"
        ? { ...sanitizedData.customData }
        : {};
    if (timezone) {
      (mergedCustomData as any).timezone = timezone;
    }

    // Insert event with enhanced error handling and performance optimization
    try {
      // Optimized write strategy based on EVENT_WRITE_MODE
      if (EVENT_WRITE_MODE === "dual") {
        // For dual mode: Write to Tinybird first (hot path), then Neon asynchronously
        const tinybirdPromise = tinybirdClient.sendEvent(
          formatEventForTinybird({
            websiteId: websiteRow.id,
            sessionId: sanitizedData.sessionId,
            visitorId: sanitizedData.visitorId,
            eventType: sanitizedData.eventType || "pageview",
            eventName: sanitizedData.eventName,
            url: sanitizedData.url,
            referrer: sanitizedData.referrer,
            userAgent,
            country,
            city,
            device,
            browser,
            os,
            utm_source: sanitizedData.utm_source,
            utm_medium: sanitizedData.utm_medium,
            utm_campaign: sanitizedData.utm_campaign,
            utm_content: sanitizedData.utm_content,
            utm_term: sanitizedData.utm_term,
            customData: mergedCustomData,
            revenue: sanitizedData.revenue,
            timestamp: new Date(),
          })
        );

        // Write to Neon in background (don't await to avoid blocking)
        const neonWrite = db.insert(events).values({
          websiteId: websiteRow.id,
          sessionId: sanitizedData.sessionId,
          visitorId: sanitizedData.visitorId,
          eventType: sanitizedData.eventType || "pageview",
          eventName: sanitizedData.eventName,
          url: sanitizedData.url,
          referrer: sanitizedData.referrer,
          userAgent,
          country,
          city,
          device,
          browser,
          os,
          utm_source: sanitizedData.utm_source,
          utm_medium: sanitizedData.utm_medium,
          utm_campaign: sanitizedData.utm_campaign,
          utm_content: sanitizedData.utm_content,
          utm_term: sanitizedData.utm_term,
          customData: mergedCustomData,
          revenue: sanitizedData.revenue
            ? sanitizedData.revenue.toString()
            : null,
          timestamp: new Date(sanitizedData.timestamp || Date.now()),
        });

        // Execute Tinybird write (priority) and handle Neon write in background
        await tinybirdPromise;

        // Handle Neon write result in background (don't block response)
        neonWrite.catch((error) => {
          console.error("Background Neon write failed:", error);
          // In production, you might want to queue this for retry
        });
      } else if (EVENT_WRITE_MODE === "neon") {
        // Neon-only mode: Write directly to Neon
        await db.insert(events).values({
          websiteId: websiteRow.id,
          sessionId: sanitizedData.sessionId,
          visitorId: sanitizedData.visitorId,
          eventType: sanitizedData.eventType || "pageview",
          eventName: sanitizedData.eventName,
          url: sanitizedData.url,
          referrer: sanitizedData.referrer,
          userAgent,
          country,
          city,
          device,
          browser,
          os,
          utm_source: sanitizedData.utm_source,
          utm_medium: sanitizedData.utm_medium,
          utm_campaign: sanitizedData.utm_campaign,
          utm_content: sanitizedData.utm_content,
          utm_term: sanitizedData.utm_term,
          customData: mergedCustomData,
          revenue: sanitizedData.revenue
            ? sanitizedData.revenue.toString()
            : null,
          timestamp: new Date(sanitizedData.timestamp || Date.now()),
        });
      } else {
        // Tinybird-only mode (default): Write only to Tinybird for maximum performance
        await tinybirdPromise;
      }

      // WebSocket/SSE removed; realtime updates are fetched via polling (/api/stream)

      return withCors(
        request,
        NextResponse.json({ success: true, mode: EVENT_WRITE_MODE })
      );
    } catch (dbError) {
      console.error("Database error:", dbError);
      return withCors(
        request,
        NextResponse.json({ error: "Failed to save event" }, { status: 500 })
      );
    }
  } catch (error) {
    console.error("Event tracking error:", error);
    return withCors(
      request,
      NextResponse.json({ error: "Internal server error" }, { status: 500 })
    );
  }
}

// Apply security middleware
export const POST = securityHandler(handlePOST);

// CORS preflight with enhanced security
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get("origin");

  // Log CORS requests for monitoring
  console.log("CORS preflight request:", {
    origin,
    userAgent: request.headers.get("user-agent"),
    timestamp: new Date().toISOString(),
  });

  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": origin || "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400",
      "Access-Control-Allow-Credentials": "false",
      Vary: "Origin",
    },
  });
}
