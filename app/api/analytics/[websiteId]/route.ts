import {
  getChartData,
  getDashboardOverview,
  getDeviceBreakdownData,
  getRealTimeVisitorsData,
  getTopPagesData,
  getTrafficSourcesData,
} from "@/lib/actions/dashboard"
import { auth } from "@/lib/auth"
import { NextRequest, NextResponse } from "next/server"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ websiteId: string }> }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { websiteId } = await params
    const searchParams = request.nextUrl.searchParams

    // Parse query parameters
    const metric =
      (searchParams.get("metric") as "visitors" | "pageviews" | "revenue") || "visitors"
    const interval = (searchParams.get("interval") as "hour" | "day" | "week" | "month") || "day"
    const fromDate = searchParams.get("from")
    const toDate = searchParams.get("to")
    const dataType = searchParams.get("type") // overview, chart, pages, sources, devices, realtime

    // Default to last 30 days if no date range provided
    const defaultTo = new Date()
    const defaultFrom = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

    const dateRange = {
      from: fromDate ? new Date(fromDate) : defaultFrom,
      to: toDate ? new Date(toDate) : defaultTo,
    }

    // Validate date range
    if (dateRange.from >= dateRange.to) {
      return NextResponse.json({ error: "Invalid date range" }, { status: 400 })
    }

    let data: any

    switch (dataType) {
      case "overview":
        data = await getDashboardOverview(websiteId, dateRange)
        break

      case "chart":
        data = await getChartData(websiteId, dateRange, metric, interval)
        break

      case "pages": {
        const limit = Number.parseInt(searchParams.get("limit") || "10")
        data = await getTopPagesData(websiteId, dateRange, limit)
        break
      }

      case "sources": {
        const sourcesLimit = Number.parseInt(searchParams.get("limit") || "10")
        data = await getTrafficSourcesData(websiteId, dateRange, sourcesLimit)
        break
      }

      case "devices":
        data = await getDeviceBreakdownData(websiteId, dateRange)
        break

      case "realtime":
        data = await getRealTimeVisitorsData(websiteId)
        break

      default: {
        // Return comprehensive dashboard data
        const [overview, chartData, topPages, sources, devices, realtime] = await Promise.all([
          getDashboardOverview(websiteId, dateRange),
          getChartData(websiteId, dateRange, metric, interval),
          getTopPagesData(websiteId, dateRange, 5),
          getTrafficSourcesData(websiteId, dateRange, 5),
          getDeviceBreakdownData(websiteId, dateRange),
          getRealTimeVisitorsData(websiteId),
        ])

        data = {
          overview,
          chart: chartData,
          topPages,
          sources,
          devices,
          realtime,
          dateRange,
          metric,
          interval,
        }
        break
      }
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error("Analytics API error:", error)

    if (error instanceof Error) {
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        return NextResponse.json({ error: "Website not found" }, { status: 404 })
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400",
    },
  })
}
