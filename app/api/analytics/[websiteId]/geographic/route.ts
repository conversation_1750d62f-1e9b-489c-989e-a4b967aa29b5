import { NextRequest, NextResponse } from "next/server";
import {
  getGeographicOverview,
  getGeographicCountries,
  getGeographicRegions,
  getGeographicCities,
  createAnalyticsFilters,
} from "@/lib/tinybird/analytics";
import { getCountryInfo } from "@/lib/utils/countries";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ websiteId: string }> }
) {
  try {
    const { websiteId } = await params;
    const { searchParams } = new URL(request.url);

    // Get date range from query parameters
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: "start_date and end_date are required" },
        { status: 400 }
      );
    }

    // Create analytics filters
    const filters = createAnalyticsFilters({
      websiteId,
      dateRange: {
        from: new Date(startDate),
        to: new Date(endDate),
      },
    });

    // Fetch all geographic data in parallel
    const [overview, countries, regions, cities] = await Promise.all([
      getGeographicOverview(filters),
      getGeographicCountries(filters, 50),
      getGeographicRegions(filters, 50),
      getGeographicCities(filters, 50),
    ]);

    // Process data for display
    const processedCountries = countries.map((country) => {
      const countryInfo = getCountryInfo(country.country);
      return {
        ...country,
        countryName: countryInfo.name,
        flagEmoji: countryInfo.flag,
      };
    });

    const processedRegions = regions.map((region) => {
      const countryInfo = getCountryInfo(region.country);
      return {
        ...region,
        countryName: countryInfo.name,
        flagEmoji: countryInfo.flag,
      };
    });

    const processedCities = cities.map((city) => {
      const countryInfo = getCountryInfo(city.country);
      return {
        ...city,
        countryName: countryInfo.name,
        flagEmoji: countryInfo.flag,
      };
    });

    // Create map data
    const mapData = processedCountries.map((country) => ({
      countryCode: country.country,
      countryName: country.countryName,
      visitors: country.unique_visitors,
      percentage: country.percentage,
      revenue: country.total_revenue,
    }));

    return NextResponse.json({
      overview,
      countries: processedCountries,
      regions: processedRegions,
      cities: processedCities,
      mapData,
    });
  } catch (error) {
    console.error("Failed to fetch geographic data:", error);
    return NextResponse.json(
      { error: "Failed to fetch geographic data" },
      { status: 500 }
    );
  }
}
