import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { websites } from "@/lib/db/schema"
import { tinybirdClient } from "@/lib/tinybird/client"
import { and, eq } from "drizzle-orm"
import { NextRequest, NextResponse } from "next/server"

// Verify user owns the website
async function verifyWebsiteAccess(websiteId: string, userId: string) {
  const website = await db
    .select()
    .from(websites)
    .where(and(eq(websites.id, websiteId), eq(websites.userId, userId)))
    .limit(1)

  if (!website.length) {
    throw new Error("Website not found or access denied")
  }

  return website[0]
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ websiteId: string }> }
) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { websiteId } = await params
    const searchParams = request.nextUrl.searchParams

    // Verify access
    const website = await verifyWebsiteAccess(websiteId, session.user.id)

    // Parse query parameters
    const fromDate = searchParams.get("from")
    const toDate = searchParams.get("to")
    const type = searchParams.get("type") || "sources" // sources, campaigns, channels

    // Default to last 30 days if no date range provided
    const defaultTo = new Date()
    const defaultFrom = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

    const dateRange = {
      start_date: fromDate ? new Date(fromDate).toISOString() : defaultFrom.toISOString(),
      end_date: toDate ? new Date(toDate).toISOString() : defaultTo.toISOString(),
    }

    let data: any

    switch (type) {
      case "sources":
        // Revenue attribution by traffic sources
        data = await tinybirdClient.getTrafficSources({
          website_id: website.trackingId,
          ...dateRange,
        })
        break

      case "campaigns":
        // Revenue attribution by UTM campaigns
        data = await tinybirdClient.getCampaignRevenue({
          website_id: website.trackingId,
          ...dateRange,
        })
        break

      case "channels":
        // Revenue attribution by marketing channels
        data = await tinybirdClient.getChannelRevenue({
          website_id: website.trackingId,
          ...dateRange,
        })
        break

      case "customer-journey":
        // Customer journey analysis
        data = await tinybirdClient.getCustomerJourney({
          website_id: website.trackingId,
          ...dateRange,
        })
        break

      case "ltv":
        // Customer Lifetime Value analysis
        data = await tinybirdClient.getCustomerLTV({
          website_id: website.trackingId,
          ...dateRange,
        })
        break

      case "revenue-trends": {
        // Revenue trends over time
        const interval = searchParams.get("interval") || "day"
        data = await tinybirdClient.getTimeSeriesData(
          {
            website_id: website.trackingId,
            ...dateRange,
          },
          "revenue",
          interval as "hour" | "day" | "week" | "month"
        )
        break
      }

      default:
        return NextResponse.json({ error: "Invalid revenue analysis type" }, { status: 400 })
    }

    // Add revenue attribution insights
    const insights = {
      totalRevenue: data?.reduce((sum: number, item: any) => sum + (item.revenue || 0), 0) || 0,
      topRevenueSource: data?.[0] || null,
      conversionRate:
        data?.reduce((sum: number, item: any) => sum + (item.conversion_rate || 0), 0) /
          (data?.length || 1) || 0,
      revenuePerVisitor:
        data?.reduce((sum: number, item: any) => sum + (item.revenue_per_visitor || 0), 0) /
          (data?.length || 1) || 0,
    }

    return NextResponse.json({
      data,
      insights,
      dateRange,
      type,
      website: {
        id: website.id,
        name: website.name,
        domain: website.domain,
      },
    })
  } catch (error) {
    console.error("Revenue attribution API error:", error)

    if (error instanceof Error) {
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        return NextResponse.json({ error: "Website not found" }, { status: 404 })
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400",
    },
  })
}
