import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { websites } from "@/lib/db/schema";
import { tinybirdClient } from "@/lib/tinybird/client";
import { getOnlineCountFromPresenceProvider } from "@/lib/presence";
import { and, eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";

/**
 * Format date for Tinybird DateTime parameters
 * Format: YYYY-MM-DD HH:MM:SS
 */
function formatDateForTinybird(date: Date): string {
  return date.toISOString().slice(0, 19).replace("T", " ");
}

// Verify user owns the website
async function verifyWebsiteAccess(websiteId: string, userId: string) {
  const website = await db
    .select()
    .from(websites)
    .where(and(eq(websites.id, websiteId), eq(websites.userId, userId)))
    .limit(1);

  if (!website.length) {
    throw new Error("Website not found or access denied");
  }

  return website[0];
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ websiteId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { websiteId } = await params;
    const searchParams = request.nextUrl.searchParams;

    // Verify access
    const website = await verifyWebsiteAccess(websiteId, session.user.id);

    // Parse query parameters
    const type = searchParams.get("type") || "visitors"; // visitors, events, revenue, overview
    const limit = Number.parseInt(searchParams.get("limit") || "50");

    let data: any;

    switch (type) {
      case "visitors":
        // Real-time visitors in the last 30 minutes
        data = await tinybirdClient.getRealtimeVisitors(
          website.trackingId,
          limit
        );
        break;

      case "events":
        // Real-time events (pageviews, conversions, etc.)
        data = await tinybirdClient.queryEndpoint("realtime_events", {
          website_id: website.trackingId,
          limit,
        });
        break;

      case "revenue":
        // Real-time revenue tracking
        data = await tinybirdClient.queryEndpoint("realtime_revenue", {
          website_id: website.trackingId,
          limit,
        });
        break;

      case "overview": {
        // Real-time overview metrics
        const [visitors, events, revenue, presenceCount] = await Promise.all([
          tinybirdClient.getRealtimeVisitors(website.trackingId, 100),
          tinybirdClient.queryEndpoint("realtime_events", {
            website_id: website.trackingId,
            limit: 100,
          }),
          tinybirdClient.queryEndpoint("realtime_revenue", {
            website_id: website.trackingId,
            limit: 100,
          }),
          getOnlineCountFromPresenceProvider(website.trackingId),
        ]);

        // Calculate real-time metrics
        const activeVisitors =
          (presenceCount ?? null) !== null
            ? (presenceCount as number)
            : visitors?.length || 0;
        const recentEvents = events?.length || 0;
        const recentRevenue =
          revenue?.reduce(
            (sum: number, item: any) => sum + (item.revenue || 0),
            0
          ) || 0;

        // Get current hour stats
        const now = new Date();
        const currentHour = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          now.getHours(),
          0,
          0
        );
        const hourlyStats = await tinybirdClient.getTimeSeriesData(
          {
            website_id: website.trackingId,
            start_date: formatDateForTinybird(currentHour),
            end_date: formatDateForTinybird(now),
          },
          "visitors",
          "hour"
        );

        data = {
          activeVisitors,
          recentEvents,
          recentRevenue,
          hourlyVisitors: hourlyStats?.[0]?.value || 0,
          visitors: visitors?.slice(0, 20) || [], // Latest 20 visitors
          events: events?.slice(0, 10) || [], // Latest 10 events
          revenue: revenue?.slice(0, 10) || [], // Latest 10 revenue events
          timestamp: new Date().toISOString(),
        };
        break;
      }

      case "heatmap":
        // Real-time page heatmap data
        data = await tinybirdClient.queryEndpoint("realtime_heatmap", {
          website_id: website.trackingId,
          limit,
        });
        break;

      case "locations":
        // Real-time visitor locations
        data = await tinybirdClient.queryEndpoint("realtime_locations", {
          website_id: website.trackingId,
          limit,
        });
        break;

      default:
        return NextResponse.json(
          { error: "Invalid realtime data type" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      data,
      type,
      website: {
        id: website.id,
        name: website.name,
        domain: website.domain,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Realtime API error:", error);

    if (error instanceof Error) {
      if (
        error.message.includes("not found") ||
        error.message.includes("access denied")
      ) {
        return NextResponse.json(
          { error: "Website not found" },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
      "Access-Control-Max-Age": "86400",
    },
  });
}
