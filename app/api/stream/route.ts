import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { websites } from "@/lib/db/schema";
import {
  getDefaultDateRange,
  getWebsiteOverview,
} from "@/lib/tinybird/analytics";
import { and, eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";
import {
  getOnlineCountFromPresenceProvider,
  getRealtimeVisitorsFromProvider,
} from "@/lib/presence";

// Polling JSON endpoint for real-time data (SSE removed)
export async function GET(request: NextRequest) {
  // Require authentication
  const session = await auth();
  if (!session?.user?.id) {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const websiteId = searchParams.get("websiteId"); // This should be the trackingId

  if (!websiteId) {
    return new Response("Missing websiteId parameter", { status: 400 });
  }

  // Verify that the authenticated user owns the website
  const website = await db
    .select()
    .from(websites)
    .where(
      and(
        eq(websites.trackingId, websiteId),
        eq(websites.userId, session.user.id)
      )
    )
    .limit(1);

  if (!website.length) {
    return new NextResponse("Website not found", { status: 404 });
  }

  // Poll-style JSON response (no SSE). Interval configured via env (ms)
  const trackingId = website[0].trackingId;
  const refreshIntervalMs = Number.parseInt(
    process.env.REALTIME_REFRESH_MS || "50000"
  );

  try {
    const dateRange = getDefaultDateRange();
    const [convexVisitors, overview, presenceCount] = await Promise.all([
      getRealtimeVisitorsFromProvider(trackingId),
      getWebsiteOverview({
        website_id: trackingId,
        start_date: dateRange.from.toISOString().split("T")[0],
        end_date: dateRange.to.toISOString().split("T")[0],
      }),
      getOnlineCountFromPresenceProvider(trackingId),
    ]);

    // 优先使用 Convex 访客数据，如果 Convex 未配置则返回空数组
    const realtimeVisitors = convexVisitors || [];
    const onlineCount =
      (presenceCount ?? null) !== null
        ? (presenceCount as number)
        : realtimeVisitors.length;

    const body = {
      timestamp: new Date().toISOString(),
      realtimeVisitors,
      overview: overview || {},
      onlineCount,
      refreshIntervalMs,
    };

    const res = NextResponse.json(body, {
      headers: {
        "Cache-Control": "no-store",
      },
    });
    return res;
  } catch (error) {
    console.error("Realtime poll fetch error:", error);
    return NextResponse.json(
      { error: "Failed to fetch data" },
      { status: 500 }
    );
  }
}

// No special OPTIONS handler needed for simple GET JSON
