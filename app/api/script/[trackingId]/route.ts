import { promises as fs } from "node:fs";
import {
  brotliCompressSync,
  constants as zlibConstants,
  deflateSync,
  gzipSync,
} from "node:zlib";
import path from "node:path";
import { getWebsiteByTrackingId } from "@/lib/db/queries";
import { detectEnvironment, getApiUrl, getToken } from "@/lib/tinybird/client";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ trackingId: string }> }
) {
  try {
    const { trackingId } = await params;

    // Verify the tracking ID exists
    const website = await getWebsiteByTrackingId(trackingId);
    if (!website || !website.isActive) {
      return new NextResponse("Website not found or inactive", { status: 404 });
    }

    // 检测环境并选择合适的脚本
    const environment = detectEnvironment();
    const scriptMode = request.nextUrl.searchParams.get("mode") || "default";

    let scriptPath: string;
    if (scriptMode === "direct") {
      // 直接发送到 Tinybird (用于 CDN 分发)
      scriptPath = path.join(process.cwd(), "public", "script-tinybird.js");
    } else if (scriptMode === "dynamic") {
      // 动态环境检测脚本
      scriptPath = path.join(process.cwd(), "public", "script-dynamic.js");
    } else if (scriptMode === "4kb" || scriptMode === "mini") {
      // 4KB 轻量级版本
      scriptPath = path.join(process.cwd(), "public", "script-4kb.js");
    } else {
      // 默认：通过 API 代理
      scriptPath = path.join(
        process.cwd(),
        "public",
        process.env.NODE_ENV === "production" ? "script.min.js" : "script.js"
      );
    }

    let script = await fs.readFile(scriptPath, "utf-8");

    const appUrl =
      process.env.NODE_ENV === "production"
        ? process.env.NEXT_PUBLIC_APP_URL
        : "http://localhost:3000";

    // 根据脚本模式注入不同的配置
    if (scriptMode === "direct" || scriptMode === "dynamic") {
      // 对于直接模式和动态模式，注入 Tinybird 配置
      const tinybirdApiUrl = getApiUrl(environment);
      const tinybirdToken = getToken(environment);

      script = script.replace(
        '"https://api.tinybird.co/v0/events?name=events"',
        `"${tinybirdApiUrl}/v0/events?name=events"`
      );

      // 注入配置参数
      const configInjection = `
      // Auto-injected configuration for tracking ID: ${trackingId}
      const autoConfig = {
        websiteId: "${trackingId}",
        domain: "${website.domain}",
        environment: "${environment}",
        tinybirdToken: "${tinybirdToken}",
        debug: ${environment === "local"},
      };

      // 更新配置对象
      Object.assign(config, autoConfig);
      `;

      script = script.replace("// 配置", `${configInjection}\n  // 配置`);
    } else if (scriptMode === "4kb" || scriptMode === "mini") {
      // 4KB 轻量级脚本配置

      const config = {
        endpoint: `${appUrl}/api/events`,
        websiteId: trackingId,
        domain: website.domain,
        debug: environment === "local",
      };

      // 注入 4KB 脚本配置
      script = script.replace(
        'e: s?.getAttribute("data-endpoint") || "/api/events"',
        `e: s?.getAttribute("data-endpoint") || "${config.endpoint}"`
      );
      script = script.replace(
        'w: s?.getAttribute("data-website-id")',
        `w: s?.getAttribute("data-website-id") || "${config.websiteId}"`
      );
      script = script.replace(
        'd: s?.getAttribute("data-domain")',
        `d: s?.getAttribute("data-domain") || "${config.domain}"`
      );
      script = script.replace(
        'g: s?.getAttribute("data-debug") === "true"',
        `g: s?.getAttribute("data-debug") === "true" || ${config.debug}`
      );
    } else {
      // 默认代理模式配置
      const config = {
        endpoint: `${appUrl}/api/events`,
        websiteId: trackingId,
        domain: website.domain,
        auto: true,
        debug: environment === "local",
      };

      // Replace the config placeholder in the script
      script = script.replace(
        'websiteId: document.currentScript?.getAttribute("data-website-id")',
        `websiteId: "${config.websiteId}"`
      );
      script = script.replace(
        'domain: document.currentScript?.getAttribute("data-domain")',
        `domain: "${config.domain}"`
      );
      script = script.replace(
        'endpoint: "/api/events"',
        `endpoint: "${config.endpoint}"`
      );
      script = script.replace(
        'debug: document.currentScript?.getAttribute("data-debug") === "true"',
        `debug: ${config.debug}`
      );
    }

    // Content negotiation: prefer br > gzip > deflate when accepted by client
    const accept = (request.headers.get("accept-encoding") || "").toLowerCase();
    const baseHeaders: Record<string, string> = {
      "Content-Type": "application/javascript",
      "Cache-Control": "public, max-age=3600",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET",
      "X-InstaSight-Environment": environment,
      "X-InstaSight-Script-Mode": scriptMode,
      "X-InstaSight-Version": "2.0.0",
      Vary: "Accept-Encoding",
    };

    if (accept.includes("br")) {
      const br = brotliCompressSync(Buffer.from(script, "utf-8"), {
        params: {
          [zlibConstants.BROTLI_PARAM_QUALITY]: 5, // good balance for on-the-fly compression
        },
      });
      return new NextResponse(br as any, {
        headers: {
          ...baseHeaders,
          "Content-Encoding": "br",
        },
      });
    }

    if (accept.includes("gzip")) {
      const gz = gzipSync(Buffer.from(script, "utf-8"));
      return new NextResponse(gz as any, {
        headers: {
          ...baseHeaders,
          "Content-Encoding": "gzip",
        },
      });
    }

    if (accept.includes("deflate")) {
      const df = deflateSync(Buffer.from(script, "utf-8"));
      return new NextResponse(df as any, {
        headers: {
          ...baseHeaders,
          "Content-Encoding": "deflate",
        },
      });
    }

    return new NextResponse(script, { headers: baseHeaders });
  } catch (error) {
    console.error("Script generation error:", error);
    return new NextResponse("Internal server error", { status: 500 });
  }
}
