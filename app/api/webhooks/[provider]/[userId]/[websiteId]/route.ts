import { NextRequest, NextResponse } from "next/server";
import { PaymentProviderManager } from "@/lib/payment-providers/manager";
import { WebhookManager } from "@/lib/payment-providers/webhook-manager";
import { trackEvent } from "@/lib/tinybird/events";
import { db } from "@/lib/db";
import { revenueEvents, websites } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import crypto from "crypto";

interface RevenueEventData {
  websiteId: string;
  sessionId?: string;
  visitorId?: string;
  provider: string;
  amount: number;
  currency: string;
  transactionId: string;
  customerEmail?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

/**
 * since there are multiple payment provider, we need to detect if the transaction is unique and not a duplicate.
 * and each payment provider client has its own way to detect if the transaction is unique and not a duplicate. we may update this later to be more generic.
 * Generate deduplication ID for revenue events.
 * this is used to detect if the transaction is unique and not a duplicate.
 */
function generateDeduplicationId(revenueData: any): string {
  const components = [
    revenueData.provider,
    revenueData.transactionId,
    revenueData.amount,
    revenueData.currency,
    Math.floor(new Date(revenueData.timestamp).getTime() / (5 * 60 * 1000)), // 5-minute window
  ];

  return crypto.createHash("sha256").update(components.join("|")).digest("hex");
}

/**
 * Check for duplicate revenue events
 */
async function checkDuplicate(deduplicationId: string): Promise<boolean> {
  try {
    const [existing] = await db
      .select()
      .from(revenueEvents)
      .where(eq(revenueEvents.deduplicationId, deduplicationId))
      .limit(1);

    return !!existing;
  } catch (error) {
    console.error("Failed to check for duplicate:", error);
    return false;
  }
}

/**
 * Store revenue event for audit and deduplication
 */
async function storeRevenueEvent(
  userId: string,
  revenueData: RevenueEventData,
  deduplicationId: string
): Promise<void> {
  try {
    await db.insert(revenueEvents).values({
      websiteId: revenueData.websiteId,
      userId,
      provider: revenueData.provider,
      transactionId: revenueData.transactionId,
      sessionId: revenueData.sessionId || null,
      visitorId: revenueData.visitorId || null,
      amount: revenueData.amount.toString(),
      currency: revenueData.currency,
      customerEmail: revenueData.customerEmail || null,
      source: "webhook",
      deduplicationId,
      metadata: JSON.stringify(revenueData.metadata || {}),
    });
  } catch (error) {
    console.error("Failed to store revenue event:", error);
    // Don't throw - we still want to process the event even if storage fails
  }
}

/**
 * Send revenue event to analytics system
 */
async function sendRevenueToAnalytics(
  userId: string,
  websiteId: string,
  revenueData: RevenueEventData
): Promise<void> {
  try {
    // Get the specific website for tracking ID
    const [website] = await db
      .select()
      .from(websites)
      .where(and(eq(websites.id, websiteId), eq(websites.userId, userId)))
      .limit(1);

    if (!website) {
      console.warn(
        `Website ${websiteId} not found for user ${userId}, skipping analytics`
      );
      return;
    }

    // Extract attribution data from metadata
    const metadata = revenueData.metadata || {};
    const visitorId =
      metadata.instasight_visitor_id ||
      revenueData.visitorId ||
      crypto.randomUUID();
    const sessionId =
      metadata.instasight_session_id ||
      revenueData.sessionId ||
      crypto.randomUUID();

    // Send revenue event to Tinybird
    await trackEvent({
      websiteId: website.trackingId,
      sessionId,
      visitorId,
      eventType: "payment",
      eventName: "revenue",
      url: metadata.return_url || metadata.success_url || "",
      referrer: metadata.referrer || "",
      revenue: revenueData.amount,
      customData: {
        currency: revenueData.currency,
        provider: revenueData.provider,
        transactionId: revenueData.transactionId,
        customerEmail: revenueData.customerEmail,
        // UTM parameters for attribution
        utm_source: metadata.utm_source,
        utm_medium: metadata.utm_medium,
        utm_campaign: metadata.utm_campaign,
        utm_content: metadata.utm_content,
        utm_term: metadata.utm_term,
        // Additional metadata
        ...metadata,
      },
      timestamp: revenueData.timestamp,
    });

    console.log(
      `Revenue event sent to analytics: ${revenueData.provider} - $${revenueData.amount} ${revenueData.currency} for website ${website.name}`
    );
  } catch (error) {
    console.error("Failed to send revenue to analytics:", error);
    throw error;
  }
}

/**
 * Process webhook revenue event
 */
async function processWebhookRevenue(
  userId: string,
  provider: string,
  websiteId: string,
  webhookData: any
): Promise<void> {
  try {
    // Get user's payment provider client for the specific website
    const client = await PaymentProviderManager.createProviderClient(
      userId,
      provider,
      websiteId === "global" ? undefined : websiteId
    );

    // Extract the revenue data from the webhook, example , extract the data from the stripe, lemonsqueezy, polar, etc.
    // the data contains the amount, currency, transaction id, customer email, metadata, timestamp, etc.
    // the metadata contains the session id, visitor id, utm parameters, etc.
    // the metadata session ,visitor will be used to attribute the revenue to the correct user. That is, the documentation will ask the website own to include the instasight_visitor_id and instasight_session_id in the metadata.
    // Extract revenue data from webhook
    const revenueData = await client.extractRevenueFromEvent(webhookData);

    if (!revenueData) {
      console.log(`No revenue data extracted from ${provider} webhook`);
      return;
    }

    // Generate deduplication ID
    // this is used to detect if the transaction is unique and not a duplicate.
    // each payment provider has its own way to detect if the transaction is unique and not a duplicate. we may update this later to be more generic.
    // currently, we are using the transaction id, amount, currency, and timestamp to generate the deduplication id.
    const deduplicationId = generateDeduplicationId(revenueData);

    // Check for duplicates
    const isDuplicate = await checkDuplicate(deduplicationId);
    if (isDuplicate) {
      console.log(`Duplicate revenue event detected: ${deduplicationId}`);
      return;
    }

    // Prepare revenue event data with correct websiteId
    const revenueEventData: RevenueEventData = {
      websiteId: websiteId === "global" ? "" : websiteId, // Will be resolved in sendRevenueToAnalytics
      provider: revenueData.provider,
      amount: revenueData.amount,
      currency: revenueData.currency,
      transactionId: revenueData.transactionId,
      customerEmail: revenueData.customerEmail,
      metadata: revenueData.metadata,
      timestamp: revenueData.timestamp,
    };

    // Store for audit and deduplication
    await storeRevenueEvent(userId, revenueEventData, deduplicationId);

    // Send to analytics system
    await sendRevenueToAnalytics(userId, websiteId, revenueEventData);

    // Update webhook last triggered timestamp
    await WebhookManager.updateWebhookLastTriggered(
      userId,
      provider,
      websiteId === "global" ? undefined : websiteId
    );

    console.log(
      `Successfully processed ${provider} webhook for user ${userId}, website ${websiteId}: $${revenueData.amount} ${revenueData.currency}`
    );
  } catch (error) {
    console.error(
      `Webhook revenue processing failed for ${userId}/${provider}/${websiteId}:`,
      error
    );
    throw error;
  }
}

/**
 * Enhanced webhook signature verification with security best practices
 */
async function verifyWebhookSecurity(
  request: NextRequest,
  provider: string,
  body: string
): Promise<{ signature: string; headers: Record<string, string> }> {
  // Extract all relevant headers for security analysis
  const headers = {
    userAgent: request.headers.get("user-agent") || "",
    contentType: request.headers.get("content-type") || "",
    contentLength: request.headers.get("content-length") || "",
    host: request.headers.get("host") || "",
    forwarded: request.headers.get("x-forwarded-for") || "",
  };

  // Get signature from headers (different providers use different header names)
  const signature =
    request.headers.get("stripe-signature") ||
    request.headers.get("x-signature") ||
    request.headers.get("x-polar-signature") ||
    request.headers.get("x-lemonsqueezy-signature") ||
    request.headers.get("paypal-transmission-sig");

  if (!signature) {
    throw new Error(`No signature found in webhook headers for ${provider}`);
  }

  // Validate content type
  const contentType = request.headers.get("content-type");
  if (!contentType || !contentType.includes("application/json")) {
    throw new Error("Invalid content type - expected application/json");
  }

  // Validate body size (prevent DoS attacks)
  const maxBodySize = 1024 * 1024; // 1MB limit
  if (body.length > maxBodySize) {
    throw new Error("Request body too large");
  }

  // Validate body is valid JSON
  try {
    JSON.parse(body);
  } catch (error) {
    throw new Error("Invalid JSON in request body");
  }

  return { signature, headers };
}

/**
 * POST /api/webhooks/[provider]/[userId]/[websiteId]
 * Process payment provider webhooks for specific website with enhanced security
 */
export async function POST(
  request: NextRequest,
  {
    params,
  }: { params: { provider: string; userId: string; websiteId: string } }
) {
  const startTime = Date.now();
  let securityHeaders: Record<string, string> = {};

  try {
    const { provider, userId, websiteId } = params;

    // Validate provider
    const supportedProviders = ["stripe", "lemonsqueezy", "polar", "paypal"];
    if (!supportedProviders.includes(provider)) {
      return NextResponse.json(
        { error: "Unsupported provider" },
        { status: 400 }
      );
    }

    // Get raw body for signature verification
    const body = await request.text();

    // Enhanced security verification
    const { signature, headers } = await verifyWebhookSecurity(
      request,
      provider,
      body
    );
    securityHeaders = headers;

    // Get user's payment provider client for webhook verification
    // the user id , provider, and the website id is used to fetch the credentials from the database.
    // each website has its own credentials, so the websiteId is used to fetch the correct credentials.
    const client = await PaymentProviderManager.createProviderClient(
      userId,
      provider,
      websiteId === "global" ? undefined : websiteId
    );

    // Verify webhook signature and parse event with enhanced security
    // this is ensure the webhook is coming from the payment provider and not a malicious actor.
    const event = await client.verifyWebhook(body, signature);

    // Process the webhook and extract revenue data
    await processWebhookRevenue(userId, provider, websiteId, event);

    // Log successful webhook processing with timing
    const processingTime = Date.now() - startTime;
    console.log(
      `Webhook processed successfully in ${processingTime}ms: ${provider}/${userId}/${websiteId} - ${event.type || event.event_type || event.meta?.event_name || "unknown_event"}`
    );

    return NextResponse.json({
      received: true,
      provider,
      userId,
      websiteId,
      eventType:
        event.type || event.event_type || event.meta?.event_name || "unknown",
      timestamp: new Date().toISOString(),
      processingTime: `${processingTime}ms`,
    });
  } catch (error: any) {
    const processingTime = Date.now() - startTime;

    // Enhanced error logging with security context (without exposing sensitive data)
    const errorLog = {
      provider: params.provider,
      userId: params.userId,
      websiteId: params.websiteId,
      error: error.message,
      errorType: error.constructor.name,
      timestamp: new Date().toISOString(),
      processingTime: `${processingTime}ms`,
      securityContext: {
        hasUserAgent: !!securityHeaders.userAgent,
        contentType: securityHeaders.contentType,
        contentLength: securityHeaders.contentLength,
        hasForwardedFor: !!securityHeaders.forwarded,
      },
    };

    console.error("Webhook processing failed:", errorLog);

    // Return appropriate error status based on error type
    let statusCode = 400;
    if (
      error.message.includes("signature verification failed") ||
      error.message.includes("Invalid webhook signature")
    ) {
      statusCode = 401; // Unauthorized
    } else if (
      error.message.includes("not configured") ||
      error.message.includes("not found")
    ) {
      statusCode = 404; // Not found
    } else if (
      error.message.includes("too large") ||
      error.message.includes("Invalid JSON")
    ) {
      statusCode = 400; // Bad request
    }

    return NextResponse.json(
      {
        error: "Webhook processing failed",
        provider: params.provider,
        timestamp: new Date().toISOString(),
      },
      { status: statusCode }
    );
  }
}

/**
 * GET /api/webhooks/[provider]/[userId]/[websiteId]
 * Get webhook endpoint status (for debugging)
 */
export async function GET(
  _request: NextRequest,
  {
    params,
  }: { params: { provider: string; userId: string; websiteId: string } }
) {
  try {
    const { provider, userId, websiteId } = params;

    // Get webhook endpoint configuration
    const webhookConfig = await WebhookManager.getWebhookEndpoint(
      userId,
      provider,
      websiteId === "global" ? undefined : websiteId
    );

    if (!webhookConfig) {
      return NextResponse.json(
        { error: "Webhook endpoint not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      provider,
      userId,
      websiteId,
      webhookUrl: webhookConfig.webhookUrl,
      isActive: webhookConfig.isActive,
      events: webhookConfig.events,
      lastTriggered: webhookConfig.lastTriggered,
    });
  } catch (error: any) {
    console.error(
      `Failed to get webhook status for ${params.provider}/${params.userId}/${params.websiteId}:`,
      error
    );
    return NextResponse.json(
      { error: "Failed to get webhook status" },
      { status: 500 }
    );
  }
}
