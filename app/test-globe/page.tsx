"use client";

import DataFastStyleGlobe from "@/components/3d/datafast-style-globe";

// Static test data for the globe
const testVisitors = [
  {
    id: "visitor-1",
    userId: "user-1",
    country: "United States",
    city: "New York",
    lat: 40.7128,
    lng: -74.0060,
    timestamp: new Date(Date.now() - 1000 * 60 * 2).toISOString(),
    url: "/pricing",
    referrer: "https://google.com",
    device: "Desktop",
    browser: "Chrome",
  },
  {
    id: "visitor-2",
    userId: "user-2",
    country: "United Kingdom",
    city: "London",
    lat: 51.5074,
    lng: -0.1278,
    timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
    url: "/features",
    referrer: "https://twitter.com",
    device: "Mobile",
    browser: "Safari",
  },
  {
    id: "visitor-3",
    userId: "user-3",
    country: "Japan",
    city: "Tokyo",
    lat: 35.6762,
    lng: 139.6503,
    timestamp: new Date(Date.now() - 1000 * 60 * 1).toISOString(),
    url: "/dashboard",
    referrer: "Direct",
    device: "Desktop",
    browser: "Firefox",
  },
  {
    id: "visitor-4",
    userId: "user-4",
    country: "Germany",
    city: "Berlin",
    lat: 52.5200,
    lng: 13.4050,
    timestamp: new Date(Date.now() - 1000 * 60 * 8).toISOString(),
    url: "/about",
    referrer: "https://linkedin.com",
    device: "Tablet",
    browser: "Chrome",
  },
  {
    id: "visitor-5",
    userId: "user-5",
    country: "Australia",
    city: "Sydney",
    lat: -33.8688,
    lng: 151.2093,
    timestamp: new Date(Date.now() - 1000 * 60 * 3).toISOString(),
    url: "/contact",
    referrer: "https://facebook.com",
    device: "Mobile",
    browser: "Safari",
  },
  {
    id: "visitor-6",
    userId: "user-6",
    country: "Brazil",
    city: "São Paulo",
    lat: -23.5505,
    lng: -46.6333,
    timestamp: new Date(Date.now() - 1000 * 60 * 7).toISOString(),
    url: "/blog",
    referrer: "https://reddit.com",
    device: "Desktop",
    browser: "Edge",
  },
  {
    id: "visitor-7",
    userId: "user-7",
    country: "Canada",
    city: "Toronto",
    lat: 43.6532,
    lng: -79.3832,
    timestamp: new Date(Date.now() - 1000 * 60 * 4).toISOString(),
    url: "/pricing",
    referrer: "https://bing.com",
    device: "Desktop",
    browser: "Chrome",
  },
  {
    id: "visitor-8",
    userId: "user-8",
    country: "India",
    city: "Mumbai",
    lat: 19.0760,
    lng: 72.8777,
    timestamp: new Date(Date.now() - 1000 * 60 * 6).toISOString(),
    url: "/features",
    referrer: "https://duckduckgo.com",
    device: "Mobile",
    browser: "Chrome",
  },
];

export default function TestGlobePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-white">
              <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                🌍
              </div>
              <span className="font-semibold">InstaSight Globe Test</span>
            </div>
          </div>
          <div className="flex items-center space-x-6 text-white/80">
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-sm">Live</span>
            </div>
            <div className="text-sm">
              {testVisitors.length} Active Visitors
            </div>
          </div>
        </div>
      </div>

      {/* Globe */}
      <DataFastStyleGlobe visitors={testVisitors} />
    </div>
  );
}
