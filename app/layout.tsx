import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "mapbox-gl/dist/mapbox-gl.css";
import AuthSessionProvider from "@/components/auth/session-provider";
import ConvexClientProvider from "./ConvexClientProvider";
import { Toaster } from "sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "InstaSight - Revenue-Driven Analytics",
  description:
    "Turn your website visitors into revenue insights with InstaSight analytics",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const hasConvexUrl = !!process.env.NEXT_PUBLIC_CONVEX_URL;

  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthSessionProvider>
          {hasConvexUrl ? (
            <ConvexClientProvider>{children}</ConvexClientProvider>
          ) : (
            children
          )}
        </AuthSessionProvider>
        <Toaster richColors position="top-right" />
      </body>
    </html>
  );
}
