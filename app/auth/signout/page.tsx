import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { signOut } from "@/lib/auth"
import { Bar<PERSON><PERSON>, LogOut } from "lucide-react"
import Link from "next/link"

export default function SignOutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="flex items-center justify-center space-x-2 mb-8">
          <BarChart className="h-8 w-8 text-blue-600" />
          <span className="text-2xl font-bold text-gray-900">InstaSight</span>
        </div>

        <Card>
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl">Sign Out</CardTitle>
            <CardDescription>
              Are you sure you want to sign out of your InstaSight account?
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <form
              action={async () => {
                "use server"
                await signOut({ redirectTo: "/" })
              }}
              className="space-y-4"
            >
              <Button type="submit" className="w-full">
                <LogOut className="h-4 w-4 mr-2" />
                Yes, Sign Out
              </Button>
            </form>

            <Button variant="outline" className="w-full" asChild>
              <Link href="/dashboard">Cancel</Link>
            </Button>
          </CardContent>
        </Card>

        <p className="text-center text-sm text-muted-foreground mt-4">
          You'll be redirected to the homepage after signing out.
        </p>
      </div>
    </div>
  )
}
