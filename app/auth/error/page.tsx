import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertTriangle, <PERSON><PERSON>hart } from "lucide-react"
import Link from "next/link"

interface ErrorPageProps {
  searchParams: Promise<{ error?: string }>
}

export default async function AuthErrorPage({ searchParams }: ErrorPageProps) {
  const { error } = await searchParams

  const getErrorMessage = (error?: string) => {
    switch (error) {
      case "Configuration":
        return "There is a problem with the server configuration."
      case "AccessDenied":
        return "You do not have permission to sign in."
      case "Verification":
        return "The verification token has expired or has already been used."
      case "Default":
        return "Unable to sign in."
      default:
        return "An unexpected error occurred during authentication."
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="flex items-center justify-center space-x-2 mb-8">
          <BarChart className="h-8 w-8 text-blue-600" />
          <span className="text-2xl font-bold text-gray-900">InstaSight</span>
        </div>

        <Card>
          <CardHeader className="space-y-1 text-center">
            <div className="flex justify-center mb-4">
              <div className="h-16 w-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </div>
            <CardTitle className="text-2xl">Authentication Error</CardTitle>
            <CardDescription>{getErrorMessage(error)}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
              <div className="text-sm text-red-800">
                <p className="font-medium">What can you do?</p>
                <ul className="mt-2 list-disc list-inside space-y-1">
                  <li>Try signing in again</li>
                  <li>Check that your email is correct</li>
                  <li>Contact support if the problem persists</li>
                </ul>
              </div>
            </div>

            <Button className="w-full" asChild>
              <Link href="/auth/signin">Try Again</Link>
            </Button>

            <Button variant="outline" className="w-full" asChild>
              <Link href="/">Back to Home</Link>
            </Button>
          </CardContent>
        </Card>

        <p className="text-center text-sm text-muted-foreground mt-4">
          Need help?{" "}
          <Link href="/support" className="underline underline-offset-4 hover:text-primary">
            Contact Support
          </Link>
        </p>
      </div>
    </div>
  )
}
