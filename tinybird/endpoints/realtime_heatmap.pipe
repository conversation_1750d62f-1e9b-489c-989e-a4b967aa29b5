#
# 实时页面热力图端点
# 显示当前最受欢迎的页面和用户活动

TOKEN "dashboard" READ

NODE page_activity
SQL >
    % SELECT 
        url,
        count() as total_hits,
        uniq(visitor_id) as unique_visitors,
        uniq(session_id) as sessions,
        sum(revenue) as revenue,
        max(timestamp) as last_activity,
        round(avg(
            CASE 
                WHEN event_type = 'pageview' THEN 1 
                ELSE 0 
            END
        ) * 100, 2) as pageview_percentage
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= now() - INTERVAL 30 MINUTE
    GROUP BY url
    ORDER BY total_hits DESC
    LIMIT {{ Int32(limit, 50) }}

NODE endpoint
SQL >
    % SELECT 
        url,
        total_hits,
        unique_visitors,
        sessions,
        revenue,
        pageview_percentage,
        formatDateTime(last_activity, '%Y-%m-%d %H:%M:%S') as last_activity
    FROM page_activity
