DESCRIPTION >
    时间序列数据端点 - 支持按小时/天/周/月聚合的访客、页面浏览、收入数据

NODE timeseries_query
SQL >
    %
    SELECT
        {% if String(interval, 'day') == 'hour' %}
            toStartOfHour(timestamp) as date,
        {% elif String(interval, 'day') == 'week' %}
            toMonday(timestamp) as date,
        {% elif String(interval, 'day') == 'month' %}
            toStartOfMonth(timestamp) as date,
        {% else %}
            toStartOfDay(timestamp) as date,
        {% end %}
        {% if String(metric, 'visitors') == 'visitors' %}
            uniq(visitor_id) as value
        {% elif String(metric, 'visitors') == 'pageviews' %}
            countIf(event_type = 'pageview') as value
        {% elif String(metric, 'visitors') == 'revenue' %}
            round(sum(revenue), 2) as value
        {% else %}
            count() as value
        {% end %}
    FROM events
    WHERE website_id = {{String(website_id)}}
      AND timestamp >= {{DateTime(start_date)}}
      AND timestamp <= {{DateTime(end_date)}}
      {% if defined(country) %}
      AND country = {{String(country)}}
      {% end %}
      {% if defined(device) %}
      AND device = {{String(device)}}
      {% end %}
      {% if defined(browser) %}
      AND browser = {{String(browser)}}
      {% end %}
      {% if defined(utm_source) %}
      AND utm_source = {{String(utm_source)}}
      {% end %}
    GROUP BY date
    ORDER BY date

TYPE endpoint
