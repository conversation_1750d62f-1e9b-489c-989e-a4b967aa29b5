DESCRIPTION >
    流量来源分析 - 分析访客来源渠道和转化效果

NODE traffic_sources_query
SQL >
    %
    WITH source_data AS (
        SELECT
            CASE 
                WHEN utm_source IS NOT NULL THEN utm_source
                WHEN empty(referrer) OR referrer IS NULL THEN 'Direct'
                WHEN position(referrer, 'google') > 0 THEN 'Google'
                WHEN position(referrer, 'facebook') > 0 THEN 'Facebook'
                WHEN position(referrer, 'twitter') > 0 THEN 'Twitter'
                WHEN position(referrer, 'linkedin') > 0 THEN 'LinkedIn'
                WHEN position(referrer, 'youtube') > 0 THEN 'YouTube'
                WHEN position(referrer, 'instagram') > 0 THEN 'Instagram'
                ELSE 'Other'
            END as source,
            uniq(visitor_id) as visitors,
            countIf(event_type = 'pageview') as pageviews,
            sum(revenue) as total_revenue,
            countIf(revenue > 0) as converting_visitors
        FROM events
        WHERE website_id = {{String(website_id)}}
          AND timestamp >= {{DateTime(start_date)}}
          AND timestamp <= {{DateTime(end_date)}}
        GROUP BY source
    )
    SELECT
        source,
        visitors,
        pageviews,
        round(total_revenue, 2) as revenue,
        round(converting_visitors * 100.0 / visitors, 2) as conversion_rate
    FROM source_data
    ORDER BY visitors DESC
    LIMIT {{Int32(limit, 10)}}

TYPE endpoint
