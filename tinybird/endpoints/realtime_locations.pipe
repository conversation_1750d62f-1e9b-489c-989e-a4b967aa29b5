#
# 实时访客地理位置端点
# 显示当前活跃访客的地理分布

TOKEN "dashboard" READ

NODE active_locations
SQL >
    % SELECT 
        country,
        city,
        count() as visitor_count,
        uniq(visitor_id) as unique_visitors,
        max(timestamp) as last_seen
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= now() - INTERVAL 30 MINUTE
        AND country IS NOT NULL
        AND country != ''
    GROUP BY country, city
    ORDER BY visitor_count DESC
    LIMIT {{ Int32(limit, 50) }}

NODE endpoint
SQL >
    % SELECT 
        country,
        city,
        visitor_count,
        unique_visitors,
        formatDateTime(last_seen, '%Y-%m-%d %H:%M:%S') as last_seen
    FROM active_locations
