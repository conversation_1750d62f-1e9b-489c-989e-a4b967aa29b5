#
# UTM Analytics Endpoint
# Provides detailed breakdown of UTM parameters (sources, mediums, campaigns, contents, terms)

TOKEN "dashboard" READ

NODE utm_data
SQL >
    % SELECT 
        visitor_id,
        session_id,
        revenue,
        timestamp,
        utm_source,
        utm_medium,
        utm_campaign,
        utm_content,
        utm_term
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= {{ DateTime(start_date, required=True) }}
        AND timestamp <= {{ DateTime(end_date, required=True) }}
        AND event_type = 'pageview'

NODE endpoint
SQL >
    % {% if category == 'utm_sources' %}
        SELECT 
            utm_source as name,
            uniq(visitor_id) as visitors,
            count() as pageviews,
            sum(revenue) as total_revenue,
            countIf(revenue > 0) as conversions,
            uniq(session_id) as sessions,
            round(toFloat64(countIf(revenue > 0)) * 100.0 / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as conversion_rate,
            round(toFloat64(sum(revenue)) / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as revenue_per_visitor,
            round(toFloat64(uniq(visitor_id)) * 100.0 / NULLIF((SELECT uniq(visitor_id) FROM utm_data WHERE utm_source IS NOT NULL AND utm_source != ''), 0), 2) as percentage
        FROM utm_data
        WHERE utm_source IS NOT NULL AND utm_source != ''
        GROUP BY utm_source
        ORDER BY visitors DESC
        LIMIT {{ Int32(limit, 10) }}
    {% elif category == 'utm_mediums' %}
        SELECT 
            utm_medium as name,
            uniq(visitor_id) as visitors,
            count() as pageviews,
            sum(revenue) as total_revenue,
            countIf(revenue > 0) as conversions,
            uniq(session_id) as sessions,
            round(toFloat64(countIf(revenue > 0)) * 100.0 / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as conversion_rate,
            round(toFloat64(sum(revenue)) / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as revenue_per_visitor,
            round(toFloat64(uniq(visitor_id)) * 100.0 / NULLIF((SELECT uniq(visitor_id) FROM utm_data WHERE utm_medium IS NOT NULL AND utm_medium != ''), 0), 2) as percentage
        FROM utm_data
        WHERE utm_medium IS NOT NULL AND utm_medium != ''
        GROUP BY utm_medium
        ORDER BY visitors DESC
        LIMIT {{ Int32(limit, 10) }}
    {% elif category == 'utm_campaigns' %}
        SELECT 
            utm_campaign as name,
            uniq(visitor_id) as visitors,
            count() as pageviews,
            sum(revenue) as total_revenue,
            countIf(revenue > 0) as conversions,
            uniq(session_id) as sessions,
            round(toFloat64(countIf(revenue > 0)) * 100.0 / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as conversion_rate,
            round(toFloat64(sum(revenue)) / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as revenue_per_visitor,
            round(toFloat64(uniq(visitor_id)) * 100.0 / NULLIF((SELECT uniq(visitor_id) FROM utm_data WHERE utm_campaign IS NOT NULL AND utm_campaign != ''), 0), 2) as percentage
        FROM utm_data
        WHERE utm_campaign IS NOT NULL AND utm_campaign != ''
        GROUP BY utm_campaign
        ORDER BY visitors DESC
        LIMIT {{ Int32(limit, 10) }}
    {% elif category == 'utm_contents' %}
        SELECT 
            utm_content as name,
            uniq(visitor_id) as visitors,
            count() as pageviews,
            sum(revenue) as total_revenue,
            countIf(revenue > 0) as conversions,
            uniq(session_id) as sessions,
            round(toFloat64(countIf(revenue > 0)) * 100.0 / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as conversion_rate,
            round(toFloat64(sum(revenue)) / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as revenue_per_visitor,
            round(toFloat64(uniq(visitor_id)) * 100.0 / NULLIF((SELECT uniq(visitor_id) FROM utm_data WHERE utm_content IS NOT NULL AND utm_content != ''), 0), 2) as percentage
        FROM utm_data
        WHERE utm_content IS NOT NULL AND utm_content != ''
        GROUP BY utm_content
        ORDER BY visitors DESC
        LIMIT {{ Int32(limit, 10) }}
    {% elif category == 'utm_terms' %}
        SELECT 
            utm_term as name,
            uniq(visitor_id) as visitors,
            count() as pageviews,
            sum(revenue) as total_revenue,
            countIf(revenue > 0) as conversions,
            uniq(session_id) as sessions,
            round(toFloat64(countIf(revenue > 0)) * 100.0 / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as conversion_rate,
            round(toFloat64(sum(revenue)) / NULLIF(toFloat64(uniq(visitor_id)), 0), 2) as revenue_per_visitor,
            round(toFloat64(uniq(visitor_id)) * 100.0 / NULLIF((SELECT uniq(visitor_id) FROM utm_data WHERE utm_term IS NOT NULL AND utm_term != ''), 0), 2) as percentage
        FROM utm_data
        WHERE utm_term IS NOT NULL AND utm_term != ''
        GROUP BY utm_term
        ORDER BY visitors DESC
        LIMIT {{ Int32(limit, 10) }}
    {% else %}
        SELECT 
            'No data' as name,
            0 as visitors,
            0 as pageviews,
            0 as total_revenue,
            0 as conversions,
            0 as sessions,
            0 as conversion_rate,
            0 as revenue_per_visitor,
            0 as percentage
        LIMIT 0
    {% end %}

TYPE endpoint
