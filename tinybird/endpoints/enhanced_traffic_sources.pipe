#
# Enhanced Traffic Sources Analysis
# Provides detailed breakdown of Channels, Sources, and Campaigns with metrics

TOKEN "dashboard" READ

NODE channel_classification
SQL >
    % SELECT 
        visitor_id,
        session_id,
        revenue,
        timestamp,
        url,
        referrer,
        utm_source,
        utm_medium,
        utm_campaign,
        utm_content,
        utm_term,
        CASE 
            WHEN utm_source IS NOT NULL AND utm_source != '' THEN
                CASE 
                    WHEN utm_medium ILIKE '%cpc%' OR utm_medium ILIKE '%ppc%' OR utm_medium ILIKE '%paid%' THEN 'Paid Search'
                    WHEN utm_medium ILIKE '%social%' OR utm_source ILIKE '%facebook%' OR utm_source ILIKE '%twitter%' OR utm_source ILIKE '%linkedin%' OR utm_source ILIKE '%instagram%' THEN 'Social Media'
                    WHEN utm_medium ILIKE '%email%' OR utm_source ILIKE '%email%' THEN 'Email Marketing'
                    WHEN utm_medium ILIKE '%display%' OR utm_medium ILIKE '%banner%' THEN 'Display Advertising'
                    WHEN utm_medium ILIKE '%affiliate%' THEN 'Affiliate'
                    WHEN utm_medium ILIKE '%video%' OR utm_source ILIKE '%youtube%' THEN 'Video Marketing'
                    ELSE 'Other Paid'
                END
            WHEN referrer IS NULL OR referrer = '' THEN 'Direct'
            WHEN referrer ILIKE '%google%' AND utm_source IS NULL THEN 'Organic Search'
            WHEN referrer ILIKE '%bing%' AND utm_source IS NULL THEN 'Organic Search'
            WHEN referrer ILIKE '%yahoo%' AND utm_source IS NULL THEN 'Organic Search'
            WHEN referrer ILIKE '%duckduckgo%' AND utm_source IS NULL THEN 'Organic Search'
            WHEN referrer ILIKE '%facebook%' THEN 'Social Media'
            WHEN referrer ILIKE '%twitter%' THEN 'Social Media'
            WHEN referrer ILIKE '%linkedin%' THEN 'Social Media'
            WHEN referrer ILIKE '%instagram%' THEN 'Social Media'
            WHEN referrer ILIKE '%youtube%' THEN 'Social Media'
            ELSE 'Referral'
        END as channel,
        CASE 
            WHEN utm_source IS NOT NULL AND utm_source != '' THEN utm_source
            WHEN referrer IS NULL OR referrer = '' THEN 'Direct'
            WHEN referrer ILIKE '%google%' THEN 'Google'
            WHEN referrer ILIKE '%bing%' THEN 'Bing'
            WHEN referrer ILIKE '%yahoo%' THEN 'Yahoo'
            WHEN referrer ILIKE '%duckduckgo%' THEN 'DuckDuckGo'
            WHEN referrer ILIKE '%facebook%' THEN 'Facebook'
            WHEN referrer ILIKE '%twitter%' THEN 'Twitter'
            WHEN referrer ILIKE '%linkedin%' THEN 'LinkedIn'
            WHEN referrer ILIKE '%instagram%' THEN 'Instagram'
            WHEN referrer ILIKE '%youtube%' THEN 'YouTube'
            ELSE COALESCE(
                CASE
                    WHEN referrer IS NOT NULL AND referrer != '' AND position(referrer, '://') > 0 THEN
                        CASE
                            WHEN position(referrer, '/') > position(referrer, '://') + 3 THEN
                                substring(referrer, position(referrer, '://') + 3, position(referrer, '/', position(referrer, '://') + 3) - position(referrer, '://') - 3)
                            ELSE
                                substring(referrer, position(referrer, '://') + 3)
                        END
                    ELSE 'Unknown'
                END, 'Unknown'
            )
        END as source
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= {{ DateTime(start_date, required=True) }}
        AND timestamp <= {{ DateTime(end_date, required=True) }}
        AND event_type = 'pageview'

NODE channels_agg
SQL >
    % SELECT 
        channel,
        uniq(visitor_id) as visitors,
        count() as pageviews,
        sum(revenue) as total_revenue,
        countIf(revenue > 0) as conversions,
        uniq(session_id) as sessions,
        0 as avg_session_duration
    FROM channel_classification
    GROUP BY channel

NODE sources_agg  
SQL >
    % SELECT 
        source,
        uniq(visitor_id) as visitors,
        count() as pageviews,
        sum(revenue) as total_revenue,
        countIf(revenue > 0) as conversions,
        uniq(session_id) as sessions
    FROM channel_classification
    WHERE source != ''
    GROUP BY source

NODE campaigns_agg
SQL >
    % SELECT
        utm_campaign as campaign,
        utm_source,
        utm_medium,
        uniq(visitor_id) as visitors,
        count() as pageviews,
        sum(revenue) as total_revenue,
        countIf(revenue > 0) as conversions,
        uniq(session_id) as sessions
    FROM channel_classification
    WHERE utm_campaign IS NOT NULL AND utm_campaign != ''
    GROUP BY utm_campaign, utm_source, utm_medium

NODE endpoint
SQL >
    % {% if category == 'channels' %}
        SELECT
            channel as name,
            visitors,
            pageviews,
            total_revenue,
            conversions,
            sessions,
            round(toFloat64(conversions) * 100.0 / NULLIF(toFloat64(visitors), 0), 2) as conversion_rate,
            round(toFloat64(total_revenue) / NULLIF(toFloat64(visitors), 0), 2) as revenue_per_visitor,
            round(toFloat64(visitors) * 100.0 / NULLIF((SELECT sum(visitors) FROM channels_agg), 0), 2) as percentage
        FROM channels_agg
        ORDER BY visitors DESC
        LIMIT {{ Int32(limit, 10) }}
    {% elif category == 'sources' %}
        SELECT
            source as name,
            visitors,
            pageviews,
            total_revenue,
            conversions,
            sessions,
            round(toFloat64(conversions) * 100.0 / NULLIF(toFloat64(visitors), 0), 2) as conversion_rate,
            round(toFloat64(total_revenue) / NULLIF(toFloat64(visitors), 0), 2) as revenue_per_visitor,
            round(toFloat64(visitors) * 100.0 / NULLIF((SELECT sum(visitors) FROM sources_agg), 0), 2) as percentage
        FROM sources_agg
        ORDER BY visitors DESC
        LIMIT {{ Int32(limit, 10) }}
    {% else %}
        SELECT
            campaign as name,
            utm_source,
            utm_medium,
            visitors,
            pageviews,
            total_revenue,
            conversions,
            sessions,
            round(toFloat64(conversions) * 100.0 / NULLIF(toFloat64(visitors), 0), 2) as conversion_rate,
            round(toFloat64(total_revenue) / NULLIF(toFloat64(visitors), 0), 2) as revenue_per_visitor,
            round(toFloat64(visitors) * 100.0 / NULLIF((SELECT sum(visitors) FROM campaigns_agg), 0), 2) as percentage
        FROM campaigns_agg
        ORDER BY visitors DESC
        LIMIT {{ Int32(limit, 10) }}
    {% end %}

TYPE endpoint
