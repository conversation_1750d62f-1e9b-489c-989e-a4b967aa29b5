#
# 营销活动收入归因分析端点
# 分析不同 UTM 营销活动带来的收入和转化

TOKEN "dashboard" READ

NODE campaign_data
SQL >
    % SELECT 
        utm_campaign,
        uniq(visitor_id) as visitors,
        sumIf(revenue, revenue > 0) as sum_revenue,
        countIf(revenue > 0) as conversions
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= {{ DateTime(start_date, required=True) }}
        AND timestamp <= {{ DateTime(end_date, required=True) }}
        {% if defined(utm_campaign) %}
        AND utm_campaign = {{ String(utm_campaign) }}
        {% end %}
        AND utm_campaign IS NOT NULL
        AND utm_campaign != ''
    GROUP BY utm_campaign

NODE endpoint
SQL >
    % SELECT 
        utm_campaign,
        visitors,
        toFloat64(sum_revenue) as total_revenue,
        conversions,
        round(toFloat64(conversions) * 100.0 / NULLIF(toFloat64(visitors), 0), 2) as conversion_rate,
        round(toFloat64(sum_revenue) / NULLIF(toFloat64(visitors), 0), 2) as revenue_per_visitor
    FROM campaign_data
    ORDER BY total_revenue DESC
    LIMIT {{ Int32(limit, 10) }}
