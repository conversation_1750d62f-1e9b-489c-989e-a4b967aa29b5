DESCRIPTION >
    Geographic Analytics - Countries
    Provides comprehensive country-level visitor analytics with time filtering

TOKEN "dashboard" READ

NODE country_stats
SQL >
    % SELECT 
        country,
        count() as total_events,
        uniq(visitor_id) as unique_visitors,
        uniq(session_id) as sessions,
        sum(CASE WHEN event_type = 'pageview' THEN 1 ELSE 0 END) as pageviews,
        sum(CASE WHEN revenue IS NOT NULL THEN revenue ELSE 0 END) as total_revenue,
        max(timestamp) as last_activity,
        min(timestamp) as first_activity,
        round(avg(CASE WHEN event_type = 'pageview' THEN 1 ELSE 0 END) * 100, 2) as pageview_rate
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= {{ DateTime(start_date, '2024-01-01 00:00:00') }}
        AND timestamp <= {{ DateTime(end_date, '2024-12-31 23:59:59') }}
        AND country IS NOT NULL
        AND country != ''
    GROUP BY country
    ORDER BY unique_visitors DESC
    LIMIT {{ Int32(limit, 100) }}

NODE endpoint
SQL >
    % SELECT 
        country,
        total_events,
        unique_visitors,
        sessions,
        pageviews,
        total_revenue,
        pageview_rate,
        formatDateTime(last_activity, '%Y-%m-%d %H:%M:%S') as last_activity,
        formatDateTime(first_activity, '%Y-%m-%d %H:%M:%S') as first_activity,
        round(unique_visitors * 100.0 / (SELECT sum(unique_visitors) FROM country_stats), 2) as percentage
    FROM country_stats
    ORDER BY unique_visitors DESC

TYPE endpoint
