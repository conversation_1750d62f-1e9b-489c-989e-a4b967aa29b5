DESCRIPTION >
    热门页面分析 - 获取访问量最高的页面及其统计数据

NODE top_pages_query
SQL >
    %
    WITH page_data AS (
        SELECT
            url,
            visitor_id,
            session_id,
            countIf(event_type = 'pageview') OVER (PARTITION BY visitor_id, url) as visitor_pageviews_on_page,
            countIf(event_type = 'pageview') OVER (PARTITION BY visitor_id) as visitor_total_pageviews,
            min(timestamp) OVER (PARTITION BY visitor_id) as visitor_first_seen,
            max(timestamp) OVER (PARTITION BY visitor_id) as visitor_last_seen
        FROM events
        WHERE website_id = {{String(website_id)}}
          AND event_type = 'pageview'
          AND timestamp >= {{DateTime(start_date)}}
          AND timestamp <= {{DateTime(end_date)}}
    )
    SELECT
        url,
        count() as pageviews,
        uniq(visitor_id) as visitors,
        round(countIf(visitor_total_pageviews = 1) * 100.0 / uniq(visitor_id), 2) as bounce_rate,
        round(avgIf((visitor_last_seen - visitor_first_seen) / 60, visitor_total_pageviews > 1), 2) as avg_duration_minutes
    FROM page_data
    GROUP BY url
    ORDER BY pageviews DESC
    LIMIT {{Int32(limit, 10)}}

TYPE endpoint
