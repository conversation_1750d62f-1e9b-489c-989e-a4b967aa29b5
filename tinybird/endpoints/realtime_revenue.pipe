#
# 实时收入追踪端点
# 显示最近的收入事件和交易

TOKEN "dashboard" READ

NODE recent_revenue
SQL >
    % SELECT 
        visitor_id,
        session_id,
        event_type,
        event_name,
        url,
        country,
        city,
        utm_source,
        utm_medium,
        utm_campaign,
        revenue,
        timestamp
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= now() - INTERVAL 60 MINUTE
        AND revenue > 0
    ORDER BY timestamp DESC
    LIMIT {{ Int32(limit, 50) }}

NODE endpoint
SQL >
    % SELECT 
        visitor_id,
        session_id,
        event_type,
        event_name,
        url,
        country,
        city,
        utm_source,
        utm_medium,
        utm_campaign,
        revenue,
        formatDateTime(timestamp, '%Y-%m-%d %H:%M:%S') as timestamp
    FROM recent_revenue
