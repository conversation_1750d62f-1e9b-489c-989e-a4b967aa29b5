DESCRIPTION >
    Geographic Analytics - Overview
    Provides summary statistics for geographic data visualization

TOKEN "dashboard" READ

NODE geographic_summary
SQL >
    % SELECT 
        uniq(country) as total_countries,
        uniq(region) as total_regions,
        uniq(city) as total_cities,
        uniq(visitor_id) as total_unique_visitors,
        count() as total_events,
        sum(CASE WHEN event_type = 'pageview' THEN 1 ELSE 0 END) as total_pageviews,
        sum(CASE WHEN revenue IS NOT NULL THEN revenue ELSE 0 END) as total_revenue
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= {{ DateTime(start_date, '2024-01-01 00:00:00') }}
        AND timestamp <= {{ DateTime(end_date, '2024-12-31 23:59:59') }}
        AND country IS NOT NULL
        AND country != ''

NODE top_countries
SQL >
    % SELECT 
        country,
        uniq(visitor_id) as unique_visitors
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= {{ DateTime(start_date, '2024-01-01 00:00:00') }}
        AND timestamp <= {{ DateTime(end_date, '2024-12-31 23:59:59') }}
        AND country IS NOT NULL
        AND country != ''
    GROUP BY country
    ORDER BY unique_visitors DESC
    LIMIT 5

NODE endpoint
SQL >
    % SELECT 
        gs.total_countries,
        gs.total_regions,
        gs.total_cities,
        gs.total_unique_visitors,
        gs.total_events,
        gs.total_pageviews,
        gs.total_revenue,
        groupArray(tc.country) as top_countries,
        groupArray(tc.unique_visitors) as top_countries_visitors
    FROM geographic_summary gs
    CROSS JOIN (
        SELECT 
            country,
            unique_visitors
        FROM top_countries
    ) tc
    GROUP BY
        gs.total_countries,
        gs.total_regions,
        gs.total_cities,
        gs.total_unique_visitors,
        gs.total_events,
        gs.total_pageviews,
        gs.total_revenue

TYPE endpoint
