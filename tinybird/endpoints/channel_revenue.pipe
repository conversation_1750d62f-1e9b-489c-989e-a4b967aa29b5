#
# 营销渠道收入归因分析端点
# 分析不同营销渠道（有机搜索、直接访问、社交媒体等）的收入贡献

TOKEN "dashboard" READ

NODE channel_classification
SQL >
    % SELECT 
        visitor_id,
        session_id,
        revenue,
        CASE 
            WHEN utm_source IS NOT NULL AND utm_source != '' THEN
                CASE 
                    WHEN utm_source ILIKE '%google%' THEN 'Paid Search'
                    WHEN utm_source ILIKE '%facebook%' OR utm_source ILIKE '%meta%' THEN 'Social Media'
                    WHEN utm_source ILIKE '%email%' THEN 'Email Marketing'
                    WHEN utm_source ILIKE '%linkedin%' THEN 'Social Media'
                    WHEN utm_source ILIKE '%twitter%' THEN 'Social Media'
                    WHEN utm_source ILIKE '%youtube%' THEN 'Video Marketing'
                    ELSE 'Other Paid'
                END
            WHEN referrer IS NULL OR referrer = '' THEN 'Direct'
            WHEN referrer ILIKE '%google%' AND utm_source IS NULL THEN 'Organic Search'
            WHEN referrer ILIKE '%bing%' AND utm_source IS NULL THEN 'Organic Search'
            WHEN referrer ILIKE '%yahoo%' AND utm_source IS NULL THEN 'Organic Search'
            WHEN referrer ILIKE '%facebook%' THEN 'Social Media'
            WHEN referrer ILIKE '%twitter%' THEN 'Social Media'
            WHEN referrer ILIKE '%linkedin%' THEN 'Social Media'
            ELSE 'Referral'
        END as channel
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= {{ DateTime(start_date, required=True) }}
        AND timestamp <= {{ DateTime(end_date, required=True) }}

NODE channel_revenue_agg
SQL >
    % SELECT 
        channel,
        uniq(visitor_id) as visitors,
        sum(revenue) as total_revenue,
        countIf(revenue > 0) as conversions,
        uniq(session_id) as sessions
    FROM channel_classification
    GROUP BY channel

NODE endpoint
SQL >
    % SELECT 
        channel,
        visitors,
        total_revenue,
        conversions,
        round(toFloat64(conversions) * 100.0 / NULLIF(toFloat64(visitors), 0), 2) as conversion_rate,
        round(toFloat64(total_revenue) / NULLIF(toFloat64(visitors), 0), 2) as revenue_per_visitor,
        sessions
    FROM channel_revenue_agg
    ORDER BY total_revenue DESC
    LIMIT {{ Int32(limit, 10) }}
