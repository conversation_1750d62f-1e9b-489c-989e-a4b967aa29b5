#
# 客户生命周期价值（LTV）分析端点
# 计算客户的历史价值和预测未来价值

TOKEN "dashboard" READ

# 预过滤事件
NODE filtered_events
SQL >
    % SELECT
        visitor_id,
        session_id,
        timestamp,
        event_type,
        revenue
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= {{ DateTime(start_date, required=True) }}
        AND timestamp <= {{ DateTime(end_date, required=True) }}

# 计算每个会话的起止时间
NODE session_bounds
SQL >
    % SELECT
        visitor_id,
        session_id,
        min(timestamp) as min_ts,
        max(timestamp) as max_ts
    FROM filtered_events
    GROUP BY visitor_id, session_id

# 计算每个访客的平均会话时长（分钟）
NODE session_metrics
SQL >
    % SELECT
        visitor_id,
        round(avg( (toUnixTimestamp(max_ts) - toUnixTimestamp(min_ts)) / 60 ), 2) as avg_session_duration
    FROM session_bounds
    GROUP BY visitor_id

# 统计访客级别的汇总指标
NODE customer_metrics
SQL >
    % SELECT 
        visitor_id,
        min(timestamp) as first_seen,
        max(timestamp) as last_seen,
        sumIf(revenue, revenue > 0) as total_revenue,
        uniqIf(session_id, event_type = 'pageview') as total_sessions,
        count() as total_pageviews,
        dateDiff('day', min(timestamp), max(timestamp)) + 1 as days_since_first_visit
    FROM filtered_events
    GROUP BY visitor_id

# 合并并计算预测 LTV
NODE ltv_prediction
SQL >
    % SELECT 
        cm.visitor_id,
        cm.first_seen,
        cm.last_seen,
        toFloat64(cm.total_revenue) as total_revenue,
        cm.total_sessions,
        cm.total_pageviews,
        cm.days_since_first_visit,
        toFloat64(coalesce(sm.avg_session_duration, 0)) as avg_session_duration,
        round(
            CASE 
                WHEN cm.days_since_first_visit > 0 THEN 
                    toFloat64(cm.total_revenue) * (365.0 / cm.days_since_first_visit)
                ELSE toFloat64(cm.total_revenue)
            END,
            2
        ) as predicted_ltv
    FROM customer_metrics cm
    LEFT JOIN session_metrics sm ON cm.visitor_id = sm.visitor_id

NODE endpoint
SQL >
    % SELECT 
        visitor_id,
        formatDateTime(first_seen, '%Y-%m-%d %H:%M:%S') as first_seen,
        formatDateTime(last_seen, '%Y-%m-%d %H:%M:%S') as last_seen,
        total_revenue,
        total_sessions,
        total_pageviews,
        avg_session_duration,
        days_since_first_visit,
        predicted_ltv
    FROM ltv_prediction
    WHERE total_revenue > 0
    ORDER BY total_revenue DESC
    LIMIT {{ Int32(limit, 100) }}
