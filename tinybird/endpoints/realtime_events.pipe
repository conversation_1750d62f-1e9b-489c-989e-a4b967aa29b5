#
# 实时事件流端点
# 显示最近发生的所有事件（页面浏览、转化、自定义事件）

TOKEN "dashboard" READ

NODE recent_events
SQL >
    % SELECT 
        visitor_id,
        session_id,
        event_type,
        event_name,
        url,
        referrer,
        country,
        city,
        device,
        browser,
        utm_source,
        utm_medium,
        utm_campaign,
        revenue,
        timestamp
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= now() - INTERVAL 30 MINUTE
    ORDER BY timestamp DESC
    LIMIT {{ Int32(limit, 50) }}

NODE endpoint
SQL >
    % SELECT 
        visitor_id,
        session_id,
        event_type,
        event_name,
        url,
        referrer,
        country,
        city,
        device,
        browser,
        utm_source,
        utm_medium,
        utm_campaign,
        revenue,
        formatDateTime(timestamp, '%Y-%m-%d %H:%M:%S') as timestamp
    FROM recent_events
