DESCRIPTION >
    实时访客监控 - 获取最近30分钟的访客活动（去重每访客，取最近记录）

NODE base_recent_events
SQL >
    %
    SELECT
        visitor_id,
        url,
        country,
        city,
        custom_data,
        device,
        browser,
        referrer,
        timestamp
    FROM events
    WHERE website_id = {{String(website_id)}}
      AND event_type = 'pageview'
      AND timestamp >= now() - INTERVAL 30 MINUTE

NODE latest_per_visitor
SQL >
    %
    SELECT
        visitor_id,
        argMax(url, timestamp) AS url,
        argMax(country, timestamp) AS country,
        argMax(city, timestamp) AS city,
        argMax(JSONExtractString(custom_data, 'timezone'), timestamp) AS timezone,
        argMax(device, timestamp) AS device,
        argMax(browser, timestamp) AS browser,
        argMax(referrer, timestamp) AS referrer,
        max(timestamp) AS last_ts
    FROM base_recent_events
    GROUP BY visitor_id

NODE realtime_query
SQL >
    %
    SELECT
        visitor_id,
        url,
        country,
        city,
        timezone,
        device,
        browser,
        referrer,
        last_ts AS timestamp
    FROM latest_per_visitor
    ORDER BY last_ts DESC
    LIMIT {{Int32(limit, 50)}}

TYPE endpoint
