DESCRIPTION >
    设备和浏览器分析 - 获取访客使用的设备、浏览器、操作系统统计

NODE device_breakdown_query
SQL >
    %
    WITH base_events AS (
        SELECT
            device,
            browser,
            os,
            visitor_id
        FROM events
        WHERE website_id = {{String(website_id)}}
          AND timestamp >= {{DateTime(start_date)}}
          AND timestamp <= {{DateTime(end_date)}}
          AND isNotNull(device)
    ),
    breakdown_data AS (
        {% if String(breakdown_type, 'device') == 'device' %}
            SELECT
                device as category,
                uniq(visitor_id) as visitors
            FROM base_events
            GROUP BY device
        {% elif String(breakdown_type, 'device') == 'browser' %}
            SELECT
                browser as category,
                uniq(visitor_id) as visitors
            FROM base_events
            GROUP BY browser
        {% elif String(breakdown_type, 'device') == 'os' %}
            SELECT
                os as category,
                uniq(visitor_id) as visitors
            FROM base_events
            GROUP BY os
        {% else %}
            SELECT
                device as category,
                uniq(visitor_id) as visitors
            FROM base_events
            GROUP BY device
        {% end %}
    ),
    total_visitors AS (
        SELECT sum(visitors) as total FROM breakdown_data
    )
    SELECT
        category,
        visitors,
        round(visitors * 100.0 / (SELECT total FROM total_visitors), 2) as percentage
    FROM breakdown_data
    ORDER BY visitors DESC

TYPE endpoint
