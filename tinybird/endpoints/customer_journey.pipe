#
# 客户旅程分析端点
# 追踪付费客户的完整转化路径和触点

TOKEN "dashboard" READ

NODE paying_visitors
SQL >
    % SELECT DISTINCT visitor_id
    FROM events
    WHERE 
        website_id = {{ String(website_id, required=True) }}
        AND timestamp >= {{ DateTime(start_date, required=True) }}
        AND timestamp <= {{ DateTime(end_date, required=True) }}
        AND revenue > 0

NODE visitor_journey
SQL >
    % SELECT 
        e.visitor_id,
        e.url,
        e.timestamp,
        e.event_type,
        e.revenue,
        e.referrer,
        e.utm_source,
        row_number() OVER (PARTITION BY e.visitor_id ORDER BY e.timestamp) as step_number
    FROM events e
    INNER JOIN paying_visitors pv ON e.visitor_id = pv.visitor_id
    WHERE 
        e.website_id = {{ String(website_id, required=True) }}
        AND e.timestamp >= {{ DateTime(start_date, required=True) }}
        AND e.timestamp <= {{ DateTime(end_date, required=True) }}

NODE journey_summary
SQL >
    % SELECT 
        visitor_id,
        sum(revenue) as total_revenue,
        min(timestamp) as first_touch_time,
        max(timestamp) as last_touch_time,
        count(*) as total_touchpoints,
        argMin(COALESCE(utm_source, 
            CASE 
                WHEN referrer ILIKE '%google%' THEN 'google'
                WHEN referrer ILIKE '%facebook%' THEN 'facebook'
                WHEN referrer IS NULL OR referrer = '' THEN 'direct'
                ELSE 'referral'
            END
        ), timestamp) as first_touch_source,
        argMax(COALESCE(utm_source, 
            CASE 
                WHEN referrer ILIKE '%google%' THEN 'google'
                WHEN referrer ILIKE '%facebook%' THEN 'facebook'
                WHEN referrer IS NULL OR referrer = '' THEN 'direct'
                ELSE 'referral'
            END
        ), timestamp) as last_touch_source
    FROM visitor_journey
    GROUP BY visitor_id

NODE endpoint
SQL >
    % SELECT 
        js.visitor_id,
        js.total_revenue,
        js.first_touch_source,
        js.last_touch_source,
        js.total_touchpoints,
        arrayStringConcat(
            groupArray(
                COALESCE(vj.utm_source, 
                    CASE 
                        WHEN vj.referrer ILIKE '%google%' THEN 'google'
                        WHEN vj.referrer ILIKE '%facebook%' THEN 'facebook'
                        WHEN vj.referrer IS NULL OR vj.referrer = '' THEN 'direct'
                        ELSE 'referral'
                    END
                )
            ), ' → '
        ) as conversion_path,
        groupArray(tuple(
            vj.step_number,
            vj.url,
            formatDateTime(vj.timestamp, '%Y-%m-%d %H:%M:%S'),
            vj.event_type,
            vj.revenue
        )) as journey_steps
    FROM journey_summary js
    LEFT JOIN visitor_journey vj ON js.visitor_id = vj.visitor_id
    GROUP BY 
        js.visitor_id,
        js.total_revenue,
        js.first_touch_source,
        js.last_touch_source,
        js.total_touchpoints
    ORDER BY js.total_revenue DESC
    LIMIT {{ Int32(limit, 10) }}
