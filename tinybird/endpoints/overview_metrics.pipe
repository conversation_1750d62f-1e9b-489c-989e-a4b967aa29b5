DESCRIPTION >
    获取网站概览指标：访客数、页面浏览量、跳出率、平均会话时长、总收入

NODE overview_query
SQL >
    %
    WITH base_data AS (
        SELECT
            visitor_id,
            session_id,
            revenue,
            event_type,
            countIf(event_type = 'pageview') OVER (PARTITION BY session_id) as session_pageviews,
            (max(timestamp) OVER (PARTITION BY session_id) - min(timestamp) OVER (PARTITION BY session_id)) / 60 as session_duration_minutes
        FROM events
        WHERE website_id = {{String(website_id)}}
          AND timestamp >= {{DateTime(start_date)}}
          AND timestamp <= {{DateTime(end_date)}}
          {% if defined(country) %}
          AND country = {{String(country)}}
          {% end %}
          {% if defined(device) %}
          AND device = {{String(device)}}
          {% end %}
          {% if defined(browser) %}
          AND browser = {{String(browser)}}
          {% end %}
          {% if defined(utm_source) %}
          AND utm_source = {{String(utm_source)}}
          {% end %}
    )
    SELECT
        uniq(visitor_id) as visitors,
        countIf(event_type = 'pageview') as pageviews,
        round(countIf(session_pageviews = 1) * 100.0 / uniq(session_id), 2) as bounce_rate,
        round(avgIf(session_duration_minutes, session_pageviews > 1), 2) as avg_session_duration,
        round(sum(revenue), 2) as total_revenue
    FROM base_data

TYPE endpoint
