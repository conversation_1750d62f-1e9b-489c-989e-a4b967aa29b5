DESCRIPTION >
    用户事件数据 - DataFast 实时分析的核心数据源
    支持页面浏览、自定义事件、支付、注册、转化等事件类型

SCHEMA >
    id String `json:$.id`,
    website_id String `json:$.website_id`,
    session_id String `json:$.session_id`,
    visitor_id String `json:$.visitor_id`,
    event_type LowCardinality(String) `json:$.event_type`,
    event_name Nullable(String) `json:$.event_name`,
    url String `json:$.url`,
    referrer Nullable(String) `json:$.referrer`,
    user_agent Nullable(String) `json:$.user_agent`,
    ip_address Nullable(String) `json:$.ip_address`,
    country Nullable(FixedString(2)) `json:$.country`,
    region Nullable(String) `json:$.region`,
    city Nullable(String) `json:$.city`,
    device LowCardinality(Nullable(String)) `json:$.device`,
    browser LowCardinality(Nullable(String)) `json:$.browser`,
    os LowCardinality(Nullable(String)) `json:$.os`,
    utm_source Nullable(String) `json:$.utm_source`,
    utm_medium Nullable(String) `json:$.utm_medium`,
    utm_campaign Nullable(String) `json:$.utm_campaign`,
    utm_content Nullable(String) `json:$.utm_content`,
    utm_term Nullable(String) `json:$.utm_term`,
    custom_data Nullable(String) `json:$.custom_data`,
    revenue Nullable(Decimal(10,2)) `json:$.revenue`,
    timestamp DateTime `json:$.timestamp`

ENGINE "MergeTree"
ENGINE_PARTITION_KEY "toYYYYMM(timestamp)"
ENGINE_SORTING_KEY "website_id, timestamp, visitor_id"
ENGINE_TTL "timestamp + toIntervalDay(730)"
