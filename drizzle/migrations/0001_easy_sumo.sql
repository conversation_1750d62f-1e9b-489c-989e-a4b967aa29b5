CREATE TABLE "payment_provider_credentials" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"website_id" uuid,
	"provider" varchar(50) NOT NULL,
	"credential_type" varchar(50) NOT NULL,
	"encrypted_value" text NOT NULL,
	"key_prefix" varchar(10),
	"is_active" boolean DEFAULT true NOT NULL,
	"last_validated" timestamp,
	"validation_status" varchar(20) DEFAULT 'pending',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "unique_user_provider_credential_type" UNIQUE("user_id","provider","credential_type","website_id")
);
--> statement-breakpoint
CREATE TABLE "revenue_events" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"website_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"provider" varchar(50) NOT NULL,
	"transaction_id" varchar(255) NOT NULL,
	"session_id" uuid,
	"visitor_id" uuid,
	"amount" numeric(12, 2) NOT NULL,
	"currency" varchar(3) NOT NULL,
	"customer_email" varchar(255),
	"source" varchar(20) NOT NULL,
	"deduplication_id" varchar(64) NOT NULL,
	"metadata" text,
	"processed_at" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "revenue_events_deduplication_id_idx" UNIQUE("deduplication_id")
);
--> statement-breakpoint
CREATE TABLE "webhook_endpoints" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"website_id" uuid,
	"provider" varchar(50) NOT NULL,
	"webhook_id" varchar(255) NOT NULL,
	"webhook_url" text NOT NULL,
	"events" text NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"last_triggered" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "payment_provider_credentials" ADD CONSTRAINT "payment_provider_credentials_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment_provider_credentials" ADD CONSTRAINT "payment_provider_credentials_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "revenue_events" ADD CONSTRAINT "revenue_events_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "revenue_events" ADD CONSTRAINT "revenue_events_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "webhook_endpoints" ADD CONSTRAINT "webhook_endpoints_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "webhook_endpoints" ADD CONSTRAINT "webhook_endpoints_website_id_websites_id_fk" FOREIGN KEY ("website_id") REFERENCES "public"."websites"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "payment_credentials_user_provider_idx" ON "payment_provider_credentials" USING btree ("user_id","provider");--> statement-breakpoint
CREATE INDEX "payment_credentials_website_provider_idx" ON "payment_provider_credentials" USING btree ("website_id","provider");--> statement-breakpoint
CREATE INDEX "revenue_events_website_id_idx" ON "revenue_events" USING btree ("website_id");--> statement-breakpoint
CREATE INDEX "revenue_events_transaction_id_idx" ON "revenue_events" USING btree ("transaction_id");--> statement-breakpoint
CREATE INDEX "revenue_events_provider_transaction_idx" ON "revenue_events" USING btree ("provider","transaction_id");--> statement-breakpoint
CREATE INDEX "webhook_endpoints_user_provider_idx" ON "webhook_endpoints" USING btree ("user_id","provider");--> statement-breakpoint
CREATE INDEX "webhook_endpoints_webhook_id_idx" ON "webhook_endpoints" USING btree ("webhook_id");