/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static exports for better performance
  // output: "standalone",
  turbopack: {},
  // External packages for server components
  serverExternalPackages: ["@neondatabase/serverless"],

  // Enable experimental features
  experimental: {
    // Enable PPR (PartialPrerendering) when stable
    // ppr: true,
    optimizePackageImports: ["lucide-react"],
  },

  // Images optimization
  images: {
    formats: ["image/webp", "image/avif"],
    minimumCacheTTL: 31536000, // 1 year
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Compression and optimization
  compress: true,
  poweredByHeader: false,

  // Static optimization
  generateEtags: true,

  // Headers for better caching and security
  async headers() {
    return [
      {
        source: "/api/:path*",
        headers: [
          {
            key: "Cache-Control",
            value: "s-maxage=60, stale-while-revalidate=300",
          },
        ],
      },
      {
        source: "/script.js",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
          {
            key: "Content-Type",
            value: "application/javascript",
          },
        ],
      },
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          {
            key: "Permissions-Policy",
            value: "camera=(), microphone=(), geolocation=()",
          },
        ],
      },
    ]
  },

  // Redirects for better SEO
  async redirects() {
    return [
      {
        source: "/dashboard/analytics",
        destination: "/dashboard",
        permanent: true,
      },
    ]
  },

  // Webpack optimizations
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Fix server-side compilation issues
    if (isServer) {
      // Define globals as undefined for server-side rendering
      config.plugins.push(
        new webpack.DefinePlugin({
          self: "undefined",
          window: "undefined",
        })
      )
    }

    // Optimize bundle size
    config.optimization.splitChunks = {
      chunks: "all",
      cacheGroups: {
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
        },
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
          priority: -10,
          chunks: "all",
        },
        recharts: {
          test: /[\\/]node_modules[\\/](recharts|d3-.*)[\\/]/,
          name: "recharts",
          priority: 10,
          chunks: "all",
        },
      },
    }

    // Analyze bundle size in development
    if (!dev && !isServer) {
      config.plugins.push(
        new webpack.DefinePlugin({
          "process.env.ANALYZE": JSON.stringify(process.env.ANALYZE),
        })
      )
    }

    return config
  },

  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: false,
  },

  // Disable ESLint during builds (using Biome instead)
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Environment variables
  env: {
    CUSTOM_KEY: "datafast-analytics",
  },

  // Internationalization (if needed in the future)
  // i18n: {
  //   locales: ['en', 'es', 'fr'],
  //   defaultLocale: 'en',
  // },

  // Rewrites for cleaner URLs
  async rewrites() {
    return [
      {
        source: "/js/script.js",
        destination: "/script.js",
      },
      {
        source: "/analytics.js",
        destination: "/script.js",
      },
    ]
  },
}

module.exports = nextConfig
