{"$schema": "https://biomejs.dev/schemas/2.1.3/schema.json", "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "useImportType": "off"}, "suspicious": {"noExplicitAny": "off"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "asNeeded", "arrowParentheses": "asNeeded", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto"}}, "files": {"include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "ignore": ["node_modules", ".next", "dist", "build", "*.d.ts", "public/**"]}, "organizeImports": {"enabled": true}, "overrides": [{"include": ["app/api/**/*.ts"], "linter": {"rules": {"correctness": {"noUnusedVariables": "warn"}}}}]}