## InstaSight - Revenue-Driven Analytics

InstaSight 是一个基于 Next.js 15、Tinybird 与 Neon 的现代分析平台，聚焦收入归因与实时访客洞察，提供轻量可插拔的前端埋点脚本与高性能服务端上报路径。

### 功能特性

- 收入归因与渠道分析（Tinybird 实时聚合）
- 实时访客/事件/概览（轮询 JSON + Tinybird pipes）
- 多模式前端脚本：默认代理、直连 Tinybird、动态、4KB 轻量版
- 智能环境检测（本地/生产 Tinybird API & Token 自动切换）
- 热路径可观测与性能优化（缓存 + 负缓存、keepalive/sendBeacon）
- 安全与合规：输入校验、速率限制、DNT/GPC/consent 门控、CSP/CORS

### 技术栈

- 前端/框架：Next.js 15（App Router）、React 19、Tailwind v4、shadcn/ui
- 实时与分析：Tinybird（datasource/pipes + SDK）
- 元数据/鉴权：Neon (PostgreSQL) + Drizzle ORM
- 认证：Auth.js v5（Email magic link via Resend）
- 缓存：开发内存缓存；生产 Upstash Redis（自动切换）
- 质量：Biome（Lint/Format）、Playwright（E2E）

### 目录与关键文件

- 前端脚本：`public/script.js`、`public/script-tinybird.js`、`public/script-dynamic.js`、`public/script-4kb.js`
- Tinybird 接入：`lib/tinybird/client.ts`、`lib/tinybird/analytics.ts`、`lib/tinybird/events.ts`、`tinybird/endpoints/*`
- API 路由：
  - 事件上报：`app/api/events/route.ts`
  - 实时查询：`app/api/realtime/[websiteId]/route.ts`、`app/api/stream/route.ts`
  - 动态脚本：`app/api/script/[trackingId]/route.ts`
  - 分析/收入：`app/api/analytics/[websiteId]/route.ts`、`app/api/revenue/[websiteId]/route.ts`
- 数据库：`lib/db/schema.ts`、`lib/db/index.ts`
- 缓存层：`lib/cache/redis.ts`（开发 MemoryCache，生产 UpstashCache）
- 安全：`lib/security/middleware.ts`、`lib/security/validation.ts`
- 架构图：`docs/architecture-flow.md`

### 环境变量

- 数据库/认证
  - `DATABASE_URL`、`AUTH_SECRET`、`AUTH_RESEND_KEY`
- Tinybird（本地与生产自动检测）
  - `TINYBIRD_API_URL`、`TINYBIRD_TOKEN`
  - 可选本地：`TINYBIRD_API_URL_LOCAL`、`TINYBIRD_TOKEN_LOCAL`
- Upstash Redis（生产缓存）
  - `UPSTASH_REDIS_REST_URL`、`UPSTASH_REDIS_REST_TOKEN`
- 应用
  - `NEXT_PUBLIC_APP_URL`（你的站点域名）
  - `EVENT_WRITE_MODE` = `tinybird` | `dual` | `neon`（默认 `tinybird`）

### 本地开发

```bash
npm i
npm run dev
# 或构建验证
npm run build
# Lint
npx biome check .
```

本地 Tinybird 开发：可通过 Tinybird CLI 启动本地 endpoint，并在 `.env.local` 配置本地 API/Token；代码会自动检测并使用本地配置。

### 部署到 Vercel

1. 在 Vercel 项目设置中配置环境变量：
   - `DATABASE_URL`、`AUTH_SECRET`、`AUTH_RESEND_KEY`
   - `TINYBIRD_API_URL`、`TINYBIRD_TOKEN`
   - `UPSTASH_REDIS_REST_URL`、`UPSTASH_REDIS_REST_TOKEN`
   - `NEXT_PUBLIC_APP_URL`（生产域名）、`EVENT_WRITE_MODE`（建议 `tinybird`）
2. 直接推送到 `main` 触发部署（或手动 Deploy）。
3. 访问 `https://<your-app>.vercel.app` 验证。

### 集成前端埋点（Next.js 15, App Router）

- 推荐“动态分发”脚本（自动注入正确端点/Token）：

```tsx
// app/layout.tsx
import Script from "next/script";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const trackingId = "YOUR_TRACKING_ID";
  return (
    <html lang="en">
      <body>
        {children}
        <Script
          src={`https://<your-app>.vercel.app/api/script/${trackingId}?mode=default`}
          data-website-id={trackingId}
          data-domain="example.com"
          strategy="afterInteractive"
        />
      </body>
    </html>
  );
}
```

- 4KB 轻量版：将 `mode=4kb`；直连 Tinybird：`mode=direct`。
- 如使用 `public/script.js`，跨域时务必传 `data-endpoint="https://<your-app>.vercel.app/api/events"`。

### 实时与权限约定（重要）

- 受保护读取 API（`/api/realtime/[websiteId]`、`/api/stream`）参数必须传数据库主键 `websites.id`，服务端会做“登录+站点归属”校验；校验通过后，路由内部再使用 `website.trackingId` 调 Tinybird。`/api/stream` 返回 JSON（不再使用 SSE）。
- 前端上报与 Tinybird 查询统一使用 `trackingId` 作为 `website_id`。

### 事件热路径与缓存

- `/api/events`：
  - 采用 `trackingId -> website` 的正/负缓存（开发内存、生产 Upstash Redis）
  - 兼容 `fetch(keepalive)` 与 `sendBeacon`（`text/plain`）
  - `EVENT_WRITE_MODE` 默认 `tinybird`（仅写 TB）；`dual` 会同时写 Neon，不建议在请求线程做重写入

### 安全与隐私

- `withSecurity(SECURITY_CONFIGS.tracking)`：限流、CORS、可疑 UA、包体限制
- 输入校验/清洗：`lib/security/validation.ts`（上报参数 schema 与 sanitize）
- 遵循 DNT/GPC 与 `data-consent`，前端脚本默认尊重隐私门控
- 生产建议在 CSP 中允许 Tinybird 域名直连（如果使用直连模式）

### 测试与诊断

- 分发脚本：`curl -I https://<your-app>.vercel.app/api/script/<trackingId>`
- 上报最小体：

```bash
curl -X POST https://<your-app>.vercel.app/api/events \
  -H "Content-Type: application/json" \
  -d '{"websiteId":"<trackingId>","visitorId":"v1","sessionId":"s1","eventType":"pageview","eventName":"page_view","url":"https://example.com/"}'
```

- 实时（需登录）：`GET /api/realtime/<websiteId>?type=visitors&limit=50`（参数用 DB 主键 id）

### 常见问题（FAQ）

- 实时 API 返回 404：大概率是用 `trackingId` 调了受保护路由。需传 `websites.id`。
- 跨域上报失败：静态脚本需通过 `data-endpoint` 指向完整统计服务 URL；或改用动态分发脚本。
- Tinybird 未收到事件：检查 `EVENT_WRITE_MODE`、Tinybird Token、以及 `/api/events` 日志输出。

---

更多细节参见：

- 架构与调用流：`docs/architecture-flow.md`
- 环境变量完整示例：`docs/环境变量配置示例.md`
- 追踪脚本对比：`docs/追踪脚本对比.md`
