# InstaSight ↔ Plausible 集成与对齐计划

## 目标

- 系统性梳理 Plausible（analytics-master）能力，与 InstaSight 现状对齐。
- 在保留 InstaSight 架构优势（Tinybird 实时聚合 + Neon 元数据 + 轻量脚本）的前提下，提供与 Plausible 体验等价或更优的功能集。
- 输出可执行的实施清单（API/前端/UI/管道/数据模型/安全与运维）。

---

## 一、Plausible 关键能力（源码与文档梳理）

- **隐私友好 & 无 Cookie**：最小化采集，GDPR/CCPA/PECR 合规。
- **轻量追踪脚本**：兼容 SPA（pushState/hash），也支持直接 Events API 上报。
- **Stats API（外部/内部）**：
  - time-series（metric: visitors/pageviews/…，interval: minute/hour/date/week/month）
  - aggregate（汇总）、breakdown（按 property 维度聚合）
  - filters（utm_source/device/browser/country/path/host 等）
  - 分页/limit、with_imported 开关
- **Realtime**：当前在线、最近页面、热点入口。
- **目标/转化与漏斗**：按会话链路统计到达率、转化率、时长（CE 精简；商业版更强）。
- **收入追踪**：电商/转化收入聚合与分解（商业版完善）。
- **团队/共享链接**：公开只读视图、邀请/角色。
- **报表通知**：周/月报，Slack/Email 通知，峰值提醒。
- **Bot 过滤**：UA/数据中心 IP 过滤，算法判定（CE 基础版）。
- **GSC 集成、导出**：关键词报告、CSV/Stats API 导出。

---

## 二、InstaSight 现状对齐

- **架构**：Next.js 15（App Router）+ Tinybird（实时分析）+ Neon（元数据）+ Upstash（生产缓存）。
- **热路径**：`/api/events` 默认仅 Tinybird；有负缓存与 trackingId→website 缓存。
- **分析**：`tinybird/endpoints/*` 已有 overview、timeseries、top_pages、traffic_sources、device_breakdown、realtime_visitors。
- **实时**：`/api/stream` 已改为轮询 JSON，周期由 `REALTIME_REFRESH_MS` 控制；`useRealtimeData` 轮询消费。
- **追踪脚本**：`public/script*.js`（标准/动态/4kb/直连），支持 DNT/GPC/consent、心跳与可选 idle；跨域/端点可配置；br/gzip 按需协商。

---

## 三、设计原则

- 不引入 ClickHouse 直连；全部通过 Tinybird pipes 实现统计。
- API 与 UI 尽量对齐 Plausible 使用体验，但遵循当前安全/缓存/鉴权约束（登录 + 站点归属）。
- 渐进增强：优先上线 Stats API 与 Realtime，对 GSC/报表做后续里程碑。

---

## 四、实施清单（TODO）

### 1) Stats API 对齐（高优先）

- **目标**：在 `app/api/analytics/[websiteId]` 暴露等价 Plausible 的查询能力（timeseries/aggregate/breakdown）。
- **任务**：
  1. 路由扩展 query 参数：`metric/period/interval/filters/property/limit/page`，并兼容现有 `type=overview|chart|pages|sources|devices`。
  2. 新增转换层（`lib/tinybird/analytics.ts`）：
     - period → dateRange(from,to)
     - interval → `timeseries_data.pipe` 的 `interval`
     - metric → `metric`
     - filters/property → WHERE 条件（utm_source/device/browser/country/path/url 等）
  3. **响应结构对齐**：
     - timeseries：`{ plot, labels, present_index, interval }`
     - aggregate/breakdown：`{ results, meta? }`
  4. **缓存**：Stats API 只读查询加短期 Redis 缓存（30-120s，可配置）。
- **Tinybird**：
  - 扩展/新增 pipes：`timeseries_data.pipe`（支持 metric: visitors/pageviews/revenue）、`breakdown.pipe`、`aggregate.pipe`。

### 2) Realtime（中高）

- **目标**：在轮询接口 `/api/stream` 中增加“当前在线 + 最近页面/热门入口”字段，前端统一用 `useRealtimeData`。
- **任务**：
  - Tinybird 新增 `realtime_top_pages.pipe`、`realtime_entries.pipe`（近 30-60 分钟窗口）。
  - `/api/stream` 返回体新增：`topPages`, `recentEntries`；继续下发 `refreshIntervalMs`（由 `REALTIME_REFRESH_MS` 控制）。
  - `real-time-visitors.tsx` 与 `real-time-activity.tsx` 复用 `useRealtimeData`，移除各自独立请求。

### 3) 目标/漏斗（中高）

- **目标**：Neon 存目标定义；Tinybird 以会话序列统计漏斗；前端提供 Goals/Funnel 视图。
- **任务**：
  - Neon：`goals` 表扩展（步骤/匹配规则/类型）。
  - Tinybird：`funnel_steps.pipe`（按 session_id、timestamp 排序，计算到达率/转化率/平均用时）、`goals_conversion.pipe`。
  - API：`/api/analytics/[websiteId]?type=funnel|goals`。
  - UI：新增 Goals/Funnels 页卡。

### 4) 收入归因（中）

- **目标**：`event_type=payment|conversion` + revenue 字段完成收入/渠道/活动聚合。
- **任务**：
  - 追踪脚本：保留最小事件集，按隐私规则上报 revenue。
  - Tinybird：`revenue_overview.pipe`、`channel_revenue.pipe`、`campaign_revenue.pipe`（已有基础迭代）。
  - API：`/api/revenue/[websiteId]` 增加 breakdown 维度与过滤。

### 5) Bot 过滤（中）

- **目标**：参考 Plausible CE，在写入侧/查询侧共同过滤。
- **任务**：
  - 写入：`lib/security/validation.ts` + `public/script.js` 扩展 automation 检测，事件写入 `is_bot` 标记。
  - 查询：pipes 内 `WHERE is_bot = 0`；并提供 `include_bots` 调试参数。

### 6) 共享/公开链接（中）

- **目标**：支持公开只读 Dashboard（无需登录）。
- **任务**：
  - Neon：`websites.sharedSlug`（随机 slug）、`isPublic`。
  - 路由：`/p/[slug]` 渲染只读仪表盘，API 根据 slug → trackingId → Tinybird 查询（限制敏感维度）。

### 7) 导出与报表（中）

- **导出**：`GET /api/analytics/[websiteId]?type=export&format=csv` 串 Tinybird 导出，流式响应。
- **报表**：Vercel Cron 调用 `lib/actions`，生成周/月报数据，通过 Resend 或 Slack webhook 推送。

### 8) GSC 集成（次）

- 提供配置页与 token 存储；查询走 Google API；在仪表盘透出关键词卡片。

---

## 五、接口与数据面变更

- **API**：
  - `app/api/analytics/[websiteId]/route.ts`：扩展 Stats API 参数/返回；
  - `app/api/stream/route.ts`：返回体新增 `topPages/recentEntries`；
  - `app/api/revenue/[websiteId]/route.ts`：扩展 breakdown 维度；
  - `app/api/public/[slug]/route.ts`（新增）：公开只读视图数据接口。
- **Tinybird pipes（新增/扩展）**：
  - `timeseries_data.pipe`（metric/interval 扩展）、`breakdown.pipe`、`aggregate.pipe`；
  - `realtime_top_pages.pipe`、`realtime_entries.pipe`；
  - `funnel_steps.pipe`、`goals_conversion.pipe`；
  - `revenue_overview.pipe`、`channel_revenue.pipe`、`campaign_revenue.pipe`。
- **Neon**：
  - 表 `goals` 扩展字段；
  - `websites` 增加 `sharedSlug`、`isPublic`。

---

## 六、前端/UI 改造

- 统一实时数据源：`useRealtimeData` 轮询 `/api/stream`；
- 新增 Goals/Funnels 视图；
- Dashboard 增加 breakdown/aggregate 维度选择器（property/filters/limit/page）；
- 导出与共享按钮；
- 报表设置页（邮件/Slack、周期）。

---

## 七、环境变量

- `REALTIME_REFRESH_MS`：服务端建议轮询周期（也在响应体返回）；
- 报表/通知：`SLACK_WEBHOOK_URL`、`REPORT_CRON_KEY`（示例）；
- 公开链接：无需额外 env。

---

## 八、安全与合规

- 延续 `withSecurity(SECURITY_CONFIGS.tracking)`、`lib/security/validation.ts`、DNT/GPC/consent；
- 公开视图仅可读且屏蔽敏感维度（如 IP/细粒度 UA）；
- CORS 与速率限制按现有策略执行。

---

## 九、里程碑

- **M1**：Stats API 对齐 + Realtime 扩展（topPages/recentEntries）
- **M2**：Goals/Funnels + 收入 breakdown
- **M3**：共享/公开链接 + 导出/报表
- **M4**：GSC 集成 + 高级过滤（机器人/数据中心黑名单迭代）

---

## 十、与 Plausible 的差异化优势

- Tinybird 实时聚合与本地/云端自动切换，延迟更低；
- 追踪脚本支持心跳 + 可选 idle，配合客户端 TTL 去噪更贴近“真实在线”；
- 热路径缓存与 Upstash Redis 生产切换，稳定可控；
- 多脚本形态（4kb/动态/直连）灵活满足不同站点性能需求。

> 注：本计划为“对齐 + 超越”的集成方案，避免简单复制，充分利用 InstaSight 现有工程与实时优势。
