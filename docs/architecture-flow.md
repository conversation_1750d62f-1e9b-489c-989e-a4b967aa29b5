### InstaSight 架构：函数调用与数据流

```mermaid
graph TD
  %% High-level function call and data flow

  subgraph "Browser / Client"
    ScriptJS["public/script.js\ntrack() / flushOnHide()"]
    RealtimeUI["Dashboard realtime UI\ncomponents/*real-time-* + useRealtimeData()"]
    ScriptJS -->|"POST /api/events"| EventsRoute
  RealtimeUI -->|"GET /api/stream?websiteId (poll JSON)"| StreamRoute
  end

  subgraph "API (Next.js)"
    EventsRoute["app/api/events/route.ts\nPOST handler"]
    ScriptRoute["app/api/script/[trackingId]/route.ts\nGET handler"]
    RealtimeRoute["app/api/realtime/[websiteId]/route.ts\nGET handler"]
  StreamRoute["app/api/stream/route.ts\nGET (Polling JSON)"]
    AnalyticsRoute["app/api/analytics/[websiteId]/route.ts"]
    RevenueRoute["app/api/revenue/[websiteId]/route.ts"]
  end

  subgraph "Security & Validation"
    WithSec["withSecurity(SECURITY_CONFIGS.tracking)"]
    Validate["validate(VALIDATION_SCHEMAS.event)"]
    TrackUtils["lib/utils/tracking.ts\nvalidateTrackingData / isBot / parseUserAgent"]
  end

  subgraph "Cache"
    CacheLayer["lib/cache/redis.ts\nMemoryCache (dev) | UpstashCache (prod)"]
  end

  subgraph "DB & ORM (Neon via Drizzle)"
    DB["lib/db/index.ts\nDrizzle client"]
    Websites["lib/db/schema.ts\nwebsites table"]
    EventsTbl["lib/db/schema.ts\nevents table"]
    Queries["lib/db/queries.ts\ngetWebsiteByTrackingId()"]
  end

  subgraph "Tinybird"
    TBClient["lib/tinybird/client.ts\ndetectEnvironment / getApiUrl / getToken / sendEvent / queryEndpoint"]
    TBEvents["lib/tinybird/events.ts\ntrackEvent() / formatEventForTinybird()"]
    TBAnalytics["lib/tinybird/analytics.ts\ngetWebsiteOverview() / getTimeSeriesData() / getRealTimeVisitors() ..."]
  end

  subgraph "Server Actions & Pages"
    DashboardActions["lib/actions/dashboard.ts\nverifyWebsiteAccess() / getDashboardOverview() / getChartData() ..."]
    CachedActions["lib/actions/cached-dashboard.ts\nget* cached wrappers"]
    Pages["app/dashboard/... pages"]
  end

  %% Events ingestion flow
  EventsRoute --> WithSec
  EventsRoute --> Validate
  EventsRoute --> TrackUtils
  EventsRoute --> CacheLayer
  CacheLayer -->|"miss"| DB
  DB --> Websites
  EventsRoute -->|"EVENT_WRITE_MODE !== 'tinybird'"| EventsTbl
  EventsRoute --> TBEvents
  TBEvents --> TBClient

  %% Script distribution
  ScriptJS -. "<script src=/api/script/[trackingId]>" .-> ScriptRoute
  ScriptRoute --> Queries
  Queries --> DB
  ScriptRoute --> TBClient

  %% Realtime polling & API
  StreamRoute --> DB
  DB --> Websites
  StreamRoute --> TBAnalytics
  RealtimeRoute --> DB
  DB --> Websites
  RealtimeRoute --> TBClient

  %% Dashboard reads
  Pages --> DashboardActions
  DashboardActions --> DB
  DB --> Websites
  DashboardActions --> TBAnalytics
  CachedActions --> DashboardActions
```
