# DataFast - 收入驱动的网站分析平台

## 概述

DataFast 是一个现代化的网站分析平台，专注于收入驱动的洞察而非虚荣指标。基于 Next.js 15 构建，现已集成 Tinybird 实时分析数据库，提供真正的实时分析能力。

## 🚀 核心功能

### 1. 收入归因分析

- **功能描述**：追踪并分析哪些流量来源和营销活动真正驱动销售
- **使用方法**：
  - 在仪表板中查看"流量来源"报告
  - 筛选特定时间段和来源渠道
  - 查看每个来源的转化率和收入贡献
- **技术实现**：通过 UTM 参数追踪和支付事件关联

### 2. 实时访客监控

- **功能描述**：实时观察访客在网站上的浏览行为
- **使用方法**：
  - 访问仪表板右侧的"实时访客"面板
  - 查看最近 30 分钟内的访客活动
  - 显示访客位置、设备、当前页面等信息
- **技术实现**：基于 Tinybird 的实时查询和 WebSocket 推送

### 3. 目标跟踪

- **功能描述**：设置并跟踪转化目标，监控客户旅程
- **使用方法**：
  1. 在网站设置中点击"创建目标"
  2. 定义目标名称、事件类型和目标值
  3. 在仪表板中查看目标完成情况
- **支持的目标类型**：
  - 页面浏览量目标
  - 自定义事件目标
  - 收入目标

### 4. 轻量级追踪脚本

- **功能描述**：仅 4KB 的追踪脚本，不会拖慢网站速度
- **安装方法**：
  ```html
  <script
    src="https://yourhost.com/script-tinybird.js"
    data-website-id="your-website-id"
    data-tinybird-token="your-tinybird-token"
    data-domain="yourdomain.com"
    defer
  ></script>
  ```
- **自动追踪功能**：
  - 页面浏览
  - 外部链接点击
  - 表单提交
  - SPA 路由变化
  - 支付事件

### 5. 隐私合规

- **功能描述**：GDPR 友好的分析，在不损害洞察的情况下保护隐私
- **实现方式**：
  - 自动过滤机器人流量
  - 数据匿名化处理
  - 用户可控的追踪设置
  - 敏感数据自动清理

### 6. 支付集成

- **功能描述**：直接集成 Stripe、LemonSqueezy 和 Polar
- **支持功能**：
  - 自动检测支付成功页面
  - 收入事件自动追踪
  - 转化漏斗分析
  - 客户生命周期价值计算

## 📊 分析报告

### 概览仪表板

显示核心指标：

- **独立访客数**：去重统计的访客数量
- **页面浏览量**：总的页面访问次数
- **跳出率**：仅访问一个页面即离开的访客比例
- **平均会话时长**：访客在网站停留的平均时间
- **总收入**：追踪到的所有收入总和

### 时间序列图表

- **访客趋势**：按小时/天/周/月显示访客变化
- **收入趋势**：收入随时间的变化曲线
- **页面浏览趋势**：流量变化趋势分析

### 热门页面分析

显示每个页面的详细统计：

- 页面浏览量
- 独立访客数
- 页面跳出率
- 平均停留时间

### 流量来源分析

追踪访客来源：

- **直接访问**：直接输入网址或书签
- **搜索引擎**：Google、Bing 等搜索引擎
- **社交媒体**：Facebook、Twitter、LinkedIn 等
- **UTM 来源**：营销活动的具体来源
- 每个来源的转化率和收入贡献

### 设备和浏览器分析

- **设备类型**：桌面、移动、平板设备分布
- **浏览器统计**：Chrome、Firefox、Safari 等使用率
- **操作系统**：Windows、macOS、Linux、移动系统分布

## 🔧 技术架构

### 前端技术栈

- **框架**：Next.js 15 with App Router
- **UI 组件**：shadcn/ui + TailwindCSS
- **状态管理**：Zustand
- **图表库**：Recharts
- **认证**：Auth.js (NextAuth.js)

### 数据库架构

- **用户管理**：PostgreSQL + Drizzle ORM
- **分析数据**：Tinybird (基于 ClickHouse)
- **实时功能**：WebSocket + Server-Sent Events

### 部署和运维

- **推荐部署**：Vercel
- **邮件服务**：Resend
- **监控**：内置的实时指标监控
- **备份**：自动数据备份和灾难恢复

## 🚀 快速开始

### 1. 环境配置

```bash
# 克隆项目
git clone <repository-url>
cd datafast

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.local
```

### 2. 环境变量设置

```env
# 用户数据库 (PostgreSQL)
DATABASE_URL="postgresql://username:password@localhost:5432/datafast"

# Tinybird 配置
TINYBIRD_API_URL="https://api.tinybird.co"
TINYBIRD_TOKEN="your_tinybird_token"

# 认证配置
AUTH_SECRET="your-random-secret"
AUTH_RESEND_KEY="your-resend-api-key"
AUTH_GOOGLE_ID="your-google-oauth-id"
AUTH_GOOGLE_SECRET="your-google-oauth-secret"

# 应用配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

### 3. 数据库初始化

```bash
# 生成数据库迁移
npm run db:generate

# 执行迁移
npm run db:migrate

# 启动开发服务器
npm run dev
```

### 4. Tinybird 设置

1. 创建 Tinybird 账户：https://tinybird.co
2. 创建新的工作空间
3. 上传 `tinybird/` 目录中的数据源和端点配置
4. 获取 API Token 并添加到环境变量

### 5. 网站追踪设置

1. 登录 DataFast 仪表板
2. 创建新网站项目
3. 复制提供的追踪代码
4. 将追踪代码添加到网站的 `<head>` 标签中

## 📱 JavaScript API

DataFast 提供了丰富的 JavaScript API 用于自定义事件追踪：

### 基础事件追踪

```javascript
// 追踪自定义事件
datafast.track("button_click", { button: "signup" });

// 追踪转化
datafast.trackConversion(99.99, "USD");

// 追踪收入
datafast.trackRevenue(299.99, "USD");

// 用户识别
datafast.identify("user123", { plan: "pro" });

// 手动页面浏览追踪
datafast.trackPageView();
```

### 高级配置

```javascript
// 配置追踪脚本
<script
  src="/script-tinybird.js"
  data-website-id="your-website-id"
  data-tinybird-token="your-token"
  data-domain="yourdomain.com"
  data-auto="true"
  data-debug="false"
  data-batch-size="5"
  data-flush-interval="3000"
  defer
></script>
```

## 🔐 隐私和安全

### 数据保护措施

- **自动机器人检测**：过滤爬虫和机器人流量
- **XSS 防护**：自动清理恶意脚本
- **CSRF 保护**：请求伪造保护
- **速率限制**：API 端点访问限制
- **数据验证**：输入数据自动验证和清理

### 隐私合规

- **最小化数据收集**：只收集分析必需的数据
- **数据匿名化**：IP 地址和个人信息匿名处理
- **用户控制**：用户可以选择退出追踪
- **透明度**：清晰的数据使用说明

## 🎯 支付检测

DataFast 自动检测以下支付平台的成功事件：

### 支持的支付平台

- **Stripe**：成功页面和 Webhook 检测
- **LemonSqueezy**：重定向成功检测
- **Polar**：结账完成检测

### 自动检测规则

- URL 包含 `/success`、`/thank-you` 等关键词
- URL 参数 `payment=success`
- 特定支付平台的成功页面模式

## 📈 性能优势

### Tinybird 集成优势

- **查询速度**：毫秒级查询响应
- **实时处理**：事件到查询延迟 < 1 秒
- **高并发**：支持数千并发查询
- **自动扩展**：无需手动管理基础设施

### 成本效益

- **存储效率**：比 PostgreSQL 节省 10 倍存储空间
- **查询成本**：按使用量付费的灵活定价
- **开发效率**：预构建的分析功能

## 🛠️ 开发和维护

### 可用的 npm 脚本

```bash
# 开发
npm run dev          # 启动开发服务器
npm run build        # 生产构建
npm run start        # 启动生产服务器

# 数据库
npm run db:generate  # 生成迁移
npm run db:migrate   # 执行迁移
npm run db:studio    # 打开 Drizzle Studio
npm run db:push      # 推送 schema 到数据库

# 代码质量
npm run lint         # 运行 Biome 检查
npm run format       # 格式化代码
npm run format:check # 检查代码格式
```

### 监控和调试

- **实时日志**：应用和数据库操作日志
- **性能监控**：查询性能和响应时间
- **错误追踪**：自动错误报告和处理
- **调试模式**：开发环境的详细日志

## 🤝 社区和支持

### 获取帮助

- **文档**：完整的 API 和配置文档
- **GitHub Issues**：报告 Bug 和功能请求
- **社区论坛**：与其他用户交流经验

### 贡献指南

- Fork 项目并创建功能分支
- 遵循代码规范和最佳实践
- 提交 Pull Request 前运行测试
- 详细描述变更内容和原因

---

**DataFast** - 将网站访客转化为收入洞察的强大工具 🚀

_构建下一代实时分析平台，专注于真正重要的指标。_
