# DataFast 追踪脚本对比指南

## 📊 脚本文件概览

DataFast 提供多种追踪脚本，满足不同的性能和功能需求：

| 脚本文件             | 大小  | 用途                | 推荐场景                  |
| -------------------- | ----- | ------------------- | ------------------------- |
| `script-4kb.js`      | ~4KB  | 🏆 **极简轻量级**   | 对性能要求极高的网站      |
| `script.js`          | ~12KB | 🎯 **标准功能版**   | 大多数网站的默认选择      |
| `script-dynamic.js`  | ~12KB | 🔄 **智能环境版**   | 需要开发/生产自动切换     |
| `script-tinybird.js` | ~16KB | 🚀 **直连高性能版** | 高流量网站，直连 Tinybird |

---

## 🏆 script-4kb.js - 极简轻量级版本

### ✨ 特点

- **📏 体积极小**: 仅 4KB，比竞品小 5-10 倍
- **⚡ 加载极快**: 减少页面加载时间影响
- **🎯 核心功能**: 专注页面访问和基础事件追踪
- **📱 移动优化**: 特别适合移动端网站

### 🔧 功能列表

- ✅ 页面浏览追踪
- ✅ 自定义事件追踪
- ✅ 访客 ID 和会话管理
- ✅ 设备信息检测（基础）
- ✅ UTM 参数解析
- ✅ SPA 路由追踪
- ✅ 机器人检测（基础）
- ❌ 批量事件处理
- ❌ 高级地理位置
- ❌ 详细设备信息
- ❌ 高级缓存策略

### 📥 使用方法

```html
<!-- 4KB 轻量级版本 -->
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID?mode=4kb"></script>

<!-- 或使用别名 -->
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID?mode=mini"></script>
```

### 🎯 推荐场景

- 🌐 性能敏感的网站
- 📱 移动端优先的应用
- 🚀 需要极速加载的落地页
- 💰 流量成本敏感的项目
- 🎯 只需基础追踪功能

---

## 🎯 script.js - 标准功能版本

### ✨ 特点

- **🔄 代理模式**: 通过您的服务器发送数据
- **🛡️ 最佳兼容性**: 避免跨域和广告拦截问题
- **⚖️ 功能平衡**: 功能丰富但体积适中
- **🔧 易于配置**: 简单的参数配置

### 🔧 功能列表

- ✅ 所有 4KB 版本功能
- ✅ 事件队列和批处理
- ✅ 详细设备信息检测
- ✅ 高级机器人检测
- ✅ 错误重试机制
- ✅ 本地存储管理
- ✅ 完整的 UTM 追踪

### 📥 使用方法

```html
<!-- 标准版本（默认） -->
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID"></script>

<!-- 或明确指定 -->
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID?mode=default"></script>
```

### 🎯 推荐场景

- 🏢 大多数企业网站
- 🛒 电商平台
- 📰 内容网站
- 🎓 教育平台
- 💼 B2B 应用

---

## 🔄 script-dynamic.js - 智能环境版本

### ✨ 特点

- **🤖 智能检测**: 自动识别开发/生产环境
- **🔄 自动切换**: 本地开发时使用本地端点
- **🛠️ 开发友好**: 丰富的调试信息
- **🌐 生产优化**: 生产环境自动优化

### 🔧 功能列表

- ✅ 所有标准版本功能
- ✅ 环境自动检测
- ✅ 开发调试模式
- ✅ 动态端点切换
- ✅ 环境特定配置
- ✅ 详细错误日志

### 📥 使用方法

```html
<!-- 智能环境版本 -->
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID?mode=dynamic"></script>
```

### 🎯 推荐场景

- 👩‍💻 开发团队使用
- 🔄 需要多环境部署
- 🧪 测试和调试阶段
- 🚀 CI/CD 自动化部署

---

## 🚀 script-tinybird.js - 直连高性能版本

### ✨ 特点

- **⚡ 直连 API**: 直接发送到 Tinybird，无服务器代理
- **🚀 高性能**: 减少服务器负载和延迟
- **📡 CDN 友好**: 适合 CDN 分发
- **🔥 高流量优化**: 专为高流量网站设计

### 🔧 功能列表

- ✅ 所有标准版本功能
- ✅ 直连 Tinybird API
- ✅ 高级批处理策略
- ✅ 智能重试机制
- ✅ 网络优化
- ✅ 详细性能监控

### 📥 使用方法

```html
<!-- 直连高性能版本 -->
<script
  src="https://yourdomain.com/api/script/YOUR_TRACKING_ID?mode=direct"
  data-tinybird-token="YOUR_TINYBIRD_TOKEN"
></script>
```

### 🎯 推荐场景

- 🎯 高流量网站
- ⚡ 对性能要求极高
- 🌐 使用 CDN 分发
- 🔄 减少服务器负载
- 📡 需要实时数据

---

## 📊 性能对比

| 指标           | 4KB 版本 | 标准版本 | 智能版本 | 直连版本 |
| -------------- | -------- | -------- | -------- | -------- |
| **文件大小**   | 4KB      | 12KB     | 12KB     | 16KB     |
| **加载时间**   | <50ms    | <150ms   | <150ms   | <200ms   |
| **服务器负载** | 中等     | 中等     | 中等     | 极低     |
| **功能完整性** | 基础     | 完整     | 完整+    | 完整++   |
| **兼容性**     | 优秀     | 优秀     | 优秀     | 良好     |
| **配置复杂度** | 简单     | 简单     | 中等     | 中等     |

---

## 🎯 选择建议

### 🏆 性能优先 → `script-4kb.js`

如果您的网站：

- 对加载速度要求极高
- 移动端用户居多
- 只需要基础分析功能
- 流量成本敏感

### 🎯 功能平衡 → `script.js`

如果您的网站：

- 需要完整的分析功能
- 要求最佳兼容性
- 不想处理复杂配置
- 大多数企业网站

### 🔄 开发友好 → `script-dynamic.js`

如果您的团队：

- 需要频繁开发部署
- 使用多环境部署
- 重视开发体验
- 需要调试功能

### 🚀 高性能 → `script-tinybird.js`

如果您的网站：

- 流量非常大
- 对实时性要求高
- 愿意配置 CORS
- 使用 CDN 分发

---

## 📝 迁移指南

### 从其他分析工具迁移

**从 Google Analytics:**

```html
<!-- 删除 GA 代码 -->
<!-- <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script> -->

<!-- 替换为 DataFast 4KB 版本 -->
<script src="/api/script/YOUR_TRACKING_ID?mode=4kb"></script>
```

**性能提升对比:**

- 文件大小: 45KB → 4KB (减少 91%)
- 加载时间: 500ms → 50ms (减少 90%)
- 第三方请求: 3 个 → 0 个

### 版本间迁移

所有版本的 API 兼容，只需更改 `mode` 参数：

```html
<!-- 从标准版升级到4KB版 -->
<script src="/api/script/YOUR_ID?mode=default"></script>
↓
<script src="/api/script/YOUR_ID?mode=4kb"></script>
```

---

## 🛠️ 自定义配置

### 通用属性

所有版本都支持这些属性：

```html
<script
  src="/api/script/YOUR_ID?mode=4kb"
  data-website-id="YOUR_ID"
  data-domain="yourdomain.com"
  data-auto="true"
  data-debug="false"
></script>
```

### 版本特定属性

**直连版本额外属性:**

```html
<script
  data-tinybird-token="YOUR_TOKEN"
  data-batch-size="10"
  data-flush-interval="5000"
></script>
```

**智能版本额外属性:**

```html
<script data-environment="local" data-api-url="http://localhost:7181"></script>
```

---

**📞 技术支持**: 如有问题，请查看使用指南或联系技术支持团队。
