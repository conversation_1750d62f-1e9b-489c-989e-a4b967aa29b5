# DataFast - 环境变量配置完整示例

## 📋 快速开始

复制以下内容到您的 `.env.local` 文件中：

```env
# =============================================================================
# DataFast - 完整环境配置示例
# 复制此文件为 .env.local 并填写实际值
# =============================================================================

# =============================================================================
# 🚀 核心应用配置
# =============================================================================
# 应用运行环境（Next.js 会自动设置，无需手动指定）
# NODE_ENV="development"

# 应用基础 URL（生产环境设置为实际域名）
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# 追踪脚本 URL（通常与应用 URL 相同）
NEXT_PUBLIC_TRACKING_SCRIPT_URL="http://localhost:3000"

# =============================================================================
# 🗄️ 数据库配置 - 用户和配置数据 (PostgreSQL)
# =============================================================================
DATABASE_URL="postgresql://username:password@localhost:5432/datafast_dev"

# =============================================================================
# 📊 Tinybird 分析配置 - 自动环境检测
# =============================================================================

# --- 生产环境 Tinybird 配置 ---
# Tinybird API 基础 URL (生产环境)
TINYBIRD_API_URL="https://api.tinybird.co"

# Tinybird 工作空间的 API Token (生产环境)
# 从 Tinybird 仪表板获取: https://www.tinybird.co/tokens
TINYBIRD_TOKEN="p.your_production_tinybird_token_here"

# --- 本地开发环境 Tinybird 配置 ---
# 本地 Tinybird API URL (tb local start 后可用)
TINYBIRD_API_URL_LOCAL="http://localhost:7181"

# 本地 Tinybird Token (运行 tb info 获取)
TINYBIRD_TOKEN_LOCAL="p.your_local_tinybird_token_here"

# --- 通用 Tinybird 配置 ---
# 查询超时时间 (毫秒)
TINYBIRD_TIMEOUT="10000"

# 强制环境类型 (local | production) - 可选，留空为自动检测
# TINYBIRD_ENVIRONMENT="local"

# =============================================================================
# 🔐 认证配置 (Auth.js / NextAuth.js)
# =============================================================================
# 随机密钥，用于 JWT 签名
# 生成命令: openssl rand -base64 32
AUTH_SECRET="your-random-secret-here-change-this-in-production"

# Resend 邮件服务 API 密钥
AUTH_RESEND_KEY="re_your-resend-api-key"

# Google OAuth 配置 (可选)
AUTH_GOOGLE_ID="your-google-oauth-client-id"
AUTH_GOOGLE_SECRET="your-google-oauth-client-secret"

# =============================================================================
# 🚀 缓存配置 (可选)
# =============================================================================
# Redis 配置 (用于缓存，可选)
REDIS_URL="redis://localhost:6379"

# =============================================================================
# 💳 支付配置 (可选)
# =============================================================================
# Stripe 配置 (用于支付检测，可选)
STRIPE_PUBLIC_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."

# =============================================================================
# 🛡️ 安全配置
# =============================================================================
# CORS 允许的域名 (生产环境请设置具体域名)
ALLOWED_ORIGINS="*"

# 安全 Cookie 设置 (生产环境建议启用)
SECURE_COOKIES="false"

# =============================================================================
# 🐛 调试配置 (开发环境)
# =============================================================================
# 日志级别 (debug | info | warn | error)
LOG_LEVEL="debug"

# Tinybird 调试开关
DEBUG_TINYBIRD="true"

# 追踪调试开关
DEBUG_TRACKING="true"

# WebSocket 调试开关
DEBUG_WEBSOCKET="true"

# =============================================================================
# 📈 监控配置 (可选)
# =============================================================================
# Sentry DSN (错误监控)
# SENTRY_DSN="https://your-sentry-dsn"

# Vercel Analytics (如果使用 Vercel 部署)
# NEXT_PUBLIC_VERCEL_ANALYTICS="true"

# =============================================================================
# ⚙️ 高级配置
# =============================================================================
# 最大文件上传大小 (字节)
MAX_FILE_SIZE="10485760"

# 最大事件批处理大小
MAX_BATCH_SIZE="100"

# 会话超时时间 (分钟)
SESSION_TIMEOUT="30"

# =============================================================================
# 🔧 开发环境特定配置
# =============================================================================
# 开发模式特性开关
ENABLE_DEVTOOLS="true"

# 模拟延迟 (毫秒，用于测试)
SIMULATE_DELAY="0"
```

## 🔧 环境特定配置说明

### 🏠 本地开发环境

当您在本地开发时，系统会自动检测并使用本地 Tinybird 实例：

- **自动检测条件**:
  - `hostname` 包含 `localhost` 或 `127.0.0.1`
  - `TINYBIRD_ENVIRONMENT="local"`

- **本地 Token 获取**:

  ```bash
  # 启动本地 Tinybird
  tb local start

  # 查看配置信息
  tb info

  # 复制 Local Token 到 TINYBIRD_TOKEN_LOCAL
  ```

### 🌐 生产环境

生产环境会自动使用云端 Tinybird 服务：

- **自动检测条件**:
  - API URL 不包含 `localhost`
  - 或显式设置 `TINYBIRD_ENVIRONMENT="production"`

- **生产 Token 配置**:
  1. 访问 [Tinybird Dashboard](https://www.tinybird.co/tokens)
  2. 创建新 Token，确保权限包括：
     - `events:append` (写入事件)
     - `pipes:read` (读取端点)
  3. 复制 Token 到 `TINYBIRD_TOKEN`

## 📡 追踪脚本模式

系统支持三种追踪脚本模式：

### 1. 默认模式 (推荐)

```html
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID"></script>
```

- 通过 API 代理发送数据
- 自动环境检测
- 最佳兼容性

### 2. 直接模式 (高性能)

```html
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID?mode=direct"></script>
```

- 直接发送到 Tinybird
- 减少服务器负载
- CDN 友好

### 3. 动态模式 (灵活)

```html
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID?mode=dynamic"></script>
```

- 客户端环境检测
- 自动选择最佳端点
- 开发/生产自动切换

## 🛡️ 生产环境安全检查清单

在部署到生产环境前，请确保：

- [ ] 使用强随机密钥作为 `AUTH_SECRET`
- [ ] 启用 `SECURE_COOKIES="true"`
- [ ] 设置具体的 `ALLOWED_ORIGINS`（不使用 `*`）
- [ ] 关闭调试模式 `DEBUG_*="false"`
- [ ] 配置生产环境的 `TINYBIRD_TOKEN`
- [ ] 设置真实的 `DATABASE_URL`
- [ ] 配置错误监控 `SENTRY_DSN`
- [ ] 验证 SSL 证书配置
- [ ] 设置适当的缓存策略

## 🔍 故障排除

### 环境检测问题

```javascript
// 在浏览器控制台中检查检测结果
console.log(window.datafast?.environment);
console.log(window.datafast?.config);
```

### Tinybird 连接测试

```bash
# 测试本地连接
curl http://localhost:7181/v0/pipes/overview_metrics.json

# 测试生产连接
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://api.tinybird.co/v0/pipes/overview_metrics.json
```

### Token 验证

```bash
# 检查 Token 权限
tb token ls

# 验证 Token 有效性
tb endpoint ls
```

## 📞 获取帮助

如果遇到配置问题：

1. 检查 [Tinybird 文档](https://www.tinybird.co/docs)
2. 查看浏览器开发者工具中的网络请求
3. 检查服务器日志中的错误信息
4. 确认所有必需的环境变量都已设置
