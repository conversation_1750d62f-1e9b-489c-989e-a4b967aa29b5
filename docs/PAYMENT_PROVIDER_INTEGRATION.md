# Payment Provider Integration System

This document describes the comprehensive payment provider integration system that enables automated revenue tracking with user-provided API keys.

## Overview

The payment provider integration system allows users to securely connect their own payment provider accounts (Stripe, LemonSqueezy, Polar, etc.) to enable automatic revenue tracking and attribution. This follows the DataFast model where users provide their own credentials rather than using a centralized payment system.

## Key Features

- **User-Provided API Keys**: Secure storage of encrypted payment provider credentials per user
- **Automatic Webhook Management**: Programmatic creation and management of webhook endpoints
- **Multi-Provider Support**: Native integrations with Stripe, LemonSqueezy, and Polar
- **Revenue Attribution**: Links revenue events to marketing channels and user sessions
- **Deduplication**: Prevents double-counting between webhook and client-side detection
- **Enhanced Payment Detection**: Comprehensive client-side payment success detection

## Architecture Components

### 1. Database Schema

#### Payment Provider Credentials Table

```sql
CREATE TABLE "payment_provider_credentials" (
  "id" uuid PRIMARY KEY,
  "user_id" uuid NOT NULL,
  "website_id" uuid,
  "provider" varchar(50) NOT NULL,
  "credential_type" varchar(50) NOT NULL,
  "encrypted_value" text NOT NULL,
  "key_prefix" varchar(10),
  "is_active" boolean DEFAULT true,
  "validation_status" varchar(20) DEFAULT 'pending',
  "created_at" timestamp DEFAULT now(),
  "updated_at" timestamp DEFAULT now()
);
```

#### Webhook Endpoints Table

```sql
CREATE TABLE "webhook_endpoints" (
  "id" uuid PRIMARY KEY,
  "user_id" uuid NOT NULL,
  "provider" varchar(50) NOT NULL,
  "webhook_id" varchar(255) NOT NULL,
  "webhook_url" text NOT NULL,
  "events" text NOT NULL,
  "is_active" boolean DEFAULT true,
  "last_triggered" timestamp,
  "created_at" timestamp DEFAULT now()
);
```

#### Revenue Events Table

```sql
CREATE TABLE "revenue_events" (
  "id" uuid PRIMARY KEY,
  "website_id" uuid NOT NULL,
  "user_id" uuid NOT NULL,
  "provider" varchar(50) NOT NULL,
  "transaction_id" varchar(255) NOT NULL,
  "amount" numeric(12,2) NOT NULL,
  "currency" varchar(3) NOT NULL,
  "deduplication_id" varchar(64) NOT NULL UNIQUE,
  "source" varchar(20) NOT NULL,
  "metadata" text,
  "processed_at" timestamp DEFAULT now()
);
```

### 2. Security & Encryption

#### PaymentCredentialsVault

- **Algorithm**: AES-256-GCM for authenticated encryption
- **Key Management**: Environment-based encryption key
- **Key Rotation**: 90-day rotation recommendations
- **Audit Logging**: All credential operations are logged

```typescript
// Example usage
const encrypted = PaymentCredentialsVault.encrypt("sk_test_...");
const decrypted = PaymentCredentialsVault.decrypt(encrypted);
```

### 3. Payment Provider Clients

#### Stripe Client

- **Credential Validation**: Live API validation
- **Webhook Management**: Automatic endpoint creation/deletion
- **Event Processing**: Revenue extraction from webhooks
- **Supported Events**:
  - `checkout.session.completed`
  - `payment_intent.succeeded`
  - `invoice.payment_succeeded`

#### LemonSqueezy Client (Planned)

- **API Integration**: LemonSqueezy API v1
- **Webhook Events**: Order creation and subscription events
- **Revenue Extraction**: Amount and currency from order data

#### Polar Client (Planned)

- **API Integration**: Polar API
- **Webhook Events**: Checkout and subscription events
- **Revenue Extraction**: Amount and currency from checkout data

## Implementation Guide

### 1. Environment Setup

Add the encryption key to your environment:

```bash
# Generate a secure encryption key
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Add to .env
PAYMENT_CREDENTIALS_ENCRYPTION_KEY="your_64_character_hex_key_here"
```

### 2. Database Migration

Run the migration to create the required tables:

```bash
# Apply the migration
psql $DATABASE_URL -f lib/db/migrations/0001_add_payment_provider_tables.sql
```

### 3. Frontend Integration

Add the payment provider setup component to your dashboard:

```tsx
import { PaymentProviderSetup } from "@/components/dashboard/payment-provider-setup";

export default function SettingsPage() {
  return (
    <div>
      <PaymentProviderSetup
        websiteId={websiteId}
        onProviderConfigured={(provider) => {
          console.log(`${provider} configured successfully`);
        }}
      />
    </div>
  );
}
```

### 4. Stripe Integration Example

For Stripe Checkout, include metadata for attribution:

```javascript
// When creating Stripe Checkout session
const session = await stripe.checkout.sessions.create({
  // ... other parameters
  metadata: {
    instasight_visitor_id: cookieStore.get("instasight_visitor_id")?.value,
    instasight_session_id: cookieStore.get("instasight_session_id")?.value,
    utm_source: searchParams.get("utm_source"),
    utm_medium: searchParams.get("utm_medium"),
    utm_campaign: searchParams.get("utm_campaign"),
  },
  success_url: "https://yoursite.com/success?session_id={CHECKOUT_SESSION_ID}",
});
```

### 4. Frontend Integration Examples

#### Setting Up Payment Providers

```tsx
// Global credentials (applies to all websites)
<PaymentProviderSetup
  onProviderConfigured={(provider) => {
    console.log(`${provider} configured globally`);
  }}
/>

// Website-specific credentials
<PaymentProviderSetup
  websiteId="website-uuid-here"
  onProviderConfigured={(provider) => {
    console.log(`${provider} configured for specific website`);
  }}
/>
```

### 5. API Usage Examples

#### Setup Global Credentials

```typescript
const response = await fetch("/api/payment-providers", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    provider: "stripe",
    credentials: [
      { type: "secret_key", value: "sk_test_..." },
      { type: "webhook_secret", value: "whsec_..." },
    ],
    // No websiteId = global credentials
  }),
});
```

#### Setup Website-Specific Credentials

```typescript
const response = await fetch("/api/payment-providers", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    provider: "stripe",
    credentials: [{ type: "secret_key", value: "sk_test_..." }],
    websiteId: "website-uuid-here", // Website-specific
  }),
});
```

#### Check Webhook Status

```typescript
// Check global webhook
const globalStatus = await fetch("/api/webhooks/stripe/user123/global");

// Check website-specific webhook
const websiteStatus = await fetch("/api/webhooks/stripe/user123/website456");
```

## API Endpoints

### POST /api/payment-providers

Setup payment provider credentials

```typescript
interface SetupRequest {
  provider: "stripe" | "lemonsqueezy" | "polar";
  credentials: {
    type: string;
    value: string;
  }[];
  websiteId?: string;
}
```

### GET /api/payment-providers

Get configured payment providers

### DELETE /api/payment-providers

Remove payment provider integration

### POST /api/webhooks/[provider]/[userId]/[websiteId]

Process payment provider webhooks for specific website

**URL Structure:**

- `[provider]`: Payment provider (stripe, lemonsqueezy, polar)
- `[userId]`: User's unique ID
- `[websiteId]`: Website ID or 'global' for user-level credentials

## Multi-Website Architecture

### How It Works

The system supports multiple websites per user, each with their own payment provider credentials:

#### Credential Hierarchy

1. **Website-Specific Credentials**: Stored with `websiteId` - highest priority
2. **Global Credentials**: Stored with `websiteId = null` - fallback option

#### Webhook URL Structure

```
/api/webhooks/[provider]/[userId]/[websiteId]
```

**Examples:**

- Website-specific: `/api/webhooks/stripe/user123/website456`
- Global credentials: `/api/webhooks/stripe/user123/global`

#### Credential Resolution Logic

```typescript
// 1. Try website-specific credentials first
const websiteCredentials = await getCredentials(userId, provider, websiteId);

// 2. Fall back to global credentials if none found
if (!websiteCredentials) {
  const globalCredentials = await getCredentials(userId, provider, null);
}
```

### Usage Scenarios

#### Scenario 1: Single Website with Global Credentials

```typescript
// User has one website, uses global Stripe credentials
// Webhook URL: /api/webhooks/stripe/user123/global
// Credentials: stored with websiteId = null
```

#### Scenario 2: Multiple Websites with Different Credentials

```typescript
// User has multiple websites, each with different Stripe accounts
// Website A: /api/webhooks/stripe/user123/websiteA
// Website B: /api/webhooks/stripe/user123/websiteB
// Credentials: stored with respective websiteId values
```

#### Scenario 3: Multiple Providers Per Website

```typescript
// Website A uses both Stripe and LemonSqueezy
// Setup Stripe for Website A
await setupCredentials("stripe", websiteA);
// Setup LemonSqueezy for Website A
await setupCredentials("lemonsqueezy", websiteA);

// Webhook URLs:
// /api/webhooks/stripe/user123/websiteA
// /api/webhooks/lemonsqueezy/user123/websiteA
```

#### Scenario 4: Mixed Global and Website-Specific

```typescript
// Global Stripe credentials (fallback)
await setupCredentials("stripe", null);
// Website A: Specific Stripe + LemonSqueezy
await setupCredentials("stripe", websiteA);
await setupCredentials("lemonsqueezy", websiteA);
// Website B: Uses global Stripe + specific LemonSqueezy
await setupCredentials("lemonsqueezy", websiteB);

// Result:
// Website A: Uses specific Stripe + specific LemonSqueezy
// Website B: Uses global Stripe + specific LemonSqueezy
// Website C: Uses global Stripe only
```

## Revenue Attribution Flow

1. **User provides API keys** → System validates and stores encrypted credentials (per website or globally)
2. **Webhook endpoints created** → Automatic webhook configuration with website-specific URLs
3. **Customer makes payment** → Payment provider processes transaction
4. **Webhook received** → System processes webhook using correct credentials for that website
5. **Revenue attributed** → Links payment to specific website, marketing channels, and user sessions
6. **Analytics updated** → Revenue data flows into Tinybird analytics with proper website attribution

## Enhanced Payment Detection

The client-side `detectPayment()` function now supports:

### Detection Strategies

1. **Provider-specific parameters** (Stripe session_id, PayPal tx, etc.)
2. **URL pattern matching** (/success, /thank-you, etc.)
3. **DOM element detection** (payment confirmation elements)
4. **Structured data** (JSON-LD, meta tags)

### Amount Extraction

- URL parameters (amount, total, value, etc.)
- DOM data attributes ([data-amount], [data-total])
- CSS selectors (.order-total, .payment-amount)
- JSON-LD structured data
- Meta tag properties

### Currency Detection

- URL parameters (currency, cc)
- DOM data attributes ([data-currency])
- Currency symbols in page content ($, €, £, etc.)

## Security Considerations

### Credential Storage

- All credentials encrypted at rest using AES-256-GCM
- Encryption keys stored separately from database
- Key prefixes stored for identification (first 6 characters)
- Regular validation and rotation recommendations

### Webhook Security

- Signature verification for all incoming webhooks
- User-specific webhook URLs for isolation
- Rate limiting and abuse prevention
- Comprehensive audit logging

### Access Control

- User-specific credential isolation
- Website-level credential scoping (optional)
- Role-based access control integration
- Audit trails for all operations

## Monitoring & Debugging

### Webhook Status

Check webhook endpoint status:

```bash
GET /api/webhooks/stripe/{userId}
```

### Revenue Event Logs

Monitor revenue events in the database:

```sql
SELECT * FROM revenue_events
WHERE user_id = 'user-uuid'
ORDER BY created_at DESC;
```

### Error Handling

- Webhook processing failures are logged
- Credential validation errors are returned to users
- Deduplication prevents double-counting
- Graceful fallbacks for missing data

## Testing

### Local Development

1. Use Stripe test keys for development
2. Use webhook testing tools (ngrok, Stripe CLI)
3. Test payment detection on success pages
4. Verify revenue attribution in analytics

### Production Deployment

1. Generate secure encryption keys
2. Configure production webhook URLs
3. Test with live payment providers
4. Monitor webhook delivery and processing

## Troubleshooting

### Common Issues

**Webhook not receiving events**

- Check webhook URL accessibility
- Verify webhook endpoint configuration
- Check payment provider webhook logs

**Revenue not attributed**

- Verify metadata inclusion in payment sessions
- Check visitor/session ID cookie availability
- Review UTM parameter capture

**Credential validation failing**

- Verify API key format and permissions
- Check network connectivity to payment providers
- Review credential encryption/decryption

## Future Enhancements

- **Additional Providers**: PayPal, Square, Shopify Payments
- **Advanced Attribution**: Multi-touch attribution models
- **Revenue Forecasting**: Predictive analytics based on payment data
- **Subscription Tracking**: Enhanced recurring revenue tracking
- **Fraud Detection**: Integration with payment provider fraud tools
