# DataFast 多环境部署使用指南

## ✨ 新功能概述

DataFast 现在支持智能环境检测，能够自动在本地开发和生产环境之间切换，无需手动修改配置。

### 🎯 主要特性

- **🤖 自动环境检测**: 根据域名、URL 等自动识别环境（无需手动设置 `NODE_ENV`）
- **🔄 智能端点切换**: 本地开发时自动使用 `http://localhost:7181`，生产环境使用云端 API
- **🔧 灵活配置**: 支持环境变量覆盖，可手动指定环境类型
- **📊 多种脚本模式**: 支持代理模式、直连模式和动态模式

## 🚀 快速开始

### 1. 本地开发环境设置

```bash
# 启动本地 Tinybird
tb local start

# 获取本地配置信息
tb info

# 复制本地 token 到环境变量
echo 'TINYBIRD_TOKEN_LOCAL="你的本地token"' >> .env.local
```

### 2. 生产环境配置

```bash
# 设置生产环境变量
echo 'TINYBIRD_TOKEN="你的生产token"' >> .env.production
```

### 3. 验证配置

```bash
# 运行环境检测测试
npm run test:env

# 或手动运行
npx tsx scripts/test-environment-detection.ts
```

## 📡 追踪脚本使用

### 模式 1: 默认代理模式 (推荐)

```html
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID"></script>
```

**特点**:

- ✅ 通过服务器代理，最佳兼容性
- ✅ 自动环境检测
- ✅ 统一错误处理
- ❌ 增加服务器负载

### 模式 2: 直连模式 (高性能)

```html
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID?mode=direct"></script>
```

**特点**:

- ✅ 直接发送到 Tinybird，性能更好
- ✅ 减少服务器负载
- ✅ CDN 友好
- ❌ 需要配置 CORS

### 模式 3: 动态模式 (最灵活)

```html
<script src="https://yourdomain.com/api/script/YOUR_TRACKING_ID?mode=dynamic"></script>
```

**特点**:

- ✅ 客户端智能环境检测
- ✅ 开发/生产自动切换
- ✅ 最大灵活性
- ❌ 脚本体积稍大

## 🔧 环境检测逻辑

系统按以下优先级检测环境：

1. **明确指定**: `TINYBIRD_ENVIRONMENT=local|production`
2. **API URL**: 包含 `localhost`/`127.0.0.1` → `local`
3. **默认**: `production`

## 📊 API 端点自动选择

| 环境       | API URL                   | Token 变量             |
| ---------- | ------------------------- | ---------------------- |
| Local      | `http://localhost:7181`   | `TINYBIRD_TOKEN_LOCAL` |
| Production | `https://api.tinybird.co` | `TINYBIRD_TOKEN`       |

## 🎛️ 环境变量配置

### 必需变量

```env
# 本地开发
TINYBIRD_TOKEN_LOCAL="p.your_local_token"

# 生产环境
TINYBIRD_TOKEN="p.your_production_token"
```

### 可选变量

```env
# 自定义本地 API URL
TINYBIRD_API_URL_LOCAL="http://localhost:7181"

# 自定义生产 API URL
TINYBIRD_API_URL="https://api.your-region.tinybird.co"

# 强制指定环境
TINYBIRD_ENVIRONMENT="local"

# 调试模式
DEBUG_TINYBIRD="true"
```

## 🧪 测试和验证

### 环境检测测试

```bash
# 基础测试
npm run test:env

# 带本地 token 测试
TINYBIRD_TOKEN_LOCAL="your_token" npm run test:env

# 测试生产环境（无需设置 NODE_ENV）
TINYBIRD_TOKEN="your_token" npm run test:env
```

### 连接测试

```bash
# 测试本地 Tinybird
curl http://localhost:7181/v0/pipes

# 测试生产 Tinybird
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://api.tinybird.co/v0/pipes
```

## 🐛 故障排除

### 常见问题

1. **环境检测不正确**

   ```bash
   # 检查当前检测结果
   node -e "console.log(require('./lib/tinybird/client').detectEnvironment())"
   ```

2. **Token 未配置**

   ```bash
   # 检查环境变量
   echo $TINYBIRD_TOKEN_LOCAL
   echo $TINYBIRD_TOKEN
   ```

3. **连接失败**

   ```bash
   # 确认 Tinybird Local 运行状态
   tb local status

   # 重启本地服务
   tb local restart
   ```

### 调试模式

在 `.env.local` 中添加：

```env
DEBUG_TINYBIRD="true"
DEBUG_TRACKING="true"
LOG_LEVEL="debug"
```

查看浏览器控制台和服务器日志获取详细信息。

## 🚀 部署建议

### 开发环境

- 使用 `mode=dynamic` 获得最佳开发体验
- 启用调试模式查看详细日志
- 定期运行环境检测测试

### 生产环境

- 使用 `mode=direct` 获得最佳性能
- 配置适当的 CORS 策略
- 启用错误监控和日志记录
- 设置环境变量验证

## 📞 技术支持

遇到问题时：

1. 运行 `npm run test:env` 检查配置
2. 查看浏览器开发者工具网络面板
3. 检查服务器错误日志
4. 确认 Tinybird 服务状态

---

**更新日期**: 2025-08-07  
**版本**: 2.0.0  
**作者**: DataFast Team
