# DataFast → Tinybird 集成计划

## 概述

将 DataFast 从 PostgreSQL 迁移到 Tinybird，实现真正的实时分析能力。Tinybird 基于 ClickHouse，专为高性能实时分析而设计。

## 当前架构分析

### 现有数据模型

- **users**: 用户信息和订阅管理
- **websites**: 网站配置和追踪 ID
- **events**: 核心分析事件数据（页面浏览、自定义事件、支付等）
- **goals**: 转化目标定义

### 现有功能

- 4KB 追踪脚本，支持自动页面浏览、外部链接、表单提交追踪
- 实时 WebSocket 广播
- 复杂的 SQL 分析查询（访客、页面浏览、跳出率、收入等）
- 支付集成检测（Stripe、LemonSqueezy、Polar）

## Tinybird 集成策略

### 混合架构方案

保留 PostgreSQL 用于用户管理和配置，使用 Tinybird 专门处理事件数据和实时分析：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │    Tinybird     │    │   前端应用      │
│                 │    │                 │    │                 │
│ • users         │    │ • events        │    │ • Dashboard     │
│ • websites      │    │ • real-time     │◄───┤ • 实时图表      │
│ • goals         │    │   analytics     │    │ • API调用       │
│ • 配置数据      │    │ • 流式API       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   追踪脚本      │
                    │                 │
                    │ • 事件收集      │
                    │ • 直接发送到    │
                    │   Tinybird      │
                    └─────────────────┘
```

### Tinybird 数据源设计

#### 1. Events 数据源

```sql
-- events.datasource
DESCRIPTION >
    用户事件数据，支持页面浏览、自定义事件、支付等

SCHEMA >
    id String,
    website_id String,
    session_id String,
    visitor_id String,
    event_type Enum8('pageview' = 1, 'custom' = 2, 'payment' = 3, 'signup' = 4, 'conversion' = 5),
    event_name Nullable(String),
    url String,
    referrer Nullable(String),
    user_agent Nullable(String),
    ip_address Nullable(String),
    country Nullable(FixedString(2)),
    region Nullable(String),
    city Nullable(String),
    device Nullable(String),
    browser Nullable(String),
    os Nullable(String),
    utm_source Nullable(String),
    utm_medium Nullable(String),
    utm_campaign Nullable(String),
    utm_content Nullable(String),
    utm_term Nullable(String),
    custom_data Nullable(String),
    revenue Nullable(Decimal(10,2)),
    timestamp DateTime DEFAULT now()

ENGINE MergeTree()
ORDER BY (website_id, timestamp, visitor_id)
PARTITION BY toYYYYMM(timestamp)
```

#### 2. 实时聚合视图

```sql
-- real_time_metrics.pipe
DESCRIPTION >
    实时指标聚合：访客数、页面浏览量、收入等

NODE real_time_aggregation
SQL >
    SELECT
        website_id,
        toStartOfMinute(timestamp) as minute,
        uniqState(visitor_id) as unique_visitors,
        countState() as total_events,
        sumState(revenue) as total_revenue,
        countStateIf(event_type = 'pageview') as pageviews,
        countStateIf(event_type = 'conversion') as conversions
    FROM events
    GROUP BY website_id, minute

TYPE materialized
DATASOURCE real_time_metrics
```

### API 端点设计

#### 1. 概览指标端点

```sql
-- overview_metrics.pipe
DESCRIPTION >
    网站概览指标：访客、页面浏览、跳出率、平均会话时长、收入

NODE overview_query
SQL >
    SELECT
        uniq(visitor_id) as visitors,
        countIf(event_type = 'pageview') as pageviews,
        round(
            countIf(session_pageviews = 1) * 100.0 / uniq(session_id), 2
        ) as bounce_rate,
        round(avg(session_duration_minutes), 2) as avg_session_duration,
        sum(revenue) as total_revenue
    FROM (
        SELECT
            visitor_id,
            session_id,
            revenue,
            event_type,
            count() OVER (PARTITION BY session_id) as session_pageviews,
            (max(timestamp) OVER (PARTITION BY session_id) - min(timestamp) OVER (PARTITION BY session_id)) / 60 as session_duration_minutes
        FROM events
        WHERE website_id = {{String(website_id)}}
          AND timestamp >= {{DateTime(start_date)}}
          AND timestamp <= {{DateTime(end_date)}}
    )

TYPE endpoint
```

#### 2. 时间序列数据端点

```sql
-- timeseries_data.pipe
DESCRIPTION >
    时间序列数据：按小时/天/周/月聚合的访客、页面浏览、收入

NODE timeseries_query
SQL >
    %
    {% set interval_map = {
        'hour': 'toStartOfHour',
        'day': 'toStartOfDay',
        'week': 'toMonday',
        'month': 'toStartOfMonth'
    } %}

    SELECT
        {{interval_map[interval]}}(timestamp) as date,
        {% if metric == 'visitors' %}
            uniq(visitor_id) as value
        {% elif metric == 'pageviews' %}
            countIf(event_type = 'pageview') as value
        {% elif metric == 'revenue' %}
            sum(revenue) as value
        {% endif %}
    FROM events
    WHERE website_id = {{String(website_id)}}
      AND timestamp >= {{DateTime(start_date)}}
      AND timestamp <= {{DateTime(end_date)}}
    GROUP BY date
    ORDER BY date

TYPE endpoint
```

#### 3. 热门页面端点

```sql
-- top_pages.pipe
DESCRIPTION >
    热门页面分析：URL、页面浏览量、独立访客、跳出率

NODE top_pages_query
SQL >
    SELECT
        url,
        count() as pageviews,
        uniq(visitor_id) as visitors,
        round(
            countIf(visitor_pageviews = 1) * 100.0 / uniq(visitor_id), 2
        ) as bounce_rate
    FROM (
        SELECT
            url,
            visitor_id,
            count() OVER (PARTITION BY visitor_id) as visitor_pageviews
        FROM events
        WHERE website_id = {{String(website_id)}}
          AND event_type = 'pageview'
          AND timestamp >= {{DateTime(start_date)}}
          AND timestamp <= {{DateTime(end_date)}}
    )
    GROUP BY url
    ORDER BY pageviews DESC
    LIMIT {{Int32(limit, 10)}}

TYPE endpoint
```

#### 4. 实时访客端点

```sql
-- realtime_visitors.pipe
DESCRIPTION >
    实时访客：最近30分钟的访客活动

NODE realtime_query
SQL >
    SELECT
        visitor_id,
        url,
        country,
        city,
        device,
        browser,
        timestamp
    FROM events
    WHERE website_id = {{String(website_id)}}
      AND event_type = 'pageview'
      AND timestamp >= now() - interval 30 minute
    ORDER BY timestamp DESC
    LIMIT 50

TYPE endpoint
```

## 实施步骤

### 第一阶段：设置 Tinybird 环境

1. 创建 Tinybird 账户和工作空间
2. 配置数据源和初始管道
3. 设置环境变量和 API 密钥

### 第二阶段：数据迁移

1. 创建数据导出脚本
2. 将历史事件数据迁移到 Tinybird
3. 验证数据完整性

### 第三阶段：API 集成

1. 更新追踪脚本，直接发送到 Tinybird Events API
2. 创建新的分析 API 客户端
3. 更新仪表板组件以使用 Tinybird 端点

### 第四阶段：优化和扩展

1. 实施连续聚合以提高查询性能
2. 添加更高级的分析功能
3. 实现实时流处理

## 技术优势

### 性能提升

- **查询速度**：毫秒级响应时间，相比 PostgreSQL 提升 10-100 倍
- **并发处理**：支持数千个并发查询
- **实时处理**：事件到查询延迟小于 1 秒

### 扩展性

- **水平扩展**：支持 PB 级数据存储
- **高吞吐量**：每秒处理数万个事件
- **自动压缩**：存储效率提升 10 倍

### 开发者体验

- **SQL 兼容**：使用熟悉的 SQL 语法
- **API 优先**：自动生成 REST API
- **版本控制**：支持 Git 工作流

## 成本分析

### Tinybird 定价模型

- **免费层**：10GB 存储，1000 API 请求/天
- **开发计划**：基于数据处理量和 API 调用
- **企业计划**：专用基础设施和高级功能

### 预期节省

- **基础设施成本**：减少 PostgreSQL 实例负载
- **开发时间**：预构建的分析功能
- **运维成本**：托管服务，无需维护 ClickHouse 集群

## 风险和缓解措施

### 潜在风险

1. **学习曲线**：Tinybird 特有的功能和限制
2. **数据迁移**：确保零停机时间
3. **成本控制**：监控数据处理量和 API 使用

### 缓解策略

1. **渐进式迁移**：分阶段实施，保持向后兼容
2. **双写策略**：初期同时写入两个系统
3. **监控和告警**：实时监控成本和性能指标

## 下一步行动

1. [ ] 创建 Tinybird 账户和工作空间
2. [ ] 实施基本数据源和管道
3. [ ] 更新追踪脚本以支持 Tinybird
4. [ ] 创建 API 客户端库
5. [ ] 更新仪表板组件
6. [ ] 性能测试和优化
7. [ ] 文档更新和团队培训
