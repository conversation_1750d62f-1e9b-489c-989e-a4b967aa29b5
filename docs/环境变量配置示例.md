# DataFast - Tinybird 集成环境变量配置

复制以下内容到您的 `.env.local` 文件中：

```env
# =============================================================================
# 核心应用配置
# =============================================================================
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_TRACKING_SCRIPT_URL="http://localhost:3000/script-tinybird.js"

# =============================================================================
# 数据库配置 - 用户和配置数据 (PostgreSQL)
# =============================================================================
DATABASE_URL="postgresql://username:password@localhost:5432/datafast_dev"

# =============================================================================
# Tinybird 实时分析配置
# =============================================================================
# Tinybird API 基础 URL (通常不需要更改)
TINYBIRD_API_URL="https://api.tinybird.co"

# Tinybird 工作空间的 API Token (必需)
# 从 Tinybird 仪表板获取: https://www.tinybird.co/tokens
TINYBIRD_TOKEN="your_tinybird_api_token_here"

# Tinybird 查询超时时间 (毫秒)
TINYBIRD_TIMEOUT="10000"

# =============================================================================
# 认证配置 (Auth.js / NextAuth.js)
# =============================================================================
# 随机密钥，用于 JWT 签名 (生成命令: openssl rand -base64 32)
AUTH_SECRET="your-random-secret-here"

# Resend 邮件服务 API 密钥
AUTH_RESEND_KEY="re_your-resend-api-key"

# Google OAuth 配置 (可选)
AUTH_GOOGLE_ID="your-google-oauth-client-id"
AUTH_GOOGLE_SECRET="your-google-oauth-client-secret"

# =============================================================================
# 可选配置
# =============================================================================
# 日志级别 (development: debug, production: error)
LOG_LEVEL="debug"

# Redis 配置 (用于缓存，可选)
REDIS_URL="redis://localhost:6379"

# Stripe 配置 (用于支付检测，可选)
STRIPE_PUBLIC_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."

# =============================================================================
# 开发环境特定配置
# =============================================================================
# 开发模式下的详细日志
DEBUG_TINYBIRD="true"
DEBUG_TRACKING="true"

# =============================================================================
# 生产环境特定配置
# =============================================================================
# 无需手动设置 NODE_ENV，Next.js 将自动设置
# NODE_ENV="development"

# 安全 Cookie 设置 (生产环境建议启用)
SECURE_COOKIES="false"

# CORS 允许的域名 (生产环境请设置具体域名)
ALLOWED_ORIGINS="*"
```

## 📝 配置说明

### 必需变量

- `TINYBIRD_TOKEN`: 从 Tinybird 控制台获取的 API 令牌
- `DATABASE_URL`: PostgreSQL 数据库连接字符串
- `AUTH_SECRET`: NextAuth.js 使用的随机密钥

### 可选变量

- `AUTH_RESEND_KEY`: 邮件验证服务密钥
- `AUTH_GOOGLE_ID/SECRET`: Google OAuth 登录配置
- `DEBUG_*`: 开发环境调试开关

### 生产环境注意事项

1. 使用强随机密钥作为 `AUTH_SECRET`
2. 启用 `SECURE_COOKIES="true"`
3. 设置具体的 `ALLOWED_ORIGINS`
4. 关闭调试模式 `DEBUG_*="false"`
