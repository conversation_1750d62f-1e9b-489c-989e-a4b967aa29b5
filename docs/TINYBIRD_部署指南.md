# DataFast + Tinybird 部署指南

本指南将帮助您将 DataFast 项目与 Tinybird 实时分析数据库集成并部署到生产环境。

## 📋 前置条件

### 系统要求

- Node.js 18+
- PostgreSQL 12+ (用于用户数据)
- Tinybird 账户 (用于分析数据)
- Vercel 账户 (推荐部署平台)

### 必需的服务账户

1. **Tinybird 账户**: https://www.tinybird.co
2. **Resend 账户**: https://resend.com (邮件服务)
3. **Google Cloud Console**: https://console.cloud.google.com (OAuth，可选)

## 🚀 第一步：Tinybird 配置

### 1.1 创建 Tinybird 工作空间

```bash
# 安装 Tinybird CLI
curl https://tinybird.co | sh

# 登录并创建工作空间
tb login
```

### 1.2 上传数据源和端点

```bash
# 克隆项目后，进入 tinybird 目录
cd tinybird

# 部署数据源
tb datasource create datasources/events.datasource

# 部署所有端点
tb endpoint create endpoints/overview_metrics.pipe
tb endpoint create endpoints/timeseries_data.pipe
tb endpoint create endpoints/top_pages.pipe
tb endpoint create endpoints/traffic_sources.pipe
tb endpoint create endpoints/device_breakdown.pipe
tb endpoint create endpoints/realtime_visitors.pipe
```

### 1.3 获取 API Token

1. 访问 Tinybird 仪表板
2. 进入 "Tokens" 页面
3. 创建新的 Token，确保有以下权限：
   - `events:append` (写入事件数据)
   - `pipes:read` (读取端点数据)
4. 复制 Token 用于环境变量配置

## 🔧 第二步：环境配置

### 2.1 配置环境变量

复制环境变量模板：

```bash
cp .env.tinybird.example .env.local
```

填写关键配置：

```env
# Tinybird 配置
TINYBIRD_API_URL="https://api.tinybird.co"
TINYBIRD_TOKEN="your_actual_tinybird_token"

# PostgreSQL 用户数据库
DATABASE_URL="********************************/datafast"

# 认证密钥
AUTH_SECRET="your_secure_random_secret"
AUTH_RESEND_KEY="re_your_resend_api_key"
```

### 2.2 数据库初始化

```bash
# 安装依赖
npm install

# 生成并执行 PostgreSQL 迁移
npm run db:generate
npm run db:migrate

# 验证数据库连接
npm run db:studio
```

## 📦 第三步：本地开发测试

### 3.1 启动开发服务器

```bash
npm run dev
```

### 3.2 测试 Tinybird 集成

1. 访问 http://localhost:3000
2. 创建测试网站
3. 复制追踪代码并添加到测试页面
4. 验证事件是否正确发送到 Tinybird

测试追踪代码示例：

```html
<!DOCTYPE html>
<html>
  <head>
    <title>测试页面</title>
    <script
      src="http://localhost:3000/script-tinybird.js"
      data-website-id="your-website-id"
      data-tinybird-token="your-tinybird-token"
      data-domain="localhost"
      data-debug="true"
      defer
    ></script>
  </head>
  <body>
    <h1>测试页面</h1>
    <button onclick="datafast.track('button_click', {button: 'test'})">
      测试自定义事件
    </button>
  </body>
</html>
```

## 🌐 第四步：生产环境部署

### 4.1 Vercel 部署 (推荐)

1. **连接 GitHub 仓库**:

   - 访问 https://vercel.com
   - 导入您的 GitHub 仓库

2. **配置环境变量**:
   在 Vercel 仪表板中添加所有生产环境变量：

   ```
   TINYBIRD_API_URL=https://api.tinybird.co
   TINYBIRD_TOKEN=your_production_tinybird_token
   DATABASE_URL=your_production_postgresql_url
   AUTH_SECRET=your_production_secret
   AUTH_RESEND_KEY=your_resend_key
   NEXT_PUBLIC_APP_URL=https://yourdomain.com
   ```

3. **部署应用**:
   ```bash
   # 推送到 main 分支触发自动部署
   git add .
   git commit -m "Deploy with Tinybird integration"
   git push origin main
   ```

### 4.2 手动部署选项

如果不使用 Vercel，可以手动部署：

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

使用 PM2 管理进程：

```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start npm --name "datafast" -- start

# 设置开机自启
pm2 startup
pm2 save
```

## 🔒 第五步：安全配置

### 5.1 生产环境安全设置

1. **HTTPS 配置**: 确保网站使用 HTTPS
2. **环境变量**: 不要在代码中硬编码敏感信息
3. **CORS 设置**: 限制允许的域名
4. **API 速率限制**: 配置合理的速率限制

### 5.2 Tinybird 安全

1. **Token 权限**: 使用最小权限原则
2. **IP 白名单**: 如果可能，限制 API 访问 IP
3. **监控**: 设置异常访问告警

## 📊 第六步：监控和维护

### 6.1 性能监控

监控关键指标：

- Tinybird API 响应时间
- 事件丢失率
- PostgreSQL 连接状态
- 应用错误率

### 6.2 日志配置

在生产环境中配置适当的日志级别：

```env
LOG_LEVEL="error"
DEBUG_TINYBIRD="false"
DEBUG_TRACKING="false"
```

### 6.3 备份策略

1. **PostgreSQL 数据备份**: 配置定期数据库备份
2. **Tinybird 数据**: Tinybird 自动处理数据备份
3. **应用配置**: 版本控制所有配置文件

## 🚨 故障排除

### 常见问题和解决方案

#### 1. Tinybird 连接失败

```bash
# 检查网络连接
curl -H "Authorization: Bearer YOUR_TOKEN" https://api.tinybird.co/v0/

# 验证 Token 权限
tb token info YOUR_TOKEN
```

#### 2. 事件未正确追踪

- 检查浏览器控制台错误
- 验证追踪脚本参数
- 确认 Tinybird Token 有写入权限

#### 3. 查询性能问题

- 检查 Tinybird 端点查询优化
- 验证索引设置
- 考虑添加数据过滤条件

#### 4. PostgreSQL 连接问题

```bash
# 测试数据库连接
npm run db:studio

# 检查连接字符串格式
echo $DATABASE_URL
```

## 📈 性能优化建议

### 6.1 Tinybird 优化

- 使用合适的分区键 (`toYYYYMM(timestamp)`)
- 优化查询的 WHERE 条件顺序
- 考虑使用物化视图加速常用查询

### 6.2 应用优化

- 启用追踪脚本的批量发送
- 配置合适的缓存策略
- 使用 CDN 加速静态资源

### 6.3 成本控制

- 监控 Tinybird 的数据处理量
- 设置合理的数据保留期限
- 优化查询减少不必要的数据扫描

## 🔄 维护和更新

### 定期维护任务

1. **更新依赖包**: 定期更新 npm 包
2. **监控日志**: 检查错误日志和性能指标
3. **备份验证**: 定期测试备份恢复
4. **安全更新**: 及时应用安全补丁

### 扩展计划

- 根据数据增长调整 Tinybird 配置
- 考虑添加更多分析维度
- 优化实时功能性能

---

通过遵循本指南，您应该能够成功部署带有 Tinybird 集成的 DataFast 应用。如有任何问题，请查看项目文档或提交 Issue。
