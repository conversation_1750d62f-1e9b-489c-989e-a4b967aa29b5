# Plausible 集成实施 TODO（InstaSight）

> 基于 `docs/plausible-integration-plan.md` 拆解的可执行任务清单。含里程碑、子任务、依赖、验收标准与涉及文件。

## 里程碑与优先级

- M1（高）Stats API 对齐 + Realtime 扩展
- M2（中高）Goals/Funnels + 收入归因
- M3（中）共享/公开链接 + 导出/报表
- M4（中）GSC 集成 + 高级过滤

---

## M1 - Stats API 对齐 + Realtime 扩展

### 1. Stats API 对齐（aggregate / breakdown / timeseries）

- [ ] 路由扩展：`app/api/analytics/[websiteId]/route.ts`
  - [ ] 解析 query：`metric/period/interval/filters/property/limit/page`（兼容现有 `type=*`）
  - [ ] 校验：period→(from,to)、interval 合法性、limit/page 范围
  - [ ] 输出对齐：
    - [ ] timeseries：`{ plot, labels, present_index, interval }`
    - [ ] aggregate/breakdown：`{ results, meta? }`
- [ ] 转换层：`lib/tinybird/analytics.ts`
  - [ ] `buildFilters(params)`：period/interval/filters/property → Tinybird params
  - [ ] `mapToPlausibleTimeseries(data)`：封装 `plot/labels/present_index`
  - [ ] `mapToPlausibleAggregate/Breakdown(data)`
- [ ] Tinybird pipes：
  - [ ] 扩展 `tinybird/endpoints/timeseries_data.pipe`（支持 `metric=visitors|pageviews|revenue`，`interval=minute/hour/date/week/month`）
  - [ ] 新增 `tinybird/endpoints/aggregate.pipe`（总览指标支持 filters/property）
  - [ ] 新增 `tinybird/endpoints/breakdown.pipe`（按 property 聚合 + limit/page）
- [ ] 缓存：`lib/cache/redis.ts`
  - [ ] 新增 Stats API 缓存键：`stats:{websiteId}:{hash(params)}`，TTL=30～120s
- [ ] 文档：`README.md` 与 `docs/README-INSTASIGHT.md` 增加 Stats API 使用示例
- [ ] 验收标准：
  - [ ] 对同一参数组合，多次请求命中缓存；
  - [ ] 与 Tinybird CLI 校验的结果在误差容许内一致；
  - [ ] 单元测试覆盖 period/interval/filters/property 组合（至少 8 例）。

### 2. Realtime 扩展（轮询 JSON）

- [ ] Tinybird 新增：
  - [ ] `tinybird/endpoints/realtime_top_pages.pipe`（近 30～60 分钟窗口，`url,count,last_seen`）
  - [ ] `tinybird/endpoints/realtime_entries.pipe`（入口来源/页面）
- [ ] API：`app/api/stream/route.ts`
  - [ ] 返回体新增：`topPages`, `recentEntries`
  - [ ] 环境变量：`REALTIME_REFRESH_MS`（默认 50000ms），继续在响应体回传 `refreshIntervalMs`
- [ ] Hook：`lib/hooks/useRealtimeData.ts`
  - [ ] 继续轮询模式，消费新增字段；组件可选显示
- [ ] UI：
  - [ ] `components/dashboard/real-time-visitors.tsx` / `real-time-activity.tsx` 支持 top/recent 区块（可折叠）
- [ ] 验收标准：
  - [ ] 连接仅使用轮询，无 SSE；
  - [ ] UI 能在 1 分钟内反映最新 topPages/entries 变化；
  - [ ] MPA/SPA 场景下路由切换仍可更新。

---

## M2 - Goals/Funnels + 收入归因

### 3. 目标/漏斗（Goals/Funnels）

- [ ] 数据模型（Neon）：`lib/db/schema.ts`
  - [ ] `goals` 表扩展：`type`、`steps`（JSON）或规则表达式、`active`
  - [ ] migration 脚本：`drizzle/migrations/*`
- [ ] Tinybird pipes：
  - [ ] `tinybird/endpoints/funnel_steps.pipe`（按 session_id + timestamp 排序，计算到达率/转化率/平均时长）
  - [ ] `tinybird/endpoints/goals_conversion.pipe`（按 goal/eventName 聚合）
- [ ] API：`app/api/analytics/[websiteId]/route.ts`
  - [ ] `type=funnel|goals` 支持，参数校验、返回统一
- [ ] 前端 UI：
  - [ ] 新增 Goals/Funnels 页卡 + 基本图表（漏斗图/表格）
- [ ] 验收标准：
  - [ ] 可创建/编辑/停用目标（仅保存到 Neon）并在仪表盘查询；
  - [ ] 漏斗转化率、平均用时数值可信（与样例数据对拍）。

### 4. 收入归因（Revenue）

- [ ] 追踪脚本：`public/script.js`
  - [ ] 允许 `trackRevenue(amount,currency)` 与 `track('conversion',{value,...})`（保持隐私最小化）
- [ ] Tinybird pipes：
  - [ ] 扩展 `channel_revenue.pipe`、`campaign_revenue.pipe`（已有基础）
  - [ ] 新增 `revenue_overview.pipe`（总收入、订单数、AOV）
- [ ] API：`app/api/revenue/[websiteId]/route.ts`
  - [ ] 增加 filters 与 breakdown 参数
- [ ] UI：Dashboard 卡片与明细表
- [ ] 验收标准：
  - [ ] 回归：不报错地处理无 revenue 站点；
  - [ ] 与 Tinybird CLI 查询一致。

---

## M3 - 共享/公开链接 + 导出/报表

### 5. 共享/公开链接（Public Dashboard）

- [ ] 数据模型（Neon）：`websites` 表
  - [ ] 新增 `sharedSlug`、`isPublic` 字段与索引
- [ ] API：
  - [ ] 新增 `app/api/public/[slug]/route.ts`：slug → trackingId → Tinybird 查询（隐藏敏感维度）
- [ ] 页面：
  - [ ] `app/p/[slug]/page.tsx` 只读仪表盘视图
- [ ] 安全：禁止展示 IP/UA 细粒度与任何 PII
- [ ] 验收标准：
  - [ ] 关闭登录情况下可访问公开链接；
  - [ ] 禁止写接口；链接可撤销（重置 slug）。

### 6. 导出与报表（Export & Reports）

- [ ] 导出：
  - [ ] `GET /api/analytics/[websiteId]?type=export&format=csv`（串 Tinybird 导出）
  - [ ] 大结果流式输出；响应 `Content-Disposition` 正确
- [ ] 报表：
  - [ ] Vercel Cron/外部任务触发 `lib/actions` 聚合周/月报
  - [ ] 通知：Resend（Email）/Slack webhook（可选）
  - [ ] 报表模板（Top Pages、Sources、Visitors、Revenue 摘要）
- [ ] 验收标准：
  - [ ] CSV 列头与 UI 对齐；
  - [ ] 报表可配置站点/周期，能成功发送。

---

## M4 - GSC 集成 + 高级过滤

### 7. GSC 集成（关键词）

- [ ] 设置页：关联 GSC，存储 token（Neon 加密字段 or KMS）
- [ ] 查询：GSC API 拉取关键词/展示在 Dashboard 卡片
- [ ] 验收标准：
  - [ ] 无 GSC 授权时 UI 优雅降级；
  - [ ] 存在授权时能展示近 7/28/90 天关键词趋势。

### 8. 高级过滤与 Bot 阻断

- [ ] 写入：在 `app/api/events/route.ts` 加强 UA/Automation 标记（`is_bot`）
- [ ] 查询：pipes 默认 `WHERE is_bot = 0`，提供 `include_bots` 调试参数
- [ ] 数据中心 IP：可选导入公共 IP 列表（后续）
- [ ] 验收标准：
  - [ ] 系统性降低机器人流量干扰；
  - [ ] 对真实用户零误伤（抽样核验）。

---

## 环境变量与配置

- [ ] `REALTIME_REFRESH_MS`（默认 50000）- 轮询间隔；
- [ ] `SLACK_WEBHOOK_URL`（可选）- 报表通知；
- [ ] `REPORT_CRON_KEY`（可选）- 报表任务鉴权；
- [ ] GSC 相关：`GSC_CLIENT_ID/SECRET`、`GSC_REDIRECT_URL`（后续）。

---

## 测试与质量

- [ ] 单元：转换层、API 参数校验、缓存命中/失效；
- [ ] 集成：Tinybird pipes 与 API 对拍（使用固定样例数据）；
- [ ] 端到端（Playwright）：Dashboard 主要视图渲染与导出；
- [ ] Lint/格式（Biome）全通过；`npm run build` 通过。

---

## 跟踪与文档

- [ ] 更新 `README.md` 与 `docs/README-INSTASIGHT.md`（新增 Stats API/Realtime/Goals/Export 使用）；
- [ ] `docs/architecture-flow.md`：补充 Realtime 轮询流与公开视图；
- [ ] 本 TODO 定期勾选更新至 `PROGRESS_UPDATE.md`。
