# Tinybird 优化指南

## 概述

本指南提供了优化 DataFast 项目中 Tinybird 集成性能的最佳实践和建议。

## 🚀 查询性能优化

### 1. 索引策略

```sql
-- 优化事件数据源的排序键
ENGINE_SORTING_KEY "timestamp, websiteId, eventType"
ENGINE_PARTITION_KEY "toYYYYMM(timestamp)"
```

**最佳实践：**

- 将最常用的过滤字段放在排序键的前面
- 使用时间字段进行分区以提高查询效率
- 避免高基数字段作为主要排序键

### 2. 查询优化

#### 时间范围查询

```sql
-- 好的做法
WHERE timestamp >= {{DateTime(from, 'America/Los_Angeles')}}
  AND timestamp < {{DateTime(to, 'America/Los_Angeles')}}
  AND websiteId = {{String(websiteId)}}

-- 避免的做法
WHERE toDate(timestamp) BETWEEN '2024-01-01' AND '2024-01-31'
```

#### 聚合查询优化

```sql
-- 使用适当的聚合函数
SELECT
  uniq(visitorId) as unique_visitors,
  count() as total_events,
  sum(revenue) as total_revenue
FROM events
WHERE websiteId = {{String(websiteId)}}
  AND timestamp >= {{DateTime(from)}}
```

### 3. 物化视图

对于复杂的聚合查询，考虑创建物化视图：

```sql
-- 每日汇总物化视图
CREATE MATERIALIZED VIEW daily_stats_mv
ENGINE = SummingMergeTree()
ORDER BY (websiteId, date)
AS SELECT
  websiteId,
  toDate(timestamp) as date,
  uniq(visitorId) as visitors,
  count() as pageviews,
  sum(revenue) as revenue
FROM events
GROUP BY websiteId, date
```

## 📊 数据建模最佳实践

### 1. 数据类型选择

```sql
-- 推荐的数据类型
id String,                    -- 使用 String 而不是 UUID
websiteId String,             -- 网站标识符
sessionId String,             -- 会话标识符
visitorId String,             -- 访客标识符
timestamp DateTime64(3),      -- 毫秒精度时间戳
revenue Float64,              -- 收入数据
country String,               -- 国家代码
city String,                  -- 城市名称
customData String             -- JSON 字符串存储自定义数据
```

### 2. 分区策略

```sql
-- 按月分区，适合大多数分析查询
ENGINE_PARTITION_KEY "toYYYYMM(timestamp)"

-- 对于超大数据量，考虑按天分区
ENGINE_PARTITION_KEY "toYYYYMMDD(timestamp)"
```

## 🔄 实时数据优化

### 1. Events API 优化

```typescript
// 批量发送事件以提高吞吐量
const batchEvents = async (events: Event[]) => {
  const ndjson = events.map((e) => JSON.stringify(e)).join("\n");

  return fetch(`${TINYBIRD_API_URL}/v0/events?name=events`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${TINYBIRD_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: ndjson,
  });
};
```

### 2. 查询缓存

```typescript
// 实现查询结果缓存
class TinybirdCache {
  private cache = new Map();
  private ttl = 30000; // 30秒缓存

  async get<T>(key: string, fetchFn: () => Promise<T>): Promise<T> {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.data;
    }

    const data = await fetchFn();
    this.cache.set(key, { data, timestamp: Date.now() });
    return data;
  }
}
```

## 📈 监控和调试

### 1. 查询性能监控

```typescript
// 添加查询性能监控
const monitorQuery = async (queryName: string, queryFn: () => Promise<any>) => {
  const start = Date.now();
  try {
    const result = await queryFn();
    const duration = Date.now() - start;

    console.log(`Query ${queryName} completed in ${duration}ms`);

    // 发送到监控系统
    if (duration > 1000) {
      console.warn(`Slow query detected: ${queryName} took ${duration}ms`);
    }

    return result;
  } catch (error) {
    console.error(`Query ${queryName} failed:`, error);
    throw error;
  }
};
```

### 2. 错误处理

```typescript
// 实现重试机制
const retryQuery = async <T>(
  queryFn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await queryFn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise((resolve) =>
        setTimeout(resolve, delay * Math.pow(2, i))
      );
    }
  }
  throw new Error("Max retries exceeded");
};
```

## 🛠️ 开发环境配置

### 1. 本地开发

```bash
# 启动本地 Tinybird 实例
tb local start

# 同步数据源和管道
tb push --wait

# 模拟数据注入
tb mock --datasource events --size 1000
```

### 2. 测试数据

```typescript
// 生成测试数据
const generateTestEvent = (): Event => ({
  id: nanoid(),
  websiteId: "test-website",
  sessionId: nanoid(),
  visitorId: nanoid(),
  eventType: "pageview",
  url: "/test-page",
  timestamp: new Date(),
  country: "US",
  city: "San Francisco",
  device: "Desktop",
  browser: "Chrome",
  revenue: Math.random() * 100,
});
```

## 📋 性能基准

### 目标性能指标

| 查询类型 | 目标延迟 | 数据量      | 并发数 |
| -------- | -------- | ----------- | ------ |
| 概览查询 | < 200ms  | 1M+ 事件    | 100+   |
| 时序数据 | < 300ms  | 30 天数据   | 50+    |
| 实时查询 | < 100ms  | 最近 1 小时 | 200+   |
| 聚合查询 | < 500ms  | 任意范围    | 20+    |

### 性能测试

```bash
# 运行性能测试
npm run test:performance

# 压力测试
npm run test:load
```

## 🔧 故障排除

### 常见问题

1. **查询超时**

   - 检查排序键是否优化
   - 减少查询的时间范围
   - 使用物化视图预聚合数据

2. **高内存使用**

   - 优化 GROUP BY 查询
   - 限制返回的行数
   - 使用采样查询

3. **数据延迟**
   - 检查 Events API 配置
   - 确认网络连接稳定
   - 监控数据管道状态

### 调试工具

```bash
# 查看管道状态
tb pipe ls

# 检查数据源
tb datasource ls

# 查看查询计划
tb sql "EXPLAIN SELECT ..." --format JSONEachRow
```

## 📚 进一步优化

### 1. 高级特性

- 使用 Materialized Views 预计算复杂指标
- 实现数据分层存储
- 配置自动数据保留策略

### 2. 扩展性考虑

- 设计分片策略
- 实现读写分离
- 配置自动扩缩容

### 3. 成本优化

- 监控查询成本
- 优化数据保留期
- 使用压缩算法

---

## 📞 支持

如需更多帮助，请参考：

- [Tinybird 官方文档](https://docs.tinybird.co/)
- [DataFast 项目 README](../README.md)
- [性能测试脚本](../scripts/test-tinybird-performance.ts)
