---
alwaysApply: true
---

# 项目结构速览

- 应用入口与布局：[`app/layout.tsx`](mdc:app/layout.tsx)、全局样式：[`app/globals.css`](mdc:app/globals.css)
- 首页落地页：[`app/page.tsx`](mdc:app/page.tsx)
- 认证：
  - 配置与导出：[`lib/auth/config.ts`](mdc:lib/auth/config.ts)、[`lib/auth/index.ts`](mdc:lib/auth/index.ts)
  - Auth 路由：[`app/api/auth/[...nextauth]/route.ts`](mdc:app/api/auth/[...nextauth]/route.ts)
  - 中间件：[`middleware.ts`](mdc:middleware.ts)（务必跳过 `/api/auth/*`）
- 仪表盘数据动作（服务端 actions）：[`lib/actions/dashboard.ts`](mdc:lib/actions/dashboard.ts)
- Tinybird 客户端与分析：
  - 客户端：[`lib/tinybird/client.ts`](mdc:lib/tinybird/client.ts)
  - 事件工具：[`lib/tinybird/events.ts`](mdc:lib/tinybird/events.ts)
  - 分析封装：[`lib/tinybird/analytics.ts`](mdc:lib/tinybird/analytics.ts)
  - Tinybird pipes：[`tinybird/endpoints/*`](mdc:tinybird/endpoints)
- API 路由：
  - 分析：[`app/api/analytics/[websiteId]/route.ts`](mdc:app/api/analytics/[websiteId]/route.ts)
  - 实时：[`app/api/realtime/[websiteId]/route.ts`](mdc:app/api/realtime/[websiteId]/route.ts)、[`app/api/stream/route.ts`](mdc:app/api/stream/route.ts)
  - 收入归因：[`app/api/revenue/[websiteId]/route.ts`](mdc:app/api/revenue/[websiteId]/route.ts)
  - 事件上报：[`app/api/events/route.ts`](mdc:app/api/events/route.ts)
  - 动态脚本分发：[`app/api/script/[trackingId]/route.ts`](mdc:app/api/script/[trackingId]/route.ts)
- 数据库（Neon）与 Drizzle 模式：[`lib/db/schema.ts`](mdc:lib/db/schema.ts)、连接：[`lib/db/index.ts`](mdc:lib/db/index.ts)
- 安全：
  - 中间件与校验：[`lib/security/middleware.ts`](mdc:lib/security/middleware.ts)、[`lib/security/validation.ts`](mdc:lib/security/validation.ts)
- 构建配置：[`next.config.js`](mdc:next.config.js)、[`postcss.config.mjs`](mdc:postcss.config.mjs)、[`package.json`](mdc:package.json)

关键约定：

- 分析与高频事件统一走 Tinybird，Neon 仅保存配置/元数据。
- 事件写入策略由环境变量 `EVENT_WRITE_MODE=tinybird|dual|neon` 控制，默认 `tinybird`。
