---
globs: app/**/*.tsx,components/**/*.tsx
---

# TailwindCSS 使用规范（v4）

- 样式入口：在 [`app/globals.css`](mdc:app/globals.css) 使用 `@import "tailwindcss"`（v4 写法）。不要再使用 `@tailwind base/components/utilities`。
- PostCSS：[`postcss.config.mjs`](mdc:postcss.config.mjs) 已启用 `@tailwindcss/postcss` 插件，无需单独的 `tailwind.config` 文件即可工作。
- 容器宽度：页面容器统一使用 `max-w-7xl mx-auto px-4`。
- 卡片：使用 `components/ui/card.tsx` 提供的组件，默认包含 `rounded-xl border shadow-sm`。需要更强视觉时添加 `hover:shadow-md transition-all`。
- 文本与间距：
  - 标题使用 `tracking-tight`、正文 `text-gray-600`，大段文案限制 `max-w-2xl`。
  - 栅格列间距：移动端 `gap-6`，≥md `gap-8`。
- 交互：主要按钮添加 `shadow-sm hover:shadow-md`，保持视觉反馈一致。
