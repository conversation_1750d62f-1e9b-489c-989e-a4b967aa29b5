---
globs: *.ts,*.tsx
---

# 代码风格与类型安全（Biome 对齐）

- TypeScript
  - 避免隐式 `any`。为局部临时结果可用 `const value: unknown` 后再收窄。
  - Node 内置模块使用 `node:` 协议导入，例如：`import crypto from "node:crypto"`、`import path from "node:path"`。
  - 解析数字使用 `Number.parseInt/Number.parseFloat`，禁止全局 `parseInt/parseFloat`。
  - `switch` 分支里声明的 `const/let` 需要加块级作用域 `{}`，避免跨分支可见。
  - 遍历对象或数组优先 `for...of`/`for...in`，避免回调 `forEach` 造成不可控副作用与 lint 告警。
  - 处理长行字符串优先模板字面量（反引号）。

- 控制流与错误处理
  - 早返回，减少嵌套。
  - 服务器端 `fetch`/外部 API 调用必须检查 `response.ok` 并抛出带状态码的错误信息。

- 命名与可读性
  - 函数名使用动词短语，变量名语义化：`websiteId`、`trackingId`、`dateRange` 等。
  - 保持统一的 `max-w-7xl mx-auto px-4` 页面容器与语义化 UI 组件（`Card`,`Button`）。

- 构建与检查
  - 修改关键逻辑后运行 `npm run build` 保证类型与产物正确。
