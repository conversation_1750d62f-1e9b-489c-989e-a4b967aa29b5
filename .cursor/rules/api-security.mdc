---
globs: app/api/**/*.ts
---

# API 安全与鉴权规则

- 所有读取业务数据的路由必须：
  1. `auth()` 校验登录；
  2. 校验网站归属（通过 `lib/db` 查询 `websites.userId === session.user.id`）。
- 事件上报路由：使用 `withSecurity(SECURITY_CONFIGS.tracking)`，并严格校验输入（见 [`lib/security/validation.ts`](mdc:lib/security/validation.ts)）。
- 避免拦截 Auth 内部接口：在 [`middleware.ts`](mdc:middleware.ts) 跳过 `/api/auth/*`。
- 事件写入策略：默认仅写 Tinybird；如需双写，设置 `EVENT_WRITE_MODE=dual`，或仅写 Neon 设为 `neon`。
