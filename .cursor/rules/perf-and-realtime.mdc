---
description: Realtime & performance guidelines
globs: app/api/**/*.ts,lib/tinybird/**/*.ts
---

# 性能与实时指南

- 实时：
  - `app/api/realtime/[websiteId]/route.ts` 与 `app/api/stream/route.ts` 只读 Tinybird，返回快速、无阻塞。
  - SSE (`/api/stream`) 需传 `websiteId`，并已做登录与站点归属校验。

- 事件热路径：
  - `/api/events` 默认仅 Tinybird；如需双写，请使用 `EVENT_WRITE_MODE=dual`，建议改为异步队列以免阻塞。

- WebSocket 广播：
  - 广播错误仅记录日志，不影响主流程（保证写入优先）。

- 校验与安全：
  - `withSecurity(SECURITY_CONFIGS.tracking)` 用于上报端，`validate` + `sanitize` 先于任何写入。
