---
alwaysApply: true
---

# 环境变量与运行模式

- 环境检测与 Tinybird：见 [`lib/tinybird/client.ts`](mdc:lib/tinybird/client.ts)
  - `detectEnvironment()` 推断 `local|production`
  - `TINYBIRD_API_URL_LOCAL`/`TINYBIRD_TOKEN_LOCAL`、`TINYBIRD_API_URL`/`TINYBIRD_TOKEN`
  - `TINYBIRD_TIMEOUT` 毫秒（字符串形式，使用 `Number.parseInt` 解析）

- 事件写入模式（热路径性能）
  - `EVENT_WRITE_MODE=tinybird|dual|neon`，默认 `tinybird`；实现于 [`app/api/events/route.ts`](mdc:app/api/events/route.ts)。
  - `tinybird`：只写 Tinybird；`dual`：同时写 Neon 与 Tinybird；`neon`：只写 Neon。

- 脚本分发
  - 通过 [`app/api/script/[trackingId]/route.ts`](mdc:app/api/script/[trackingId]/route.ts) 注入环境端点与 Token，支持 `mode=default|direct|dynamic|4kb`。

- 中间件
  - [`middleware.ts`](mdc:middleware.ts) 必须跳过 `/api/auth/*`，否则会返回 HTML 导致客户端解析 JSON 失败。
