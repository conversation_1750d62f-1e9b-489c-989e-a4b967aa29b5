#!/usr/bin/env tsx

/**
 * Test script to simulate real-time visitors for the 3D globe visualization
 * This script will create fake presence data to demonstrate the globe functionality
 */

import { ConvexHttpClient } from "convex/browser";

const CONVEX_URL = process.env.NEXT_PUBLIC_CONVEX_URL;
const WEBSITE_TRACKING_ID = "c534ccfc-25a9-4a5a-b86e-e9831924af96"; // Test website ID

if (!CONVEX_URL) {
  console.error("NEXT_PUBLIC_CONVEX_URL environment variable is required");
  process.exit(1);
}

const client = new ConvexHttpClient(CONVEX_URL);

// Test visitor data from different countries
const testVisitors = [
  {
    userId: "test-visitor-1",
    sessionId: "session-1",
    country: "United States",
    city: "San Francisco",
    device: "Desktop",
    browser: "Chrome",
    url: "https://example.com/",
    referrer: "https://google.com",
  },
  {
    userId: "test-visitor-2", 
    sessionId: "session-2",
    country: "United Kingdom",
    city: "London",
    device: "Mobile",
    browser: "Safari",
    url: "https://example.com/pricing",
    referrer: "https://twitter.com",
  },
  {
    userId: "test-visitor-3",
    sessionId: "session-3", 
    country: "Germany",
    city: "Berlin",
    device: "Desktop",
    browser: "Firefox",
    url: "https://example.com/features",
    referrer: "https://facebook.com",
  },
  {
    userId: "test-visitor-4",
    sessionId: "session-4",
    country: "Japan",
    city: "Tokyo", 
    device: "Mobile",
    browser: "Chrome",
    url: "https://example.com/about",
    referrer: "https://linkedin.com",
  },
  {
    userId: "test-visitor-5",
    sessionId: "session-5",
    country: "Australia",
    city: "Sydney",
    device: "Tablet",
    browser: "Safari",
    url: "https://example.com/contact",
    referrer: "https://reddit.com",
  },
  {
    userId: "test-visitor-6",
    sessionId: "session-6",
    country: "Canada",
    city: "Toronto",
    device: "Desktop", 
    browser: "Edge",
    url: "https://example.com/blog",
    referrer: "https://youtube.com",
  },
  {
    userId: "test-visitor-7",
    sessionId: "session-7",
    country: "France",
    city: "Paris",
    device: "Mobile",
    browser: "Chrome",
    url: "https://example.com/docs",
    referrer: "https://github.com",
  },
  {
    userId: "test-visitor-8",
    sessionId: "session-8",
    country: "Brazil",
    city: "São Paulo",
    device: "Desktop",
    browser: "Chrome",
    url: "https://example.com/support",
    referrer: "https://instagram.com",
  },
];

async function simulateVisitor(visitor: typeof testVisitors[0]) {
  try {
    console.log(`🌍 Simulating visitor from ${visitor.city}, ${visitor.country}`);
    
    const result = await client.mutation("presence:heartbeat", {
      roomId: WEBSITE_TRACKING_ID,
      userId: visitor.userId,
      sessionId: visitor.sessionId,
      interval: 30000, // 30 seconds
      url: visitor.url,
      referrer: visitor.referrer,
      device: visitor.device,
      browser: visitor.browser,
      country: visitor.country,
      city: visitor.city,
      timestamp: new Date().toISOString(),
    });

    console.log(`✅ Visitor ${visitor.userId} heartbeat sent successfully`);
    return result;
  } catch (error) {
    console.error(`❌ Failed to simulate visitor ${visitor.userId}:`, error);
    return null;
  }
}

async function simulateAllVisitors() {
  console.log("🚀 Starting real-time globe simulation...");
  console.log(`📍 Website ID: ${WEBSITE_TRACKING_ID}`);
  console.log(`👥 Simulating ${testVisitors.length} visitors from different countries\n`);

  // Simulate all visitors with a small delay between each
  for (const visitor of testVisitors) {
    await simulateVisitor(visitor);
    // Small delay to avoid overwhelming the system
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log("\n🎉 All visitors simulated successfully!");
  console.log("🌐 Check the dashboard to see the 3D globe with active visitors");
  console.log(`📊 Dashboard URL: http://localhost:3000/dashboard/websites/${WEBSITE_TRACKING_ID}`);
}

async function keepVisitorsActive() {
  console.log("\n🔄 Keeping visitors active with periodic heartbeats...");
  
  setInterval(async () => {
    console.log("💓 Sending heartbeats to keep visitors active...");
    
    // Send heartbeats for a subset of visitors to simulate realistic activity
    const activeVisitors = testVisitors.slice(0, Math.floor(Math.random() * testVisitors.length) + 1);
    
    for (const visitor of activeVisitors) {
      await simulateVisitor(visitor);
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log(`✅ Sent heartbeats for ${activeVisitors.length} active visitors`);
  }, 25000); // Every 25 seconds
}

async function main() {
  try {
    await simulateAllVisitors();
    
    // Ask if user wants to keep visitors active
    console.log("\n❓ Do you want to keep visitors active? (Press Ctrl+C to stop)");
    await keepVisitorsActive();
    
  } catch (error) {
    console.error("❌ Simulation failed:", error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log("\n👋 Stopping simulation...");
  process.exit(0);
});

if (require.main === module) {
  main();
}
