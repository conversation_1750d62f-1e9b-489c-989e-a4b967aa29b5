#!/usr/bin/env node

/**
 * Test script for website soft delete functionality
 * This script validates the soft delete implementation
 */

const { db } = require('../lib/db');
const { websites } = require('../lib/db/schema');
const { getUserWebsites, softDeleteWebsite, getWebsiteById } = require('../lib/db/queries');
const { eq, isNull } = require('drizzle-orm');

async function testSoftDelete() {
  console.log('🧪 Testing Website Soft Delete Functionality...\n');

  try {
    // Test 1: Check if deletedAt field exists
    console.log('1. Testing database schema...');
    const schemaTest = await db.select().from(websites).limit(1);
    console.log('✅ Database schema is accessible');

    // Test 2: Test getUserWebsites excludes soft-deleted websites
    console.log('\n2. Testing getUserWebsites query...');
    // This should only return non-deleted websites
    const activeWebsites = await getUserWebsites('test-user-id');
    console.log(`✅ getUserWebsites returned ${activeWebsites.length} active websites`);

    // Test 3: Test getWebsiteById with includeDeleted parameter
    console.log('\n3. Testing getWebsiteById with includeDeleted parameter...');
    if (activeWebsites.length > 0) {
      const websiteId = activeWebsites[0].id;
      
      // Should return the website (not deleted)
      const activeWebsite = await getWebsiteById(websiteId, false);
      console.log(`✅ getWebsiteById (includeDeleted=false): ${activeWebsite ? 'Found' : 'Not found'}`);
      
      // Should also return the website when including deleted
      const websiteIncludeDeleted = await getWebsiteById(websiteId, true);
      console.log(`✅ getWebsiteById (includeDeleted=true): ${websiteIncludeDeleted ? 'Found' : 'Not found'}`);
    } else {
      console.log('⚠️  No active websites found to test getWebsiteById');
    }

    // Test 4: Test soft delete validation
    console.log('\n4. Testing soft delete validation...');
    try {
      await softDeleteWebsite('non-existent-id');
      console.log('❌ Should have thrown error for non-existent website');
    } catch (error) {
      if (error.message.includes('not found')) {
        console.log('✅ Correctly throws error for non-existent website');
      } else {
        console.log(`⚠️  Unexpected error: ${error.message}`);
      }
    }

    // Test 5: Verify database constraints
    console.log('\n5. Testing database constraints...');
    const websitesWithDeletedAt = await db
      .select()
      .from(websites)
      .where(isNull(websites.deletedAt))
      .limit(5);
    console.log(`✅ Found ${websitesWithDeletedAt.length} websites with deletedAt IS NULL`);

    console.log('\n🎉 All soft delete tests completed successfully!');
    
    console.log('\n📋 Test Summary:');
    console.log('  - Database schema: ✅');
    console.log('  - getUserWebsites filtering: ✅');
    console.log('  - getWebsiteById with includeDeleted: ✅');
    console.log('  - Soft delete validation: ✅');
    console.log('  - Database constraints: ✅');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  testSoftDelete()
    .then(() => {
      console.log('\n✅ Test script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = {
  testSoftDelete
};
