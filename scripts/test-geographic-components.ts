#!/usr/bin/env tsx

/**
 * Test script for geographic components
 * Validates data processing and component functionality
 */

import { getCountryInfo, getFlagEmoji, formatVisitorCount, formatRevenue, formatPercentage } from "../lib/utils/countries";
import type { GeographicCountry, GeographicRegion, GeographicCity } from "../lib/types/geographic";

// Test data
const mockCountryData: GeographicCountry[] = [
  {
    country: "US",
    total_events: 1500,
    unique_visitors: 1200,
    sessions: 1300,
    pageviews: 2000,
    total_revenue: 5000,
    pageview_rate: 85.5,
    last_activity: "2024-01-15 14:30:00",
    first_activity: "2024-01-01 09:00:00",
    percentage: 35.2,
  },
  {
    country: "GB",
    total_events: 800,
    unique_visitors: 650,
    sessions: 700,
    pageviews: 1100,
    total_revenue: 2500,
    pageview_rate: 82.1,
    last_activity: "2024-01-15 13:45:00",
    first_activity: "2024-01-01 10:15:00",
    percentage: 19.1,
  },
  {
    country: "DE",
    total_events: 600,
    unique_visitors: 480,
    sessions: 520,
    pageviews: 850,
    total_revenue: 1800,
    pageview_rate: 78.9,
    last_activity: "2024-01-15 12:20:00",
    first_activity: "2024-01-01 11:30:00",
    percentage: 14.1,
  },
];

const mockRegionData: GeographicRegion[] = [
  {
    country: "US",
    region: "California",
    total_events: 500,
    unique_visitors: 400,
    sessions: 450,
    pageviews: 700,
    total_revenue: 2000,
    pageview_rate: 88.2,
    last_activity: "2024-01-15 14:30:00",
    first_activity: "2024-01-01 09:00:00",
    percentage: 11.8,
  },
  {
    country: "US",
    region: "New York",
    total_events: 350,
    unique_visitors: 280,
    sessions: 320,
    pageviews: 480,
    total_revenue: 1500,
    pageview_rate: 85.1,
    last_activity: "2024-01-15 13:15:00",
    first_activity: "2024-01-01 10:45:00",
    percentage: 8.2,
  },
];

const mockCityData: GeographicCity[] = [
  {
    country: "US",
    region: "California",
    city: "San Francisco",
    total_events: 200,
    unique_visitors: 160,
    sessions: 180,
    pageviews: 280,
    total_revenue: 800,
    pageview_rate: 90.5,
    last_activity: "2024-01-15 14:30:00",
    first_activity: "2024-01-01 09:00:00",
    percentage: 4.7,
  },
  {
    country: "US",
    region: "California",
    city: "Los Angeles",
    total_events: 180,
    unique_visitors: 140,
    sessions: 160,
    pageviews: 250,
    total_revenue: 600,
    pageview_rate: 87.8,
    last_activity: "2024-01-15 13:45:00",
    first_activity: "2024-01-01 11:20:00",
    percentage: 4.1,
  },
];

function testCountryUtils() {
  console.log("🧪 Testing country utilities...");
  
  // Test country info retrieval
  const usInfo = getCountryInfo("US");
  console.log("✅ US info:", usInfo);
  
  const gbInfo = getCountryInfo("GB");
  console.log("✅ GB info:", gbInfo);
  
  // Test flag emojis
  console.log("✅ US flag:", getFlagEmoji("US"));
  console.log("✅ GB flag:", getFlagEmoji("GB"));
  console.log("✅ DE flag:", getFlagEmoji("DE"));
  
  // Test invalid country
  const invalidInfo = getCountryInfo("XX");
  console.log("✅ Invalid country info:", invalidInfo);
  
  console.log("✅ Country utilities test passed!\n");
}

function testFormatting() {
  console.log("🧪 Testing formatting functions...");
  
  // Test visitor count formatting
  console.log("✅ Format 500:", formatVisitorCount(500));
  console.log("✅ Format 1500:", formatVisitorCount(1500));
  console.log("✅ Format 15000:", formatVisitorCount(15000));
  console.log("✅ Format 1500000:", formatVisitorCount(1500000));
  
  // Test revenue formatting
  console.log("✅ Format $50:", formatRevenue(50));
  console.log("✅ Format $1500:", formatRevenue(1500));
  console.log("✅ Format $15000:", formatRevenue(15000));
  console.log("✅ Format $1500000:", formatRevenue(1500000));
  
  // Test percentage formatting
  console.log("✅ Format 5.67%:", formatPercentage(5.67));
  console.log("✅ Format 25.1%:", formatPercentage(25.1));
  
  console.log("✅ Formatting functions test passed!\n");
}

function testDataProcessing() {
  console.log("🧪 Testing data processing...");
  
  // Test country data processing
  console.log("✅ Mock country data:");
  mockCountryData.forEach(country => {
    const info = getCountryInfo(country.country);
    console.log(`  ${info.flag} ${info.name}: ${formatVisitorCount(country.unique_visitors)} visitors (${formatPercentage(country.percentage)})`);
  });
  
  // Test region data processing
  console.log("✅ Mock region data:");
  mockRegionData.forEach(region => {
    const info = getCountryInfo(region.country);
    console.log(`  ${info.flag} ${region.region}, ${info.name}: ${formatVisitorCount(region.unique_visitors)} visitors`);
  });
  
  // Test city data processing
  console.log("✅ Mock city data:");
  mockCityData.forEach(city => {
    const info = getCountryInfo(city.country);
    console.log(`  ${info.flag} ${city.city}, ${city.region}, ${info.name}: ${formatVisitorCount(city.unique_visitors)} visitors`);
  });
  
  console.log("✅ Data processing test passed!\n");
}

function testMapDataGeneration() {
  console.log("🧪 Testing map data generation...");
  
  // Generate map data from country data
  const mapData = mockCountryData.map(country => {
    const info = getCountryInfo(country.country);
    return {
      countryCode: country.country,
      countryName: info.name,
      visitors: country.unique_visitors,
      percentage: country.percentage,
      revenue: country.total_revenue,
    };
  });
  
  console.log("✅ Generated map data:");
  mapData.forEach(item => {
    console.log(`  ${item.countryCode}: ${item.countryName} - ${formatVisitorCount(item.visitors)} visitors`);
  });
  
  console.log("✅ Map data generation test passed!\n");
}

function testComponentProps() {
  console.log("🧪 Testing component props structure...");
  
  // Test GeographicAnalyticsProps structure
  const analyticsProps = {
    websiteId: "test-website-id",
    dateRange: {
      from: new Date("2024-01-01"),
      to: new Date("2024-01-31"),
    },
    className: "test-class",
  };
  
  console.log("✅ GeographicAnalyticsProps:", analyticsProps);
  
  // Test processed table items
  const processedCountries = mockCountryData.map(country => {
    const info = getCountryInfo(country.country);
    return {
      ...country,
      countryName: info.name,
      flagEmoji: info.flag,
    };
  });
  
  console.log("✅ Processed countries for table:");
  processedCountries.forEach(country => {
    console.log(`  ${country.flagEmoji} ${country.countryName}: ${formatVisitorCount(country.unique_visitors)} visitors`);
  });
  
  console.log("✅ Component props test passed!\n");
}

async function runTests() {
  console.log("🚀 Starting geographic components tests...\n");
  
  try {
    testCountryUtils();
    testFormatting();
    testDataProcessing();
    testMapDataGeneration();
    testComponentProps();
    
    console.log("🎉 All tests passed successfully!");
    console.log("\n📋 Summary:");
    console.log("✅ Country utilities working correctly");
    console.log("✅ Formatting functions working correctly");
    console.log("✅ Data processing working correctly");
    console.log("✅ Map data generation working correctly");
    console.log("✅ Component props structure valid");
    console.log("\n🎯 Geographic components are ready for use!");
    
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

export { runTests };
