#!/usr/bin/env node

/**
 * DataFast 环境配置设置脚本
 * 帮助用户快速设置本地开发环境
 */

const fs = require("node:fs");
const path = require("node:path");
const crypto = require("node:crypto");

// 环境变量模板
const ENV_TEMPLATE = `# =============================================================================
# DataFast - 本地开发环境配置文件
# 由 setup-env.js 脚本自动生成
# =============================================================================

# =============================================================================
# 🚀 核心应用配置
# =============================================================================
# 应用运行环境（无需手动设置，Next.js 会在 dev/build/production 自动确定）
# NODE_ENV="development"

# 应用基础 URL
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# 追踪脚本 URL
NEXT_PUBLIC_TRACKING_SCRIPT_URL="http://localhost:3000"

# =============================================================================
# 🗄️ 数据库配置 (PostgreSQL)
# =============================================================================
# PostgreSQL 数据库连接字符串
# 格式: postgresql://用户名:密码@主机:端口/数据库名
DATABASE_URL="postgresql://datafast_user:datafast_password@localhost:5432/datafast_dev"

# =============================================================================
# 📊 Tinybird 分析配置 - 智能环境检测
# =============================================================================

# --- 生产环境 Tinybird 配置 ---
# 生产环境 Tinybird API URL
TINYBIRD_API_URL="https://api.tinybird.co"

# 生产环境 Tinybird Token (从 https://www.tinybird.co/tokens 获取)
TINYBIRD_TOKEN="p.your_production_tinybird_token_here"

# --- 本地开发环境 Tinybird 配置 ---
# 本地 Tinybird API URL (运行 tb local start 后可用)
TINYBIRD_API_URL_LOCAL="http://localhost:7181"

# 本地 Tinybird Token (运行 tb info 获取)
TINYBIRD_TOKEN_LOCAL="p.your_local_tinybird_token_here"

# --- 通用 Tinybird 配置 ---
# API 查询超时时间 (毫秒)
TINYBIRD_TIMEOUT="10000"

# 强制环境类型 (local | production) - 可选，留空为自动检测
# TINYBIRD_ENVIRONMENT="local"

# =============================================================================
# 🔐 认证配置 (NextAuth.js)
# =============================================================================
# 随机密钥，用于 JWT 签名和加密 (自动生成)
AUTH_SECRET="${crypto.randomBytes(32).toString("base64")}"

# Resend 邮件服务 API 密钥 (用于魔法链接登录)
# 从 https://resend.com 获取
AUTH_RESEND_KEY="re_your-resend-api-key"

# Google OAuth 配置 (可选)
# 从 https://console.cloud.google.com 获取
AUTH_GOOGLE_ID="your-google-oauth-client-id"
AUTH_GOOGLE_SECRET="your-google-oauth-client-secret"

# =============================================================================
# 🚀 缓存配置 (可选)
# =============================================================================
# Redis 配置 (用于高性能缓存，可选)
# 如果不设置将使用内存缓存
REDIS_URL="redis://localhost:6379"

# =============================================================================
# 💳 支付集成配置 (可选)
# =============================================================================
# =============================================================================
# 💳 Payment Provider Integration (New Revenue Tracking System)
# =============================================================================

# Payment Provider Credentials Encryption Key (Required for secure credential storage)
# This key is used to encrypt user payment provider API keys in the database
PAYMENT_CREDENTIALS_ENCRYPTION_KEY="${crypto.randomBytes(32).toString("hex")}"

# Legacy Stripe Configuration (Optional - for backward compatibility)
# These are now replaced by the user-provided credentials system
STRIPE_PUBLIC_KEY="pk_test_your_stripe_public_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"

# =============================================================================
# 🛡️ 安全配置
# =============================================================================
# CORS 允许的域名 (开发环境可用 "*"，生产环境设置具体域名)
ALLOWED_ORIGINS="*"

# 安全 Cookie 设置 (生产环境建议启用)
SECURE_COOKIES="false"

# =============================================================================
# 🐛 调试和日志配置
# =============================================================================
# 日志级别 (debug | info | warn | error)
LOG_LEVEL="debug"

# Tinybird 调试开关
DEBUG_TINYBIRD="true"

# 追踪调试开关
DEBUG_TRACKING="true"

# WebSocket 调试开关
DEBUG_WEBSOCKET="true"

# =============================================================================
# 📈 监控配置 (可选)
# =============================================================================
# Sentry DSN (错误监控，可选)
# SENTRY_DSN="https://your-sentry-dsn"

# Vercel Analytics (如果使用 Vercel 部署，可选)
# NEXT_PUBLIC_VERCEL_ANALYTICS="true"

# =============================================================================
# ⚙️ 高级配置 (可选)
# =============================================================================
# 最大文件上传大小 (字节)
MAX_FILE_SIZE="10485760"

# 最大事件批处理大小
MAX_BATCH_SIZE="100"

# 会话超时时间 (分钟)
SESSION_TIMEOUT="30"

# =============================================================================
# 🔧 开发环境特定配置
# =============================================================================
# 开发模式特性开关
ENABLE_DEVTOOLS="true"

# 模拟延迟 (毫秒，用于测试)
SIMULATE_DELAY="0"

# =============================================================================
# 🧪 测试配置 (可选)
# =============================================================================
# Playwright 测试基础 URL
PLAYWRIGHT_BASE_URL="http://localhost:3000"

# CI 环境标识 (自动设置)
# CI="false"

# =============================================================================
# 📝 配置说明和下一步操作
# =============================================================================
# 
# ✅ 已自动配置:
# 1. AUTH_SECRET - 已生成安全的随机密钥
# 2. 基础应用配置 - 本地开发环境设置
# 3. 调试模式 - 已启用开发调试功能
#
# 🔧 需要手动配置的项目:
# 1. DATABASE_URL - 设置您的 PostgreSQL 数据库连接
# 2. TINYBIRD_TOKEN_LOCAL - 运行 'tb local start' 和 'tb info' 获取
# 3. AUTH_RESEND_KEY - 从 https://resend.com 获取 (可选)
# 4. AUTH_GOOGLE_ID/SECRET - 从 Google Cloud Console 获取 (可选)
#
# 🚀 快速开始:
# 1. 启动 PostgreSQL 数据库
# 2. 运行 'npm run db:migrate' 创建数据库表
# 3. 启动本地 Tinybird: 'tb local start' 
# 4. 运行 'npm run dev' 启动开发服务器
#
# =============================================================================
`;

const envPath = path.join(process.cwd(), ".env.local");

function main() {
  console.log("🚀 DataFast 环境配置设置");
  console.log("=".repeat(50));

  // 检查是否已存在 .env.local 文件
  if (fs.existsSync(envPath)) {
    console.log("⚠️  .env.local 文件已存在");
    console.log("如果要重新生成，请先删除现有文件");
    console.log(`文件位置: ${envPath}`);
    return;
  }

  try {
    // 写入环境变量文件
    fs.writeFileSync(envPath, ENV_TEMPLATE);

    console.log("✅ 成功创建 .env.local 文件！");
    console.log(`📁 文件位置: ${envPath}`);
    console.log("");

    console.log("🔧 下一步操作:");
    console.log("");
    console.log("1. 📊 设置 Tinybird (本地开发):");
    console.log("   tb local start");
    console.log("   tb info  # 复制本地 token 到 TINYBIRD_TOKEN_LOCAL");
    console.log("");

    console.log("2. 🗄️  设置 PostgreSQL 数据库:");
    console.log("   # 安装并启动 PostgreSQL");
    console.log("   createdb datafast_dev");
    console.log("   # 更新 .env.local 中的 DATABASE_URL");
    console.log("");

    console.log("3. 🔄 运行数据库迁移:");
    console.log("   npm run db:migrate");
    console.log("");

    console.log("4. 🚀 启动开发服务器:");
    console.log("   npm run dev");
    console.log("");

    console.log("🔑 可选配置:");
    console.log("- AUTH_RESEND_KEY: 从 https://resend.com 获取 (邮件登录)");
    console.log(
      "- AUTH_GOOGLE_ID/SECRET: 从 Google Cloud Console 获取 (Google 登录)"
    );
    console.log("- STRIPE_*KEY: 从 Stripe 获取 (支付追踪)");
    console.log("");

    console.log("📚 更多信息请查看: docs/环境变量配置完整示例.md");
  } catch (error) {
    console.error("❌ 创建环境文件失败:", error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { ENV_TEMPLATE };
