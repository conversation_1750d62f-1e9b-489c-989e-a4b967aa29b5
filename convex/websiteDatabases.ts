import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

/**
 * Create or get a database mapping for a user-website combination
 */
export const createOrGetDatabaseMapping = mutation({
  args: {
    userId: v.string(),
    websiteId: v.string(),
  },
  handler: async (ctx, { userId, websiteId }) => {
    // Check if mapping already exists
    const existing = await ctx.db
      .query("website_databases")
      .withIndex("by_user_website", (q) => 
        q.eq("userId", userId).eq("websiteId", websiteId)
      )
      .first();

    if (existing) {
      // Update last accessed time
      await ctx.db.patch(existing._id, {
        lastAccessed: Date.now(),
      });
      return existing;
    }

    // Create new mapping
    const dbName = `user_${userId}_website_${websiteId}`;
    const newMapping = await ctx.db.insert("website_databases", {
      userId,
      websiteId,
      dbName,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      isActive: true,
    });

    return await ctx.db.get(newMapping);
  },
});

/**
 * Get database mapping for a specific user-website combination
 */
export const getDatabaseMapping = query({
  args: {
    userId: v.string(),
    websiteId: v.string(),
  },
  handler: async (ctx, { userId, websiteId }) => {
    return await ctx.db
      .query("website_databases")
      .withIndex("by_user_website", (q) => 
        q.eq("userId", userId).eq("websiteId", websiteId)
      )
      .first();
  },
});

/**
 * Get all database mappings for a user
 */
export const getUserDatabaseMappings = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, { userId }) => {
    return await ctx.db
      .query("website_databases")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
  },
});

/**
 * Get all database mappings for a website
 */
export const getWebsiteDatabaseMappings = query({
  args: {
    websiteId: v.string(),
  },
  handler: async (ctx, { websiteId }) => {
    return await ctx.db
      .query("website_databases")
      .withIndex("by_website", (q) => q.eq("websiteId", websiteId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
  },
});

/**
 * Deactivate a database mapping
 */
export const deactivateDatabaseMapping = mutation({
  args: {
    userId: v.string(),
    websiteId: v.string(),
  },
  handler: async (ctx, { userId, websiteId }) => {
    const mapping = await ctx.db
      .query("website_databases")
      .withIndex("by_user_website", (q) => 
        q.eq("userId", userId).eq("websiteId", websiteId)
      )
      .first();

    if (mapping) {
      await ctx.db.patch(mapping._id, {
        isActive: false,
      });
      return true;
    }
    return false;
  },
});

/**
 * Get database name for a user-website combination
 */
export const getDatabaseName = query({
  args: {
    userId: v.string(),
    websiteId: v.string(),
  },
  handler: async (ctx, { userId, websiteId }) => {
    const mapping = await ctx.db
      .query("website_databases")
      .withIndex("by_user_website", (q) => 
        q.eq("userId", userId).eq("websiteId", websiteId)
      )
      .first();

    return mapping?.dbName || null;
  },
});

/**
 * Update last accessed time for a database mapping
 */
export const updateLastAccessed = mutation({
  args: {
    userId: v.string(),
    websiteId: v.string(),
  },
  handler: async (ctx, { userId, websiteId }) => {
    const mapping = await ctx.db
      .query("website_databases")
      .withIndex("by_user_website", (q) => 
        q.eq("userId", userId).eq("websiteId", websiteId)
      )
      .first();

    if (mapping) {
      await ctx.db.patch(mapping._id, {
        lastAccessed: Date.now(),
      });
      return true;
    }
    return false;
  },
});

/**
 * Get statistics about database mappings
 */
export const getDatabaseMappingStats = query({
  args: {},
  handler: async (ctx) => {
    const allMappings = await ctx.db.query("website_databases").collect();
    const activeMappings = allMappings.filter(m => m.isActive);
    
    return {
      total: allMappings.length,
      active: activeMappings.length,
      inactive: allMappings.length - activeMappings.length,
      uniqueUsers: new Set(activeMappings.map(m => m.userId)).size,
      uniqueWebsites: new Set(activeMappings.map(m => m.websiteId)).size,
    };
  },
});
