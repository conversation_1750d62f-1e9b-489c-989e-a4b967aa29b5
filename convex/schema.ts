import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  visitor_details: defineTable({
    userId: v.string(),
    roomId: v.string(),
    sessionId: v.string(),
    url: v.optional(v.string()),
    referrer: v.optional(v.string()),
    device: v.optional(v.string()),
    browser: v.optional(v.string()),
    os: v.optional(v.string()),
    screen: v.optional(v.string()),
    country: v.optional(v.string()),
    city: v.optional(v.string()),
    timestamp: v.optional(v.string()),
    lastUpdate: v.number(),
  })
    .index("by_user_room", ["userId", "roomId"])
    .index("by_room", ["roomId"]),

  // Website-Database mapping for multi-tenant MotherDuck architecture
  website_databases: defineTable({
    userId: v.string(),
    websiteId: v.string(),
    dbName: v.string(),
    createdAt: v.number(),
    lastAccessed: v.optional(v.number()),
    isActive: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_website", ["websiteId"])
    .index("by_user_website", ["userId", "websiteId"])
    .index("by_db_name", ["dbName"]),
});
