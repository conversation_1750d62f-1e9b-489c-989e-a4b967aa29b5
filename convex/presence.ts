import { mutation, query } from "./_generated/server";
import { components } from "./_generated/api";
import { v } from "convex/values";
import { Presence } from "@convex-dev/presence";

export const presence = new Presence(components.presence);

export const heartbeat = mutation({
  args: {
    roomId: v.string(),
    userId: v.string(),
    sessionId: v.string(),
    interval: v.number(),
    // 访客详细信息 (可选)
    url: v.optional(v.string()),
    referrer: v.optional(v.string()),
    device: v.optional(v.string()),
    browser: v.optional(v.string()),
    os: v.optional(v.string()),
    screen: v.optional(v.string()),
    country: v.optional(v.string()),
    city: v.optional(v.string()),
    timestamp: v.optional(v.string()),
  },
  handler: async (
    ctx,
    { roomId, userId, sessionId, interval, ...visitorDetails }
  ) => {
    // Call the presence component's heartbeat function
    // this function will insert / update the user details such as device, browser, os, screen, timestamp etc.
    const result = await presence.heartbeat(
      ctx,
      roomId,
      userId,
      sessionId,
      interval
    );

    // 将访客详细信息存储在单独的表中
    if (Object.keys(visitorDetails).length > 0) {
      // 检查是否已有记录
      const existing = await ctx.db
        .query("visitor_details")
        .withIndex("by_user_room", (q) =>
          q.eq("userId", userId).eq("roomId", roomId)
        )
        .first();

      if (existing) {
        // 更新现有记录
        await ctx.db.patch(existing._id, {
          ...visitorDetails,
          lastUpdate: Date.now(),
        });
      } else {
        // 创建新记录
        await ctx.db.insert("visitor_details", {
          userId,
          roomId,
          sessionId,
          ...visitorDetails,
          lastUpdate: Date.now(),
        });
      }
    }

    // Return the tokens from the presence component
    return {
      success: true,
      roomToken: result.roomToken,
      sessionToken: result.sessionToken,
    };
  },
});

export const list = query({
  args: { roomToken: v.string() },
  handler: async (ctx, { roomToken }) => {
    return await presence.list(ctx, roomToken);
  },
});

export const listByRoomId = query({
  args: { roomId: v.string() },
  handler: async (ctx, { roomId }) => {
    return await presence.listRoom(ctx, roomId);
  },
});

// 获取带有详细信息的访客列表
export const listWithDetails = query({
  args: { roomToken: v.string() },
  handler: async (ctx, { roomToken }) => {
    // 获取 Presence 数据
    const presenceList = await presence.list(ctx, roomToken);

    // 获取访客详细信息 - 使用 by_room 索引
    const visitorDetails = await ctx.db
      .query("visitor_details")
      .withIndex("by_room", (q) => q.eq("roomId", roomToken))
      .collect();

    // 合并数据
    const detailsMap = new Map(
      visitorDetails.map((detail) => [detail.userId, detail])
    );

    return presenceList.map((presenceItem: any) => {
      const details = detailsMap.get(presenceItem.user || presenceItem.userId);
      return {
        ...presenceItem,
        details: details || null,
      };
    });
  },
});

// 获取带有详细信息的访客列表 (使用 roomId)
export const listWithDetailsByRoomId = query({
  args: { roomId: v.string() },
  handler: async (ctx, { roomId }) => {
    // 获取 Presence 数据
    const presenceList = await presence.listRoom(ctx, roomId);

    // 获取访客详细信息 - 使用 by_room 索引
    const visitorDetails = await ctx.db
      .query("visitor_details")
      .withIndex("by_room", (q) => q.eq("roomId", roomId))
      .collect();

    // 合并数据
    const detailsMap = new Map(
      visitorDetails.map((detail) => [detail.userId, detail])
    );

    return presenceList.map((presenceItem: any) => {
      const details = detailsMap.get(presenceItem.user || presenceItem.userId);
      return {
        ...presenceItem,
        details: details || null,
      };
    });
  },
});

export const disconnect = mutation({
  args: { sessionToken: v.string() },
  handler: async (ctx, { sessionToken }) => {
    return await presence.disconnect(ctx, sessionToken);
  },
});
