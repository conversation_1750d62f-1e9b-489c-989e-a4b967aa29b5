(() => {
  // 当前执行脚本标签
  const t = document.currentScript;

  // 方便获取 data- 属性，如 data-website-id
  const n = t.getAttribute.bind(t);

  // 判断是否是本地环境（localhost、127.*、.local等）
  function isLocalhost(hostname) {
    if (!hostname) return false;
    const h = hostname.toLowerCase();
    return (
      !!["localhost", "127.0.0.1", "::1"].includes(h) ||
      /^127(\.[0-9]+){0,3}$/.test(h) ||
      /^(\[)?::1?\]?$/.test(h) ||
      h.endsWith(".local") ||
      h.endsWith(".localhost")
    );
  }

  // 检测是否为爬虫/机器人/自动化工具运行环境
  function isBot() {
    try {
      const nav = window.navigator;
      if (
        window.navigator.webdriver === true ||
        window.callPhantom ||
        window._phantom ||
        window.__nightmare
      )
        return true;

      if (!nav || !window.location || !window.document) return true;

      const ua = nav.userAgent?.toLowerCase?.() || "";
      if (
        !ua ||
        ua.length < 5 ||
        ua.includes("headlesschrome") ||
        ua.includes("phantomjs") ||
        ua.includes("selenium") ||
        ua.includes("webdriver") ||
        ua.includes("puppeteer") ||
        ua.includes("playwright") ||
        ua.includes("python") ||
        ua.includes("curl") ||
        ua.includes("wget") ||
        ua.includes("java/") ||
        ua.includes("go-http") ||
        ua.includes("node.js") ||
        ua.includes("axios") ||
        ua.includes("postman")
      )
        return true;

      const knownProps = [
        "__webdriver_evaluate",
        "__selenium_evaluate",
        "__webdriver_script_function",
        "__webdriver_unwrapped",
        "__fxdriver_evaluate",
        "__driver_evaluate",
        "_Selenium_IDE_Recorder",
        "_selenium",
        "calledSelenium",
        "$cdc_asdjflasutopfhvcZLmcfl_",
      ];
      for (const prop of knownProps) {
        if (window[prop] !== undefined) return true;
      }

      if (
        document.documentElement.getAttribute("webdriver") ||
        document.documentElement.getAttribute("selenium") ||
        document.documentElement.getAttribute("driver")
      )
        return true;

      return false;
    } catch {
      return false;
    }
  }

  // 设置Cookie函数
  function setCookie(name, value, days) {
    let expires = "";
    if (days) {
      const date = new Date();
      date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
      expires = `; expires=${date.toUTCString()}`;
    }
    let cookieStr = `${name}=${value || ""}${expires}; path=/`;

    // 如果可用则设置 domain
    const domain = n("domain");
    if (
      domain &&
      !isLocalhost(window.location.hostname) &&
      window.location.protocol !== "file:"
    ) {
      cookieStr += `; domain=.${domain.replace(/^\./, "")}`;
    }
    document.cookie = cookieStr;
  }

  // 读取Cookie函数
  function getCookie(name) {
    const nameEQ = `${name}=`;
    const ca = document.cookie.split(";");
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === " ") c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }

  // 生成或读取访客标识UUID Cookie
  function getVisitorId() {
    let id = getCookie("instasight_visitor_id");
    if (!id) {
      id = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
      setCookie("instasight_visitor_id", id, 365);
    }
    return id;
  }

  // 生成或读取会话标识UUID Cookie（默认半小时有效）
  function getSessionId() {
    let id = getCookie("instasight_session_id");
    if (!id) {
      id = "sxxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
      setCookie("instasight_session_id", id, 1 / 48); // 30分钟
    }
    return id;
  }

  // 事件发送接口URL
  const apiUrl = t.src.includes("datafa.st")
    ? "https://datafa.st/api/events"
    : new URL("/api/events", window.location.origin).href;

  // 收集基本页面和访客信息
  function collectBaseInfo() {
    const url = window.location.href;
    if (!url) {
      console.warn("DataFast: Unable to collect href.");
      return null;
    }
    return {
      websiteId: n("website-id"),
      domain: n("domain"),
      href: url,
      referrer: document.referrer || null,
      viewport: { width: window.innerWidth, height: window.innerHeight },
      visitorId: getVisitorId(),
      sessionId: getSessionId(),
    };
  }

  // 发送事件数据
  function sendEvent(data, callback) {
    if (localStorage.getItem("datafast_ignore") === "true") {
      // 用户关闭了追踪
      console.log("DataFast: Tracking disabled via localStorage flag");
      if (callback) callback({ status: 200 });
      return;
    }
    if (isBot()) {
      // 机器人检测，停止发送
      console.log("DataFast: Bot detected, not sending data");
      if (callback) callback({ status: 200 });
      return;
    }
    const xhr = new XMLHttpRequest();
    xhr.open("POST", apiUrl, true);
    xhr.setRequestHeader("Content-Type", "application/json");
    xhr.onreadystatechange = () => {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === 200) {
          // 更新会话ID有效期
          setCookie("instasight_session_id", getSessionId(), 1 / 48);
          console.log("Event data sent successfully");
        } else {
          console.error("Error sending event data:", xhr.status);
        }
        if (callback) callback({ status: xhr.status });
      }
    };
    xhr.send(JSON.stringify(data));
  }

  // 发送页面浏览、点击等事件辅助函数
  function trackEvent(type, extraData, callback) {
    const baseData = collectBaseInfo();
    if (!baseData) {
      if (callback) callback({ status: 400 });
      return;
    }
    baseData.type = type;
    if (extraData) {
      baseData.extraData = extraData;
    }
    sendEvent(baseData, callback);
  }

  // 对外暴露的事件采集函数，支持常用事件和自定义事件
  function datafast(eventName, eventData) {
    const supportedEvents = ["signup", "payment", "identify", "custom"];
    // 自动检测是否允许采集
    if (trackingDisabled) {
      console.log(`DataFast: Event '${eventName}' ignored - ${disableReason}`);
      return;
    }
    if (!eventName) {
      console.warn("DataFast: Missing event_name for custom event");
      return;
    }
    if (eventName === "signup" && !eventData?.email) {
      console.warn("DataFast: Missing email for signup event");
      return;
    }
    if (eventName === "payment" && !eventData?.email) {
      console.warn("DataFast: Missing email for payment event");
      return;
    }
    if (eventName === "identify" && !eventData?.user_id) {
      console.warn("DataFast: Missing user_id for identify event");
      return;
    }

    if (eventName === "signup" || eventName === "payment") {
      trackEvent(eventName, { email: eventData.email });
    } else if (eventName === "identify") {
      // 识别用户事件
      trackEvent("identify", {
        user_id: eventData.user_id,
        name: eventData.name || "",
        ...eventData,
      });
    } else if (eventName === "custom") {
      // 自定义事件参数校验
      const cleanedData = cleanCustomData(eventData);
      if (cleanedData === null) {
        console.error(
          "DataFast: Custom event rejected due to validation errors"
        );
        return;
      }
      trackEvent("custom", { eventName, ...cleanedData });
    } else {
      console.log(
        `DataFast: Event '${eventName}' ignored - unknown event type`
      );
    }
  }

  // 自定义事件参数清理与校验函数（限制长度、格式等）
  function cleanCustomData(data) {
    if (!data || typeof data !== "object" || Array.isArray(data)) {
      console.warn("DataFast: customData must be a non-null object");
      return {};
    }
    const result = {};
    const maxProps = 10;
    const propNameMaxLength = 32;
    const valueMaxLength = 255;
    let count = 0;

    function validName(name) {
      return /^[a-z0-9_-]{1,32}$/.test(name);
    }

    function sanitizeValue(val) {
      if (val === null || val === undefined) return "";
      let str = String(val);
      if (str.length > valueMaxLength) str = str.substring(0, valueMaxLength);
      // 去除潜在恶意代码片段
      return str
        .replace(/[<>'"&]/g, "")
        .replace(/javascript:/gi, "")
        .replace(/on\w+=/gi, "")
        .replace(/data:/gi, "")
        .replace(/vbscript:/gi, "")
        .trim();
    }

    for (const [key, val] of Object.entries(data)) {
      if (count >= maxProps) {
        console.error(
          `DataFast: Maximum ${maxProps} custom parameters allowed`
        );
        return null;
      }
      if (!validName(key)) {
        console.error(
          `DataFast: Invalid property name "${key}". Use only lowercase letters, numbers, underscores, and hyphens. Max ${propNameMaxLength} characters.`
        );
        return null;
      }
      result[key.toLowerCase()] = sanitizeValue(val);
      count++;
    }
    return result;
  }

  // 监听用户点击外链自动上报
  function onLinkClick(event) {
    const target = event.target.closest?.("a");
    if (target?.href && isExternalUrl(target.href)) {
      trackEvent("external_link", {
        url: target.href,
        text: target.textContent?.trim(),
      });
    }
  }

  // 判断是否外部链接
  function isExternalUrl(url) {
    try {
      const u = new URL(url, window.location.origin);
      return (
        u.protocol === "http:" ||
        (u.protocol === "https:" && u.hostname !== window.location.hostname)
      );
    } catch {
      return false;
    }
  }

  // 监听页面变化做页面浏览上报（包括单页应用的路由切换）
  function trackPageview() {
    trackEvent("pageview");
  }

  // 判断iframe中、调试模式、本地环境等条件是否允许跟踪
  let trackingDisabled = true;
  let disableReason = "";
  if (isBot()) {
    trackingDisabled = true;
    disableReason = "Tracking disabled - bot detected";
  } else if (
    isLocalhost(window.location.hostname) ||
    window.location.protocol === "file:"
  ) {
    trackingDisabled = true;
    disableReason = "Tracking disabled on localhost, file protocol";
  } else if (window !== window.parent && n("debug") !== "true") {
    trackingDisabled = true;
    disableReason = "Tracking disabled inside an iframe";
  } else if (!n("website-id") || !n("domain")) {
    trackingDisabled = true;
    disableReason = "Missing website ID or domain";
  } else {
    trackingDisabled = false;
  }

  // 批量处理页面加载时之前队列事件
  let queuedEvents = [];
  if (window.datafast && Array.isArray(window.datafast.q)) {
    queuedEvents = window.datafast.q.map((arr) => Array.from(arr));
  }

  // 替换全局datafast函数
  window.datafast = datafast;

  // 处理之前缓存的事件队列
  (function processQueue() {
    while (queuedEvents.length > 0) {
      const args = queuedEvents.shift();
      if (Array.isArray(args) && args.length > 0) {
        try {
          datafast.apply(null, args);
        } catch (e) {
          console.error("DataFast: Error processing queued call:", e, args);
        }
      }
    }
  })();

  if (!trackingDisabled) {
    // 页面加载时发起页面浏览事件
    trackPageview();

    // 监听点击外链事件
    document.addEventListener("click", onLinkClick);

    // 辅助监听键盘事件：Enter、Space 等触发点击
    document.addEventListener("keydown", (e) => {
      if (e.key === "Enter" || e.key === " ") {
        onLinkClick({ target: e.target });
      }
    });

    // 监听页面历史pushState变化（支持 SPA 路由监控）
    let lastPathname = window.location.pathname;
    const origPushState = window.history.pushState;
    window.history.pushState = (...args) => {
      origPushState.apply(window.history, args);
      if (window.location.pathname !== lastPathname) {
        lastPathname = window.location.pathname;
        trackPageview();
      }
    };
    window.addEventListener("popstate", () => {
      if (window.location.pathname !== lastPathname) {
        lastPathname = window.location.pathname;
        trackPageview();
      }
    });

    // 自动监听URL中的支付相关ID（Stripe、Polar、Lemonsqueezy）
    function autoDetectPayments() {
      // ... 此处忽略具体实现，已在原脚本体现
    }
    autoDetectPayments();
  } else {
    console.warn(`DataFast: ${disableReason}`);
  }
})();
