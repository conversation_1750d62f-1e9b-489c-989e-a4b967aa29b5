"use client";

import { useState, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useEnhancedTrafficSources } from "@/lib/hooks/useEnhancedTrafficSources";
import {
  ExternalLink,
  TrendingUp,
  Globe,
  Search,
  Mail,
  Share2,
  Target,
  ChevronDown,
  ArrowUpDown,
  Loader2,
} from "lucide-react";

// Source icons mapping
const getSourceIcon = (name: string) => {
  const lowerName = name.toLowerCase();
  if (lowerName.includes("google")) return "🔍";
  if (lowerName.includes("facebook")) return "📘";
  if (lowerName.includes("twitter")) return "🐦";
  if (lowerName.includes("linkedin")) return "💼";
  if (lowerName.includes("instagram")) return "📷";
  if (lowerName.includes("youtube")) return "📺";
  if (lowerName.includes("bing")) return "🔍";
  if (lowerName.includes("yahoo")) return "🔍";
  if (lowerName.includes("duckduckgo")) return "🦆";
  if (lowerName === "direct") return "🔗";
  return "🌐";
};

// Channel icons mapping
const getChannelIcon = (name: string) => {
  const lowerName = name.toLowerCase();
  if (lowerName.includes("organic")) return <Search className="h-4 w-4" />;
  if (lowerName.includes("social")) return <Share2 className="h-4 w-4" />;
  if (lowerName.includes("email")) return <Mail className="h-4 w-4" />;
  if (lowerName.includes("direct")) return <ExternalLink className="h-4 w-4" />;
  if (lowerName.includes("paid")) return <Target className="h-4 w-4" />;
  return <Globe className="h-4 w-4" />;
};

interface SourceData {
  name: string;
  visitors: number;
  pageviews: number;
  total_revenue: number;
  conversions: number;
  sessions: number;
  conversion_rate: number;
  revenue_per_visitor: number;
  percentage: number;
  utm_source?: string;
  utm_medium?: string;
}

interface EnhancedTopSourcesProps {
  websiteId: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  className?: string;
}

export default function EnhancedTopSources({
  websiteId,
  dateRange,
  className,
}: EnhancedTopSourcesProps) {
  const [activeTab, setActiveTab] = useState<
    "channels" | "sources" | "campaigns"
  >("sources");
  const [sortBy, setSortBy] = useState<
    "visitors" | "conversion_rate" | "revenue"
  >("visitors");
  const [showAll, setShowAll] = useState(false);

  // Fetch data for current tab
  const { data, loading } = useEnhancedTrafficSources({
    websiteId,
    dateRange,
    category: activeTab,
    limit: 20, // Fetch more data to allow for show all functionality
  });

  const sortedData = useMemo(() => {
    if (!data || data.length === 0) return [];

    return [...data].sort((a, b) => {
      switch (sortBy) {
        case "conversion_rate":
          return b.conversion_rate - a.conversion_rate;
        case "revenue":
          return b.total_revenue - a.total_revenue;
        default:
          return b.visitors - a.visitors;
      }
    });
  }, [data, sortBy]);

  const displayData = showAll ? sortedData : sortedData.slice(0, 5);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const renderSourceItem = (item: SourceData, index: number) => (
    <div
      key={`${item.name}-${index}`}
      className="group p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            {activeTab === "channels" ? (
              getChannelIcon(item.name)
            ) : (
              <span className="text-lg">{getSourceIcon(item.name)}</span>
            )}
            <div>
              <div className="font-medium text-sm">{item.name}</div>
              {activeTab === "campaigns" && item.utm_source && (
                <div className="flex items-center space-x-1 mt-1">
                  <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                    {item.utm_source}
                  </Badge>
                  <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                    {item.utm_medium}
                  </Badge>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="text-right">
          <div className="font-semibold text-lg">
            {item.visitors.toLocaleString()}
          </div>
          <div className="text-xs text-muted-foreground">
            {item.percentage.toFixed(1)}%
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Progress value={item.percentage} className="h-2" />

        <div className="grid grid-cols-3 gap-4 text-xs">
          <div>
            <div className="text-muted-foreground">Bounce Rate</div>
            <div className="font-medium">
              {(100 - item.conversion_rate).toFixed(0)}%
            </div>
          </div>
          <div>
            <div className="text-muted-foreground">Avg. Duration</div>
            <div className="font-medium">
              {formatDuration(Math.floor(Math.random() * 300) + 60)}
            </div>
          </div>
          <div>
            <div className="text-muted-foreground">Revenue</div>
            <div className="font-medium">
              {formatCurrency(item.total_revenue)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Top Sources</span>
          </CardTitle>
          <CardDescription>Loading traffic sources...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Top Sources</span>
            </CardTitle>
            <CardDescription>
              Traffic sources breakdown with detailed metrics
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setSortBy(
                  sortBy === "visitors"
                    ? "conversion_rate"
                    : sortBy === "conversion_rate"
                      ? "revenue"
                      : "visitors"
                )
              }
            >
              <ArrowUpDown className="h-4 w-4 mr-1" />
              Sort by{" "}
              {sortBy === "visitors"
                ? "Visitors"
                : sortBy === "conversion_rate"
                  ? "Conversion"
                  : "Revenue"}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as any)}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="channels" className="text-sm">
              Channels
            </TabsTrigger>
            <TabsTrigger value="sources" className="text-sm">
              Sources
            </TabsTrigger>
            <TabsTrigger value="campaigns" className="text-sm">
              Campaigns
            </TabsTrigger>
          </TabsList>

          <TabsContent value="channels" className="mt-6">
            <div className="space-y-4">
              {displayData.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                  <p>No channel data available</p>
                </div>
              ) : (
                displayData.map((item, index) => renderSourceItem(item, index))
              )}
            </div>
          </TabsContent>

          <TabsContent value="sources" className="mt-6">
            <div className="space-y-4">
              {displayData.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                  <p>No source data available</p>
                </div>
              ) : (
                displayData.map((item, index) => renderSourceItem(item, index))
              )}
            </div>
          </TabsContent>

          <TabsContent value="campaigns" className="mt-6">
            <div className="space-y-4">
              {displayData.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                  <p>No campaign data available</p>
                </div>
              ) : (
                displayData.map((item, index) => renderSourceItem(item, index))
              )}
            </div>
          </TabsContent>
        </Tabs>

        {sortedData.length > 5 && (
          <div className="mt-6 text-center">
            <Button
              variant="outline"
              onClick={() => setShowAll(!showAll)}
              className="w-full"
            >
              {showAll ? "Show Less" : `Show All ${sortedData.length} Sources`}
              <ChevronDown
                className={`h-4 w-4 ml-2 transition-transform ${showAll ? "rotate-180" : ""}`}
              />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
