"use client";

import Analytics<PERSON>hart from "@/components/dashboard/analytics-chart";
import StatsFilters, {
  type StatsFiltersState,
} from "@/components/dashboard/stats-filters";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { getOptimalInterval } from "@/lib/utils/date";
import { useCallback, useEffect, useMemo, useState } from "react";
import { getChartData } from "@/lib/actions/cached-dashboard";

interface WebsiteStatsPanelProps {
  websiteId: string;
  initialFromISO: string;
  initialToISO: string;
}

export default function WebsiteStatsPanel({
  websiteId,
  initialFromISO,
  initialToISO,
}: WebsiteStatsPanelProps) {
  const initialFilters: StatsFiltersState = useMemo(
    () => ({
      dateRange: {
        from: new Date(initialFromISO),
        to: new Date(initialToISO),
      },
      metric: "visitors",
      interval: getOptimalInterval(
        new Date(initialFromISO),
        new Date(initialToISO)
      ),
    }),
    [initialFromISO, initialToISO]
  );

  const [filters, setFilters] = useState<StatsFiltersState>(initialFilters);
  const [chartData, setChartData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const fetchChart = useCallback(async () => {
    setLoading(true);
    try {
      const dateRange = {
        from: filters.dateRange.from,
        to: filters.dateRange.to,
      } as any;

      if (filters.metric === "revenue") {
        const res = await getChartData(
          websiteId,
          dateRange,
          "revenue",
          filters.interval
        );
        // Normalize to component shape
        const normalized = res?.map((p: any) => ({
          date: p.date,
          visitors: 0,
          pageviews: 0,
          revenue: p.value ?? 0,
        }));
        setChartData(normalized);
      } else {
        // Fetch both visitors and pageviews for composed chart
        const [visitors, pageviews] = await Promise.all([
          getChartData(websiteId, dateRange, "visitors", filters.interval),
          getChartData(websiteId, dateRange, "pageviews", filters.interval),
        ]);
        const byDate = new Map<
          string,
          { date: string; visitors: number; pageviews: number; revenue: number }
        >();
        visitors?.forEach((p: any) => {
          byDate.set(p.date, {
            date: p.date,
            visitors: p.value ?? 0,
            pageviews: 0,
            revenue: 0,
          });
        });
        pageviews?.forEach((p: any) => {
          const curr = byDate.get(p.date) || {
            date: p.date,
            visitors: 0,
            pageviews: 0,
            revenue: 0,
          };
          curr.pageviews = p.value ?? 0;
          byDate.set(p.date, curr);
        });
        setChartData(Array.from(byDate.values()));
      }
    } finally {
      setLoading(false);
    }
  }, [websiteId, filters]);

  useEffect(() => {
    void fetchChart();
  }, [fetchChart]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Visitors Over Time</CardTitle>
        <CardDescription>
          Interactive filters for metric, interval and date range
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <StatsFilters value={filters} onChange={setFilters} />
        </div>
        <AnalyticsChart
          data={chartData || []}
          type={filters.metric === "revenue" ? "area" : "composed"}
          metric={filters.metric}
          title={loading ? "Loading..." : "Visitors & Pageviews Over Time"}
          description=""
        />
      </CardContent>
    </Card>
  );
}
