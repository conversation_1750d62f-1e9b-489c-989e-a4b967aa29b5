"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  FileText,
  LogIn,
  LogOut,
  TrendingUp,
  Users,
  RefreshCw,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface PageData {
  url: string;
  pageviews: number;
  visitors: number;
  bounce_rate: number;
  avg_duration_minutes: number;
}

interface PagesAnalyticsProps {
  topPages: PageData[];
  className?: string;
}

type PageTab = "top" | "entry" | "exit";

export default function PagesAnalytics({
  topPages,
  className,
}: PagesAnalyticsProps) {
  const [activeTab, setActiveTab] = useState<PageTab>("top");

  // Mock data for entry and exit pages (in real implementation, this would come from props or API)
  const entryPages: PageData[] = [
    {
      url: "/",
      pageviews: 1847,
      visitors: 1523,
      bounce_rate: 45.2,
      avg_duration_minutes: 4.2,
    },
    {
      url: "/pricing",
      pageviews: 834,
      visitors: 692,
      bounce_rate: 35.1,
      avg_duration_minutes: 6.1,
    },
    {
      url: "/features",
      pageviews: 587,
      visitors: 454,
      bounce_rate: 42.3,
      avg_duration_minutes: 5.2,
    },
  ];

  const exitPages: PageData[] = [
    {
      url: "/contact",
      pageviews: 567,
      visitors: 423,
      bounce_rate: 78.2,
      avg_duration_minutes: 2.1,
    },
    {
      url: "/pricing",
      pageviews: 434,
      visitors: 392,
      bounce_rate: 65.1,
      avg_duration_minutes: 3.1,
    },
    {
      url: "/about",
      pageviews: 287,
      visitors: 254,
      bounce_rate: 72.3,
      avg_duration_minutes: 2.8,
    },
  ];

  const getCurrentData = () => {
    switch (activeTab) {
      case "top":
        return topPages;
      case "entry":
        return entryPages;
      case "exit":
        return exitPages;
      default:
        return topPages;
    }
  };

  const getTabTitle = () => {
    switch (activeTab) {
      case "top":
        return "Top Pages";
      case "entry":
        return "Entry Pages";
      case "exit":
        return "Exit Pages";
      default:
        return "Top Pages";
    }
  };

  const getTabDescription = () => {
    switch (activeTab) {
      case "top":
        return "Most visited pages this month";
      case "entry":
        return "Pages where users first land";
      case "exit":
        return "Pages where users leave the site";
      default:
        return "Most visited pages this month";
    }
  };

  const currentData = getCurrentData();

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <div className="flex items-center space-x-2">
          <FileText className="h-4 w-4 text-blue-600" />
          <CardTitle className="text-base font-semibold">
            {getTabTitle()}
          </CardTitle>
        </div>
        <div className="flex items-center space-x-1">
          <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
            {currentData.length} Pages
          </Badge>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as PageTab)}
        >
          <div className="px-4 mb-4">
            <TabsList className="grid w-full grid-cols-3 h-9">
              <TabsTrigger
                value="top"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <TrendingUp className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">Top</span>
              </TabsTrigger>
              <TabsTrigger
                value="entry"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <LogIn className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">Entry</span>
              </TabsTrigger>
              <TabsTrigger
                value="exit"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <LogOut className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">Exit</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="px-4 pb-4">
            <TabsContent value="top" className="mt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    {getTabDescription()}
                  </h3>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>Views</span>
                    <span>Bounce</span>
                  </div>
                </div>
                <PagesList data={currentData} />
              </div>
            </TabsContent>

            <TabsContent value="entry" className="mt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    {getTabDescription()}
                  </h3>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>Views</span>
                    <span>Bounce</span>
                  </div>
                </div>
                <PagesList data={currentData} />
              </div>
            </TabsContent>

            <TabsContent value="exit" className="mt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    {getTabDescription()}
                  </h3>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>Views</span>
                    <span>Bounce</span>
                  </div>
                </div>
                <PagesList data={currentData} />
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
}

interface PagesListProps {
  data: PageData[];
}

function PagesList({ data }: PagesListProps) {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>No page data available yet</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {data.map((page) => (
        <div
          key={page.url}
          className="flex items-start justify-between gap-4 p-3 rounded-lg border hover:bg-muted/50 transition-colors"
        >
          <div className="flex-1 min-w-0">
            <p className="font-medium break-all text-sm leading-relaxed">
              {page.url}
            </p>
            <p className="text-xs text-muted-foreground">
              {page.visitors} visitors • {page.avg_duration_minutes.toFixed(1)}m
              avg duration
            </p>
          </div>
          <div className="text-right flex-shrink-0 space-y-1">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-sm">
                {page.pageviews.toLocaleString()}
              </span>
              <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                {page.bounce_rate.toFixed(1)}%
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">views</p>
          </div>
        </div>
      ))}
    </div>
  );
}
