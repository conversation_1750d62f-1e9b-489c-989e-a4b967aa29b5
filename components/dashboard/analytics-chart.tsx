"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import {
  Area,
  AreaChart,
  Bar,
  CartesianGrid,
  ComposedChart,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>Axis,
} from "recharts";

interface ChartData {
  date: string;
  visitors: number;
  pageviews: number;
  revenue: number;
}

interface AnalyticsChartProps {
  data: ChartData[];
  type?: "line" | "area" | "composed";
  metric?: "visitors" | "pageviews" | "revenue";
  title: string;
  description?: string;
}

export default function AnalyticsChart({
  data,
  type = "line",
  metric = "visitors",
  title,
  description,
}: AnalyticsChartProps) {
  const formatValue = (value: number) => {
    if (metric === "revenue") {
      return `$${value.toLocaleString()}`;
    }
    return value.toLocaleString();
  };

  const getColor = () => {
    switch (metric) {
      case "revenue":
        return "#10b981"; // green
      case "pageviews":
        return "#f97316"; // orange
      case "visitors":
      default:
        return "#3b82f6"; // blue
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            {type === "area" ? (
              <AreaChart
                data={data}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  className="stroke-muted"
                  vertical={false}
                />
                <XAxis
                  dataKey="date"
                  className="text-xs fill-muted-foreground"
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                />
                <YAxis
                  className="text-xs fill-muted-foreground"
                  tick={{ fontSize: 12 }}
                  tickFormatter={formatValue}
                  tickLine={false}
                  axisLine={false}
                  width={56}
                />
                <Tooltip />
                <defs>
                  <linearGradient
                    id={`chart-${metric}`}
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="5%"
                      stopColor={getColor()}
                      stopOpacity={0.25}
                    />
                    <stop
                      offset="95%"
                      stopColor={getColor()}
                      stopOpacity={0.03}
                    />
                  </linearGradient>
                </defs>
                <Area
                  type="monotoneX"
                  dataKey={metric}
                  stroke={getColor()}
                  fill={`url(#chart-${metric})`}
                  strokeWidth={2}
                  dot={false}
                  connectNulls
                  activeDot={{ r: 5 }}
                />
              </AreaChart>
            ) : (
              <ComposedChart
                data={data}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  className="stroke-muted"
                  vertical={false}
                />
                <XAxis
                  dataKey="date"
                  className="text-xs fill-muted-foreground"
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                />
                <YAxis
                  className="text-xs fill-muted-foreground"
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                  width={56}
                />
                <Tooltip />
                <Bar
                  dataKey="pageviews"
                  fill="#f97316"
                  radius={[2, 2, 0, 0]}
                  barSize={20}
                  name="Pageviews"
                />
                <Line
                  type="monotone"
                  dataKey="visitors"
                  stroke="#3b82f6"
                  strokeWidth={2.5}
                  dot={false}
                  activeDot={{ r: 5, fill: "#3b82f6" }}
                  name="Visitors"
                />
              </ComposedChart>
            )}
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
