"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useRealtimeData } from "@/lib/hooks/useRealtimeData"
import {
  Activity,
  DollarSign,
  Eye,
  RefreshCw,
  TrendingUp,
  Users,
  Wifi,
  WifiOff,
} from "lucide-react"
import { useState } from "react"

interface RealtimeAnalyticsProps {
  websiteId: string
  className?: string
}

export default function RealtimeAnalytics({ websiteId, className }: RealtimeAnalyticsProps) {
  const [isEnabled, setIsEnabled] = useState(true)
  const { data, isConnected, error, reconnect, visitors, onlineCount, overview, lastUpdate } =
    useRealtimeData({
      websiteId,
      enabled: isEnabled,
    })

  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp)
    const seconds = Math.floor((Date.now() - date.getTime()) / 1000)
    if (seconds < 60) return `${seconds}s ago`
    const minutes = Math.floor(seconds / 60)
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    return `${hours}h ago`
  }

  const formatLastUpdate = (timestamp?: string) => {
    if (!timestamp) return "Never"
    return formatTimeAgo(timestamp)
  }

  const getConnectionStatus = () => {
    if (error) return { text: "Error", color: "text-red-500", icon: WifiOff }
    if (!isConnected)
      return {
        text: "Connecting...",
        color: "text-yellow-500",
        icon: RefreshCw,
      }
    return { text: "Live", color: "text-green-500", icon: Wifi }
  }

  const status = getConnectionStatus()
  const StatusIcon = status.icon

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Connection Status */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Real-time Analytics</h2>
          <p className="text-muted-foreground">Live data from your website powered by Tinybird</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <StatusIcon
              className={`h-4 w-4 ${status.color} ${isConnected ? "" : "animate-spin"}`}
            />
            <span className={`text-sm font-medium ${status.color}`}>{status.text}</span>
          </div>
          {error && (
            <Button variant="outline" size="sm" onClick={reconnect}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Reconnect
            </Button>
          )}
          <Button
            variant={isEnabled ? "default" : "outline"}
            size="sm"
            onClick={() => setIsEnabled(!isEnabled)}
          >
            {isEnabled ? "Pause" : "Resume"}
          </Button>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-600">
              <WifiOff className="h-5 w-5" />
              <span className="font-medium">Connection Error</span>
            </div>
            <p className="text-sm text-red-600 mt-1">{error}</p>
            <Button variant="outline" size="sm" onClick={reconnect} className="mt-3">
              Try Again
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Real-time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online Visitors</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className="text-2xl font-bold">{onlineCount}</div>
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
            </div>
            <p className="text-xs text-muted-foreground">Active right now</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Visitors</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.visitors?.toLocaleString() || 0}</div>
            <p className="text-xs text-muted-foreground">Today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Page Views</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.pageviews?.toLocaleString() || 0}</div>
            <p className="text-xs text-muted-foreground">Today</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${overview.total_revenue?.toLocaleString() || 0}
            </div>
            <p className="text-xs text-muted-foreground">Today</p>
          </CardContent>
        </Card>
      </div>

      {/* Live Visitor Feed */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Live Visitor Activity</span>
                <Badge variant="outline" className="ml-2">
                  {visitors.length} recent
                </Badge>
              </CardTitle>
              <CardDescription>
                Real-time visitor activity • Last update: {formatLastUpdate(lastUpdate)}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {visitors.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
              <p className="text-muted-foreground">No recent activity</p>
              <p className="text-sm text-muted-foreground">
                Visitors will appear here as they browse your site
              </p>
            </div>
          ) : (
            <ScrollArea className="h-[400px]">
              <div className="space-y-3">
                {visitors.map(visitor => (
                  <div
                    key={`${visitor.visitor_id}-${visitor.timestamp}`}
                    className="flex items-center justify-between p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                        <span className="text-sm font-medium">
                          {visitor.city && visitor.country
                            ? `${visitor.city}, ${visitor.country}`
                            : visitor.country || "Unknown Location"}
                        </span>
                      </div>
                      {visitor.device && (
                        <Badge variant="secondary" className="text-xs">
                          {visitor.device}
                        </Badge>
                      )}
                      {visitor.browser && (
                        <Badge variant="outline" className="text-xs">
                          {visitor.browser}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-3 text-xs text-muted-foreground">
                      <span className="max-w-[150px] truncate">{visitor.url}</span>
                      <span>{formatTimeAgo(visitor.timestamp)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Performance Metrics</CardTitle>
            <CardDescription>Real-time website performance</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Bounce Rate</span>
                <span className="text-sm text-muted-foreground">
                  {overview.bounce_rate?.toFixed(1) || 0}%
                </span>
              </div>
              <Progress value={overview.bounce_rate || 0} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Avg. Session Duration</span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(overview.avg_session_duration || 0)}s
                </span>
              </div>
              <Progress
                value={Math.min(((overview.avg_session_duration || 0) / 300) * 100, 100)}
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Stream Status</CardTitle>
            <CardDescription>Real-time connection information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Connection</span>
              <div className="flex items-center space-x-2">
                <StatusIcon className={`h-4 w-4 ${status.color}`} />
                <span className={`text-sm ${status.color}`}>{status.text}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Data Source</span>
              <span className="text-sm text-muted-foreground">Tinybird Real-time</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Update Frequency</span>
              <span className="text-sm text-muted-foreground">30 seconds</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Last Update</span>
              <span className="text-sm text-muted-foreground">{formatLastUpdate(lastUpdate)}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
