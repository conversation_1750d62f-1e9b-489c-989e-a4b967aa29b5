import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { LucideIcon, TrendingUp, TrendingDown } from "lucide-react";

interface MetricsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    label: string;
  };
  icon: LucideIcon;
  className?: string;
  prefix?: string;
  suffix?: string;
}

export default function MetricsCard({
  title,
  value,
  change,
  icon: Icon,
  className,
  prefix = "",
  suffix = "",
}: MetricsCardProps) {
  const formatValue = (val: string | number) => {
    if (typeof val === "number") {
      if (prefix === "$") {
        return `${prefix}${val.toLocaleString()}`;
      }
      return `${prefix}${val.toLocaleString()}${suffix}`;
    }
    return `${prefix}${val}${suffix}`;
  };

  const getChangeColor = (changeValue: number) => {
    if (changeValue > 0) return "text-green-600";
    if (changeValue < 0) return "text-red-600";
    return "text-gray-500";
  };

  const getChangeIcon = (changeValue: number) => {
    if (changeValue > 0) return "↗";
    if (changeValue < 0) return "↘";
    return "→";
  };

  return (
    <Card
      className={cn(
        "group transition-all duration-300 bg-white dark:bg-slate-900 border border-slate-200/60 dark:border-slate-800/60 rounded-xl shadow-sm hover:shadow-md",
        className
      )}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-xs font-medium text-slate-500 dark:text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300 transition-colors">
          {title}
        </CardTitle>
        <div className="p-2 rounded-lg bg-blue-600/90 shadow-sm group-hover:shadow-md">
          <Icon className="h-4 w-4 text-white" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-[28px] leading-7 font-bold text-slate-900 dark:text-white mb-2">
          {formatValue(value)}
        </div>
        {change && (
          <div className="flex items-center space-x-1">
            {change.value > 0 && (
              <TrendingUp className="h-3 w-3 text-green-600" />
            )}
            {change.value < 0 && (
              <TrendingDown className="h-3 w-3 text-red-600" />
            )}
            <p className="text-xs text-slate-500 dark:text-slate-400">
              <span
                className={cn("font-semibold", getChangeColor(change.value))}
              >
                {change.value > 0 && "+"}
                {change.value}%
              </span>{" "}
              <span className="text-slate-400 dark:text-slate-500">
                {change.label}
              </span>
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
