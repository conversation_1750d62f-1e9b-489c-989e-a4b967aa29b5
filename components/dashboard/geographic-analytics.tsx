"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Globe,
  MapPin,
  Building2,
  Users,
  RefreshCw,
  MoreHorizontal,
} from "lucide-react";
// Remove direct Tinybird imports - we'll use API routes instead
import type {
  GeographicAnalyticsProps,
  GeographicTab,
  ProcessedGeographicData,
  GeographicLoadingState,
} from "@/lib/types/geographic";
import { formatVisitorCount } from "@/lib/utils/countries";
import { cn } from "@/lib/utils";
import GeographicTable from "./geographic-table";
import WorldMap from "./world-map";

export default function GeographicAnalytics({
  websiteId,
  dateRange,
  className,
}: GeographicAnalyticsProps) {
  const [activeTab, setActiveTab] = useState<GeographicTab>("map");
  const [data, setData] = useState<ProcessedGeographicData | null>(null);
  const [loading, setLoading] = useState<GeographicLoadingState>({
    overview: true,
    countries: true,
    regions: true,
    cities: true,
    realtime: false,
  });
  const [error, setError] = useState<string | null>(null);

  // Fetch geographic data from API route
  const fetchData = async () => {
    if (!websiteId || !dateRange.from || !dateRange.to) {
      return;
    }

    try {
      setError(null);
      setLoading({
        overview: true,
        countries: true,
        regions: true,
        cities: true,
        realtime: false,
      });

      // Call the API route instead of direct Tinybird functions
      const url = `/api/analytics/${websiteId}/geographic?start_date=${dateRange.from.toISOString()}&end_date=${dateRange.to.toISOString()}`;

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      setData({
        countries: data.countries,
        regions: data.regions,
        cities: data.cities,
        mapData: data.mapData,
        overview: data.overview,
      });
    } catch (err) {
      console.error("Failed to fetch geographic data:", err);
      setError("Failed to load geographic data. Please try again.");
    } finally {
      setLoading({
        overview: false,
        countries: false,
        regions: false,
        cities: false,
        realtime: false,
      });
    }
  };

  useEffect(() => {
    if (websiteId && dateRange.from && dateRange.to) {
      fetchData();
    }
  }, [websiteId, dateRange.from, dateRange.to]);

  const isLoading = Object.values(loading).some(Boolean);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <div className="flex items-center space-x-2">
          <Globe className="h-4 w-4 text-blue-600" />
          <CardTitle className="text-base font-semibold">Locations</CardTitle>
        </div>
        <div className="flex items-center space-x-1">
          {data?.overview && (
            <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
              {data.overview.total_countries} Countries
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchData}
            disabled={isLoading}
            className="h-7 w-7 p-0"
          >
            <RefreshCw className={cn("h-3 w-3", isLoading && "animate-spin")} />
          </Button>
          <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
            <MoreHorizontal className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {error && (
          <div className="px-4 pt-4">
            <div className="rounded-md bg-red-50 p-2 text-sm text-red-800">
              {error}
            </div>
          </div>
        )}

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as GeographicTab)}
        >
          <div className="px-4 mb-4">
            <TabsList className="grid w-full grid-cols-4 h-9">
              <TabsTrigger
                value="map"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <Globe className="h-3 w-3 flex-shrink-0" />
                <span className="hidden sm:inline truncate">Map</span>
              </TabsTrigger>
              <TabsTrigger
                value="countries"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <MapPin className="h-3 w-3 flex-shrink-0" />
                <span className="hidden sm:inline truncate">Countries</span>
              </TabsTrigger>
              <TabsTrigger
                value="regions"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <Building2 className="h-3 w-3 flex-shrink-0" />
                <span className="hidden sm:inline truncate">Regions</span>
              </TabsTrigger>
              <TabsTrigger
                value="cities"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <Users className="h-3 w-3 flex-shrink-0" />
                <span className="hidden sm:inline truncate">Cities</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="px-4 pb-4">
            <TabsContent value="map" className="mt-0">
              <div className="space-y-4">
                {/* Overview Stats */}
                {data?.overview && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {data.overview.total_countries}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Countries
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {data.overview.total_regions}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Regions
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {data.overview.total_cities}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Cities
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {formatVisitorCount(
                          data.overview.total_unique_visitors
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Visitors
                      </div>
                    </div>
                  </div>
                )}

                {/* Interactive World Map */}
                {data?.mapData && data.mapData.length > 0 ? (
                  <WorldMap
                    data={data.mapData}
                    onCountryClick={(countryCode) => {
                      console.log("Country clicked:", countryCode);
                      // Could implement country filtering here
                    }}
                    onCountryHover={(countryCode) => {
                      console.log("Country hovered:", countryCode);
                    }}
                  />
                ) : (
                  <div className="h-96 bg-slate-50 rounded-lg flex items-center justify-center border-2 border-dashed border-slate-200">
                    <div className="text-center space-y-2">
                      <Globe className="h-12 w-12 text-slate-400 mx-auto" />
                      <p className="text-slate-500 font-medium">
                        No geographic data available
                      </p>
                      <p className="text-sm text-slate-400">
                        Data will appear here once visitors access your site
                      </p>
                    </div>
                  </div>
                )}

                {/* Top Countries Preview */}
                {data?.countries && data.countries.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm text-muted-foreground">
                      Top Countries
                    </h4>
                    <div className="space-y-2">
                      {data.countries.slice(0, 5).map((country) => (
                        <div
                          key={country.country}
                          className="flex items-center justify-between py-2 px-3 bg-slate-50 rounded-md"
                        >
                          <div className="flex items-center space-x-3">
                            <span className="text-lg">{country.flagEmoji}</span>
                            <span className="font-medium">
                              {country.countryName}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold">
                              {formatVisitorCount(country.unique_visitors)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Visitors
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="countries" className="mt-4">
              {data?.countries ? (
                <GeographicTable
                  data={data.countries}
                  type="countries"
                  loading={loading.countries}
                />
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {loading.countries
                    ? "Loading countries..."
                    : "No country data available"}
                </div>
              )}
            </TabsContent>

            <TabsContent value="regions" className="mt-4">
              {data?.regions ? (
                <GeographicTable
                  data={data.regions}
                  type="regions"
                  loading={loading.regions}
                />
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {loading.regions
                    ? "Loading regions..."
                    : "No region data available"}
                </div>
              )}
            </TabsContent>

            <TabsContent value="cities" className="mt-4">
              {data?.cities ? (
                <GeographicTable
                  data={data.cities}
                  type="cities"
                  loading={loading.cities}
                />
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {loading.cities
                    ? "Loading cities..."
                    : "No city data available"}
                </div>
              )}
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
}
