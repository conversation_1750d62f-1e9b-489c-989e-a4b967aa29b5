"use client";

import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";
import type {
  CountryTableItem,
  RegionTableItem,
  CityTableItem,
} from "@/lib/types/geographic";
import {
  formatVisitorCount,
  formatRevenue,
  formatPercentage,
} from "@/lib/utils/countries";
import { cn } from "@/lib/utils";

interface GeographicTableProps {
  data: CountryTableItem[] | RegionTableItem[] | CityTableItem[];
  type: "countries" | "regions" | "cities";
  loading?: boolean;
  className?: string;
}

export default function GeographicTable({
  data,
  type,
  loading = false,
  className,
}: GeographicTableProps) {
  if (loading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div
            key={i}
            className="flex items-center justify-between p-3 bg-slate-50 rounded-md animate-pulse"
          >
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-slate-200 rounded"></div>
              <div className="w-32 h-4 bg-slate-200 rounded"></div>
            </div>
            <div className="w-16 h-4 bg-slate-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No {type} data available
      </div>
    );
  }

  const getLocationName = (
    item: CountryTableItem | RegionTableItem | CityTableItem
  ) => {
    if (type === "countries") {
      return (item as CountryTableItem).countryName;
    }
    if (type === "regions") {
      const region = item as RegionTableItem;
      return region.region;
    }
    if (type === "cities") {
      const city = item as CityTableItem;
      return city.city;
    }
    return "";
  };

  const getSubLocation = (
    item: CountryTableItem | RegionTableItem | CityTableItem
  ) => {
    if (type === "regions") {
      const region = item as RegionTableItem;
      return region.countryName;
    }
    if (type === "cities") {
      const city = item as CityTableItem;
      return `${city.region}, ${city.countryName}`;
    }
    return null;
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h3 className="font-medium text-sm text-muted-foreground">
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </h3>
          <Badge variant="secondary" className="text-xs">
            {data.length}
          </Badge>
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </div>

      {/* Table Header */}
      <div className="grid grid-cols-12 gap-4 px-3 py-2 text-xs font-medium text-muted-foreground border-b">
        <div className="col-span-5">
          {type === "countries"
            ? "Country"
            : type === "regions"
              ? "Region"
              : "City"}
        </div>
        <div className="col-span-2 text-right">Visitors</div>
        <div className="col-span-2 text-right">Sessions</div>
        <div className="col-span-2 text-right">Revenue</div>
        <div className="col-span-1 text-right">%</div>
      </div>

      {/* Table Content */}
      <ScrollArea className="h-80">
        <div className="space-y-0.5">
          {data.map((item, index) => (
            <div
              key={`${item.country}-${index}`}
              className="grid grid-cols-12 gap-3 px-2 py-2 hover:bg-slate-50 rounded-md transition-colors cursor-pointer group"
            >
              {/* Location */}
              <div className="col-span-5 flex items-center space-x-3 min-w-0">
                <span className="text-lg flex-shrink-0">{item.flagEmoji}</span>
                <div className="min-w-0 flex-1">
                  <div className="font-medium text-sm truncate">
                    {getLocationName(item)}
                  </div>
                  {getSubLocation(item) && (
                    <div className="text-xs text-muted-foreground truncate">
                      {getSubLocation(item)}
                    </div>
                  )}
                </div>
              </div>

              {/* Visitors */}
              <div className="col-span-2 text-right">
                <div className="font-semibold text-sm">
                  {formatVisitorCount(item.unique_visitors)}
                </div>
                <div className="text-xs text-muted-foreground">visitors</div>
              </div>

              {/* Sessions */}
              <div className="col-span-2 text-right">
                <div className="font-medium text-sm">
                  {formatVisitorCount(item.sessions)}
                </div>
                <div className="text-xs text-muted-foreground">sessions</div>
              </div>

              {/* Revenue */}
              <div className="col-span-2 text-right">
                <div className="font-medium text-sm">
                  {formatRevenue(item.total_revenue)}
                </div>
                <div className="text-xs text-muted-foreground">revenue</div>
              </div>

              {/* Percentage with Bar */}
              <div className="col-span-1 text-right">
                <div className="flex flex-col items-end space-y-1">
                  <span className="text-xs font-medium">
                    {formatPercentage(item.percentage)}
                  </span>
                  <div className="w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-500 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(item.percentage, 100)}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="flex items-center justify-center pt-3 border-t">
        <Button
          variant="ghost"
          size="sm"
          className="text-xs text-muted-foreground h-7"
        >
          <MoreHorizontal className="h-3 w-3 mr-1" />
          DETAILS
        </Button>
      </div>
    </div>
  );
}
