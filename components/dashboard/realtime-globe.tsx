"use client";

import { useRef, useMemo, Suspense } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Globe, Users, MapPin, Activity } from "lucide-react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Canvas, useFrame } from "@react-three/fiber";
import { OrbitControls, Sphere, Text } from "@react-three/drei";
import * as THREE from "three";

interface RealtimeGlobeProps {
  websiteId?: string; // This should be the trackingId for presence data
  className?: string;
}

interface GlobePoint {
  id: string;
  lat: number;
  lng: number;
  country: string;
  city: string;
  visitorCount: number;
  color: string;
  size: number;
  timestamp: string;
}

// Three.js Globe Component
function ThreeGlobe({ points }: { points: GlobePoint[] }) {
  const globeRef = useRef<THREE.Mesh>(null);

  useFrame(() => {
    if (globeRef.current) {
      globeRef.current.rotation.y += 0.005;
    }
  });

  return (
    <group>
      {/* Earth Sphere */}
      <Sphere ref={globeRef} args={[2, 64, 64]} position={[0, 0, 0]}>
        <meshStandardMaterial
          color="#1e40af"
          roughness={0.8}
          metalness={0.1}
          transparent
          opacity={0.9}
        />
      </Sphere>

      {/* Atmosphere */}
      <Sphere args={[2.1, 64, 64]} position={[0, 0, 0]}>
        <meshBasicMaterial
          color="#3b82f6"
          transparent
          opacity={0.1}
          side={THREE.BackSide}
        />
      </Sphere>

      {/* User Points */}
      {points.map((point, index) => {
        // Convert lat/lng to 3D coordinates
        const phi = (90 - point.lat) * (Math.PI / 180);
        const theta = (point.lng + 180) * (Math.PI / 180);
        const radius = 2.05;

        const x = -(radius * Math.sin(phi) * Math.cos(theta));
        const z = radius * Math.sin(phi) * Math.sin(theta);
        const y = radius * Math.cos(phi);

        return (
          <group key={point.id}>
            {/* Point Marker */}
            <Sphere args={[0.02 * point.size, 8, 8]} position={[x, y, z]}>
              <meshBasicMaterial color={point.color} />
            </Sphere>

            {/* Label for countries with multiple visitors */}
            {point.visitorCount >= 2 && (
              <Text
                position={[x * 1.1, y * 1.1, z * 1.1]}
                fontSize={0.08}
                color="white"
                anchorX="center"
                anchorY="middle"
              >
                {`${point.country}: ${point.visitorCount}`}
              </Text>
            )}
          </group>
        );
      })}
    </group>
  );
}

// Country coordinates mapping (major countries)
const COUNTRY_COORDINATES: Record<string, { lat: number; lng: number }> = {
  "United States": { lat: 39.8283, lng: -98.5795 },
  China: { lat: 35.8617, lng: 104.1954 },
  Japan: { lat: 36.2048, lng: 138.2529 },
  Germany: { lat: 51.1657, lng: 10.4515 },
  "United Kingdom": { lat: 55.3781, lng: -3.436 },
  France: { lat: 46.6034, lng: 1.8883 },
  Canada: { lat: 56.1304, lng: -106.3468 },
  Australia: { lat: -25.2744, lng: 133.7751 },
  Brazil: { lat: -14.235, lng: -51.9253 },
  India: { lat: 20.5937, lng: 78.9629 },
  Russia: { lat: 61.524, lng: 105.3188 },
  "South Korea": { lat: 35.9078, lng: 127.7669 },
  Italy: { lat: 41.8719, lng: 12.5674 },
  Spain: { lat: 40.4637, lng: -3.7492 },
  Netherlands: { lat: 52.1326, lng: 5.2913 },
  Sweden: { lat: 60.1282, lng: 18.6435 },
  Norway: { lat: 60.472, lng: 8.4689 },
  Denmark: { lat: 56.2639, lng: 9.5018 },
  Finland: { lat: 61.9241, lng: 25.7482 },
  Switzerland: { lat: 46.8182, lng: 8.2275 },
  Austria: { lat: 47.5162, lng: 14.5501 },
  Belgium: { lat: 50.5039, lng: 4.4699 },
  Poland: { lat: 51.9194, lng: 19.1451 },
  "Czech Republic": { lat: 49.8175, lng: 15.473 },
  Hungary: { lat: 47.1625, lng: 19.5033 },
  Portugal: { lat: 39.3999, lng: -8.2245 },
  Greece: { lat: 39.0742, lng: 21.8243 },
  Turkey: { lat: 38.9637, lng: 35.2433 },
  Mexico: { lat: 23.6345, lng: -102.5528 },
  Argentina: { lat: -38.4161, lng: -63.6167 },
  Chile: { lat: -35.6751, lng: -71.543 },
  Colombia: { lat: 4.5709, lng: -74.2973 },
  Peru: { lat: -9.19, lng: -75.0152 },
  Venezuela: { lat: 6.4238, lng: -66.5897 },
  "South Africa": { lat: -30.5595, lng: 22.9375 },
  Egypt: { lat: 26.0975, lng: 30.0444 },
  Nigeria: { lat: 9.082, lng: 8.6753 },
  Kenya: { lat: -0.0236, lng: 37.9062 },
  Morocco: { lat: 31.7917, lng: -7.0926 },
  Israel: { lat: 31.0461, lng: 34.8516 },
  "Saudi Arabia": { lat: 23.8859, lng: 45.0792 },
  UAE: { lat: 23.4241, lng: 53.8478 },
  Thailand: { lat: 15.87, lng: 100.9925 },
  Vietnam: { lat: 14.0583, lng: 108.2772 },
  Singapore: { lat: 1.3521, lng: 103.8198 },
  Malaysia: { lat: 4.2105, lng: 101.9758 },
  Indonesia: { lat: -0.7893, lng: 113.9213 },
  Philippines: { lat: 12.8797, lng: 121.774 },
  "New Zealand": { lat: -40.9006, lng: 174.886 },
};

export default function RealtimeGlobe({
  websiteId,
  className,
}: RealtimeGlobeProps) {
  // Get real-time presence data
  const presenceData = useQuery(
    api.presence.listWithDetailsByRoomId,
    websiteId ? { roomId: websiteId } : "skip"
  );

  // Process presence data into globe points
  const globePoints: GlobePoint[] = useMemo(() => {
    if (!presenceData || !Array.isArray(presenceData)) return [];

    const now = Date.now();
    const fiveMinutesAgo = now - 5 * 60 * 1000;

    // Group visitors by country
    const countryGroups = new Map<string, any[]>();

    presenceData
      .filter((item: any) => {
        if (!item.online) return false;
        const lastUpdate = item.details?.lastUpdate || item.updated || 0;
        return lastUpdate > fiveMinutesAgo && item.details?.country;
      })
      .forEach((item: any) => {
        const country = item.details.country;
        if (!countryGroups.has(country)) {
          countryGroups.set(country, []);
        }
        countryGroups.get(country)!.push(item);
      });

    // Convert to globe points
    return Array.from(countryGroups.entries()).map(([country, visitors]) => {
      const coords = COUNTRY_COORDINATES[country] || { lat: 0, lng: 0 };
      const visitorCount = visitors.length;

      // Color based on visitor count
      let color = "#3b82f6"; // blue
      if (visitorCount >= 5)
        color = "#ef4444"; // red
      else if (visitorCount >= 3)
        color = "#f97316"; // orange
      else if (visitorCount >= 2) color = "#eab308"; // yellow

      // Size based on visitor count
      const size = Math.min(0.8 + visitorCount * 0.2, 2.0);

      return {
        id: `${country}-${Date.now()}`,
        lat: coords.lat,
        lng: coords.lng,
        country,
        city: visitors[0]?.details?.city || "",
        visitorCount,
        color,
        size,
        timestamp: new Date().toISOString(),
      };
    });
  }, [presenceData]);

  const totalVisitors = globePoints.reduce(
    (sum, point) => sum + point.visitorCount,
    0
  );

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>Real-time Globe</span>
          </div>
          <div className="flex items-center space-x-4">
            <Badge variant="secondary" className="flex items-center space-x-1">
              <Users className="h-3 w-3" />
              <span>{totalVisitors} online</span>
            </Badge>
            <Badge variant="outline" className="flex items-center space-x-1">
              <MapPin className="h-3 w-3" />
              <span>{globePoints.length} countries</span>
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative h-[400px] w-full">
          <Canvas
            camera={{ position: [0, 0, 5], fov: 60 }}
            style={{
              background: "linear-gradient(to bottom, #0f172a, #1e293b)",
            }}
          >
            <Suspense fallback={null}>
              <ambientLight intensity={0.4} />
              <pointLight position={[10, 10, 10]} intensity={1} />
              <ThreeGlobe points={globePoints} />
              <OrbitControls
                enablePan={false}
                enableZoom={true}
                enableRotate={true}
                zoomSpeed={0.6}
                rotateSpeed={0.5}
                minDistance={3}
                maxDistance={8}
                autoRotate={true}
                autoRotateSpeed={0.5}
              />
            </Suspense>
          </Canvas>
        </div>

        {/* Legend */}
        <div className="mt-4 flex flex-wrap gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span>1 visitor</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <span>2-3 visitors</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-orange-500"></div>
            <span>3-4 visitors</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span>5+ visitors</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
