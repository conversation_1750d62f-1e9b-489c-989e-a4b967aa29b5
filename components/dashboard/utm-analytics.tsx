"use client";

import { useState, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useEnhancedTrafficSources } from "@/lib/hooks/useEnhancedTrafficSources";
import { ChevronDown, Loader2, TrendingUp, ExternalLink } from "lucide-react";

interface UTMAnalyticsProps {
  websiteId: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  className?: string;
}

type BaseCategory = "channels" | "sources";
type UTMCategory =
  | "utm_sources"
  | "utm_mediums"
  | "utm_campaigns"
  | "utm_contents"
  | "utm_terms";

const utmCategoryLabels: Record<UTMCategory, string> = {
  utm_sources: "UTM Sources",
  utm_mediums: "UTM Mediums",
  utm_campaigns: "UTM Campaigns",
  utm_contents: "UTM Contents",
  utm_terms: "UTM Terms",
};

const utmCategorySubtitles: Record<UTMCategory, string> = {
  utm_sources: "Source",
  utm_mediums: "Medium",
  utm_campaigns: "Campaign",
  utm_contents: "Content",
  utm_terms: "Term",
};

export default function UTMAnalytics({
  websiteId,
  dateRange,
  className,
}: UTMAnalyticsProps) {
  const [activeTab, setActiveTab] = useState<BaseCategory | "utm">("channels");
  const [activeUTMCategory, setActiveUTMCategory] =
    useState<UTMCategory>("utm_sources");
  const [showAll, setShowAll] = useState(false);

  // Determine which category to fetch data for
  const currentCategory =
    activeTab === "utm"
      ? activeUTMCategory === "utm_campaigns"
        ? "campaigns"
        : activeUTMCategory
      : activeTab;

  // Fetch data for current category
  const { data, loading } = useEnhancedTrafficSources({
    websiteId,
    dateRange,
    category: currentCategory,
    limit: 20,
  });

  const displayData = showAll ? data : data.slice(0, 10);

  // Calculate max visitors for progress bar scaling
  const maxVisitors = Math.max(...data.map((item) => item.visitors), 1);

  // Render item with progress bar
  const renderSourceItem = (item: any, index: number) => (
    <div key={`${item.name}-${index}`} className="group relative mb-2">
      {/* Progress bar background */}
      <div className="absolute inset-0 bg-muted/10 rounded-lg">
        <div
          className="h-full bg-gradient-to-r from-blue-500/15 to-blue-600/15 rounded-lg transition-all duration-300"
          style={{ width: `${(item.visitors / maxVisitors) * 100}%` }}
        />
      </div>

      {/* Content */}
      <div className="relative flex items-center justify-between py-4 px-4 rounded-lg hover:bg-accent/20 transition-colors">
        <div className="flex-1 min-w-0">
          <p className="font-medium text-sm text-foreground truncate">
            {item.name}
          </p>
        </div>
        <div className="text-right flex-shrink-0">
          <p className="font-bold text-lg text-foreground">
            {item.visitors.toLocaleString()}
          </p>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Top Sources</span>
          </CardTitle>
          <CardDescription>Loading traffic data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>
                {activeTab === "utm"
                  ? utmCategoryLabels[activeUTMCategory]
                  : activeTab === "channels"
                    ? "Channels"
                    : "Sources"}
              </span>
            </CardTitle>
            <CardDescription>
              Traffic breakdown by{" "}
              {activeTab === "utm"
                ? utmCategorySubtitles[activeUTMCategory].toLowerCase()
                : activeTab.slice(0, -1).toLowerCase()}
            </CardDescription>
          </div>

          {/* Tab navigation */}
          <div className="flex items-center space-x-2">
            <Button
              variant={activeTab === "channels" ? "default" : "ghost"}
              size="sm"
              onClick={() => setActiveTab("channels")}
              className="text-sm"
            >
              Channels
            </Button>
            <Button
              variant={activeTab === "sources" ? "default" : "ghost"}
              size="sm"
              onClick={() => setActiveTab("sources")}
              className="text-sm"
            >
              Sources
            </Button>

            {/* UTM Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant={activeTab === "utm" ? "default" : "outline"}
                  size="sm"
                  className="min-w-[140px]"
                >
                  {activeTab === "utm"
                    ? utmCategoryLabels[activeUTMCategory]
                    : "UTM Sources"}
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[180px]">
                {Object.entries(utmCategoryLabels).map(([key, label]) => (
                  <DropdownMenuItem
                    key={key}
                    onClick={() => {
                      setActiveTab("utm");
                      setActiveUTMCategory(key as UTMCategory);
                    }}
                    className={
                      activeTab === "utm" && activeUTMCategory === key
                        ? "bg-accent"
                        : ""
                    }
                  >
                    {label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
            <span>
              {activeTab === "utm"
                ? utmCategorySubtitles[activeUTMCategory]
                : activeTab === "channels"
                  ? "Channel"
                  : "Source"}
            </span>
            <span>Visitors</span>
          </div>

          {displayData.length === 0 ? (
            <div className="text-center py-12 text-muted-foreground">
              <div className="flex flex-col items-center space-y-3">
                <div className="p-3 bg-muted/20 rounded-full">
                  <TrendingUp className="h-8 w-8" />
                </div>
                <div className="space-y-1">
                  <p className="font-medium text-foreground">
                    No{" "}
                    {activeTab === "utm"
                      ? utmCategorySubtitles[activeUTMCategory].toLowerCase()
                      : activeTab.slice(0, -1).toLowerCase()}{" "}
                    data available
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {activeTab === "utm"
                      ? `UTM ${utmCategorySubtitles[activeUTMCategory].toLowerCase()} data will appear here once visitors arrive with UTM parameters.`
                      : `${activeTab === "channels" ? "Channel" : "Source"} data will appear here once you have website traffic.`}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            displayData.map((item, index) => renderSourceItem(item, index))
          )}
        </div>

        {data.length > 10 && (
          <div className="mt-6 text-center">
            <Button
              variant="ghost"
              onClick={() => setShowAll(!showAll)}
              className="w-full text-muted-foreground hover:text-foreground"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              DETAILS
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
