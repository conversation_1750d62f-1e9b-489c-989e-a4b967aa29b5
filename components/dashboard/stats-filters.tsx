"use client";

import DateRangePicker, {
  type DateRange,
} from "@/components/dashboard/date-range-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { useCallback } from "react";

export type StatsMetric = "visitors" | "pageviews" | "revenue";
export type StatsInterval = "hour" | "day" | "week" | "month";

export interface StatsFiltersState {
  dateRange: DateRange;
  metric: StatsMetric;
  interval: StatsInterval;
}

interface StatsFiltersProps {
  value: StatsFiltersState;
  onChange: (next: StatsFiltersState) => void;
  className?: string;
}

export default function StatsFilters({
  value,
  onChange,
  className,
}: StatsFiltersProps) {
  const onDateChange = useCallback(
    (range: DateRange) => {
      onChange({ ...value, dateRange: range });
    },
    [onChange, value]
  );

  const onMetricChange = useCallback(
    (metric: StatsMetric) => {
      onChange({ ...value, metric });
    },
    [onChange, value]
  );

  const onIntervalChange = useCallback(
    (interval: StatsInterval) => {
      onChange({ ...value, interval });
    },
    [onChange, value]
  );

  return (
    <div className={cn("flex flex-wrap items-center gap-3", className)}>
      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">Metric</span>
        <Select value={value.metric} onValueChange={onMetricChange as any}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="visitors">Visitors</SelectItem>
            <SelectItem value="pageviews">Pageviews</SelectItem>
            <SelectItem value="revenue">Revenue</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center gap-2">
        <span className="text-sm text-muted-foreground">Interval</span>
        <Select value={value.interval} onValueChange={onIntervalChange as any}>
          <SelectTrigger className="w-[160px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="hour">Hourly</SelectItem>
            <SelectItem value="day">Daily</SelectItem>
            <SelectItem value="week">Weekly</SelectItem>
            <SelectItem value="month">Monthly</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <DateRangePicker value={value.dateRange} onChange={onDateChange} />
    </div>
  );
}
