"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

import { MoreHorizontal, TrendingUp, Users } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  formatVisitorCount,
  formatDevicePercentage,
  formatRevenue,
} from "@/lib/utils/device";
import type {
  DeviceTableProps,
  DeviceTableItem,
  BrowserTableItem,
  OSTableItem,
  DeviceTabContent,
} from "@/lib/types/device";

interface DeviceTableComponentProps
  extends DeviceTableProps<DeviceTabContent[0]> {
  type: "device" | "browser" | "os";
}

export default function DeviceTable({
  data,
  loading = false,
  error = null,
  onRowClick,
  className,
  type,
}: DeviceTableComponentProps) {
  const [sortBy, setSortBy] = useState<"visitors" | "percentage" | "revenue">(
    "visitors"
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Sort data based on current sort settings
  const sortedData = [...data].sort((a, b) => {
    let aValue: number, bValue: number;

    switch (sortBy) {
      case "visitors":
        aValue = a.visitors;
        bValue = b.visitors;
        break;
      case "percentage":
        aValue = a.percentage;
        bValue = b.percentage;
        break;
      case "revenue":
        aValue = a.total_revenue;
        bValue = b.total_revenue;
        break;
      default:
        aValue = a.visitors;
        bValue = b.visitors;
    }

    return sortOrder === "desc" ? bValue - aValue : aValue - bValue;
  });

  const handleSort = (column: "visitors" | "percentage" | "revenue") => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "desc" ? "asc" : "desc");
    } else {
      setSortBy(column);
      setSortOrder("desc");
    }
  };

  const getDisplayName = (item: any): string => {
    return (
      item.displayName || item.category || item.browser || item.os || "Unknown"
    );
  };

  const getIcon = (item: any): string => {
    return item.icon || "❓";
  };

  const getSubtitle = (item: any): string => {
    switch (type) {
      case "device":
        return `${formatVisitorCount(item.visitors)} visitors`;
      case "browser":
        return `${formatVisitorCount(item.visitors)} visitors`;
      case "os":
        return `${formatVisitorCount(item.visitors)} visitors`;
      default:
        return `${formatVisitorCount(item.visitors)} visitors`;
    }
  };

  if (loading) {
    return (
      <div className={cn("space-y-3", className)}>
        {Array.from({ length: 5 }).map((_, i) => (
          <div
            key={i}
            className="flex items-center justify-between p-3 rounded-lg border"
          >
            <div className="flex items-center space-x-3">
              <div className="h-8 w-8 bg-slate-200 rounded animate-pulse" />
              <div className="space-y-1">
                <div className="h-4 w-24 bg-slate-200 rounded animate-pulse" />
                <div className="h-3 w-16 bg-slate-200 rounded animate-pulse" />
              </div>
            </div>
            <div className="text-right space-y-1">
              <div className="h-4 w-12 bg-slate-200 rounded animate-pulse" />
              <div className="h-3 w-8 bg-slate-200 rounded animate-pulse" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("text-center py-8 text-muted-foreground", className)}>
        <p>
          Error loading {type} data: {error}
        </p>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className={cn("text-center py-8 text-muted-foreground", className)}>
        <p>No {type} data available yet</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Table Header with Sort Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-muted-foreground">
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </span>
          <Badge variant="secondary" className="text-xs">
            {data.length} {data.length === 1 ? "item" : "items"}
          </Badge>
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant={sortBy === "visitors" ? "default" : "ghost"}
            size="sm"
            onClick={() => handleSort("visitors")}
            className="text-xs"
          >
            <Users className="h-3 w-3 mr-1" />
            Visitors
          </Button>
          <Button
            variant={sortBy === "percentage" ? "default" : "ghost"}
            size="sm"
            onClick={() => handleSort("percentage")}
            className="text-xs"
          >
            <TrendingUp className="h-3 w-3 mr-1" />%
          </Button>
        </div>
      </div>

      {/* Table Content */}
      <div className="space-y-2">
        {sortedData.map((item, index) => (
          <div
            key={index}
            className={cn(
              "flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors",
              onRowClick && "cursor-pointer"
            )}
            onClick={() => onRowClick?.(item)}
          >
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <div className="text-lg flex-shrink-0">{getIcon(item)}</div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm truncate">
                  {getDisplayName(item)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {getSubtitle(item)}
                </p>
              </div>
            </div>
            <div className="text-right flex-shrink-0 space-y-1">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-sm">
                  {formatVisitorCount(item.visitors)}
                </span>
                <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                  {formatDevicePercentage(item.percentage)}
                </Badge>
              </div>
              {item.total_revenue > 0 && (
                <p className="text-xs text-muted-foreground">
                  {formatRevenue(item.total_revenue)}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Show More Button */}
      {data.length > 10 && (
        <div className="text-center pt-2">
          <Button variant="ghost" size="sm" className="text-xs">
            <MoreHorizontal className="h-3 w-3 mr-1" />
            Show All {type.charAt(0).toUpperCase() + type.slice(1)}s
          </Button>
        </div>
      )}
    </div>
  );
}
