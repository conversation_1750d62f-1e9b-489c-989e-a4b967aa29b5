"use client";

import { PaymentProviderSetup } from "./payment-provider-setup";

interface PaymentProviderSetupWrapperProps {
  websiteId: string;
  websiteName?: string;
}

/**
 * Client Component wrapper for PaymentProviderSetup
 * This wrapper handles the event handlers that cannot be passed from Server Components
 */
export function PaymentProviderSetupWrapper({
  websiteId,
  websiteName,
}: PaymentProviderSetupWrapperProps) {
  const handleProviderConfigured = (provider: string) => {
    console.log(
      `${provider} configured for website: ${websiteName || websiteId}`
    );
  };

  return (
    <PaymentProviderSetup
      websiteId={websiteId}
      onProviderConfigured={handleProviderConfigured}
    />
  );
}
