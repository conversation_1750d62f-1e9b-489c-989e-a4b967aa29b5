"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useRealtimeData } from "@/lib/hooks/useRealtimeData";
import { getRelativeTimeString } from "@/lib/utils/date";
import { Activity, MousePointer, RefreshCw, Users } from "lucide-react";
import { useEffect, useState } from "react";

interface RealTimeActivityProps {
  websiteId: string;
  className?: string;
}

interface ActivityEvent {
  id: string;
  type: "pageview" | "event" | "visitor_join" | "visitor_leave" | "conversion";
  title: string;
  description: string;
  timestamp: Date;
  icon: React.ReactNode;
  badgeVariant?: "default" | "secondary" | "destructive" | "outline";
}

// Minimal typing for incoming realtime events
type RealTimeEvent = {
  type: "pageview" | "event" | "visitor_join" | "visitor_leave";
  timestamp: string;
  data?: {
    url?: string;
    eventName?: string;
    country?: string;
    duration?: number | string;
  };
};

export default function RealTimeActivity({
  websiteId,
  className,
}: RealTimeActivityProps) {
  const { data, isConnected, error } = useRealtimeData({ websiteId });
  const [activities, setActivities] = useState<ActivityEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Convert SSE realtime data to activity entries (lightweight demo mapping)
  useEffect(() => {
    if (!data) return;
    setIsLoading(false);
    const now = new Date();
    const mapped: ActivityEvent[] = (data.realtimeVisitors || [])
      .slice(0, 10)
      .map((v) => ({
        id: `visitor-${v.visitor_id}-${v.timestamp}`,
        type: "pageview",
        title: "New page view",
        description: v.url || "",
        timestamp: new Date(v.timestamp || now.toISOString()),
        icon: <MousePointer className="h-4 w-4" />,
        badgeVariant: "default",
      }));
    setActivities(mapped);
  }, [data]);

  const processEvent = (event: RealTimeEvent): ActivityEvent | null => {
    const id = `${event.type}-${event.timestamp}-${Math.random()}`;
    const timestamp = new Date(event.timestamp);

    switch (event.type) {
      case "pageview":
        return {
          id,
          type: "pageview",
          title: "New page view",
          description: `User viewed ${event.data?.url || "a page"}`,
          timestamp,
          icon: <MousePointer className="h-4 w-4" />,
          badgeVariant: "default",
        };

      case "event":
        return {
          id,
          type: "event",
          title: `Custom event: ${event.data?.eventName || "Unknown"}`,
          description: event.data?.url || "Event triggered",
          timestamp,
          icon: <Activity className="h-4 w-4" />,
          badgeVariant: "secondary",
        };

      case "visitor_join":
        return {
          id,
          type: "visitor_join",
          title: "New visitor",
          description: `Visitor from ${event.data?.country || "Unknown"} joined`,
          timestamp,
          icon: <Users className="h-4 w-4" />,
          badgeVariant: "outline",
        };

      case "visitor_leave":
        return {
          id,
          type: "visitor_leave",
          title: "Visitor left",
          description: `Session ended after ${event.data?.duration || "unknown"} time`,
          timestamp,
          icon: <Users className="h-4 w-4" />,
          badgeVariant: "secondary",
        };

      default:
        return null;
    }
  };

  const getStatusColor = () =>
    isConnected ? "text-green-500" : "text-red-500";

  const getStatusText = () => (isConnected ? "Live" : "Disconnected");

  // Simulate some demo events for testing
  // useEffect(() => {
  //   if (activities.length === 0 && !isLoading) {
  //     const demoEvents: ActivityEvent[] = [
  //       {
  //         id: "demo-1",
  //         type: "pageview",
  //         title: "New page view",
  //         description: "User viewed /pricing",
  //         timestamp: new Date(Date.now() - 30000),
  //         icon: <MousePointer className="h-4 w-4" />,
  //         badgeVariant: "default",
  //       },
  //       {
  //         id: "demo-2",
  //         type: "visitor_join",
  //         title: "New visitor",
  //         description: "Visitor from United States joined",
  //         timestamp: new Date(Date.now() - 120000),
  //         icon: <Users className="h-4 w-4" />,
  //         badgeVariant: "outline",
  //       },
  //       {
  //         id: "demo-3",
  //         type: "event",
  //         title: "Custom event: button_click",
  //         description: "User clicked signup button",
  //         timestamp: new Date(Date.now() - 180000),
  //         icon: <Activity className="h-4 w-4" />,
  //         badgeVariant: "secondary",
  //       },
  //     ];

  //     setActivities(demoEvents);
  //   }
  // }, [activities.length, isLoading]);

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Real-time Activity</span>
              <div className="flex items-center space-x-2">
                <div
                  className={`h-2 w-2 rounded-full ${isConnected ? "bg-green-500" : "bg-red-500"}`}
                />
                <span className={`text-sm font-medium ${getStatusColor()}`}>
                  {getStatusText()}
                </span>
              </div>
            </CardTitle>
            <CardDescription>Live events from your website</CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-2 text-muted-foreground">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span>Connecting to real-time feed...</span>
            </div>
          </div>
        ) : activities.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Activity className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-muted-foreground">No recent activity</p>
            <p className="text-sm text-muted-foreground">
              Events will appear here as they happen
            </p>
          </div>
        ) : (
          <ScrollArea className="h-[400px]">
            <div className="space-y-3">
              {activities.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-start space-x-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                >
                  <div className="flex-shrink-0 mt-0.5">{activity.icon}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <Badge
                        variant={activity.badgeVariant}
                        className="text-xs"
                      >
                        {activity.type}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground truncate">
                      {activity.description}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {getRelativeTimeString(activity.timestamp)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
