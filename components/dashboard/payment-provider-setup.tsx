"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
// Simple Alert components
const Alert = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => <div className={`rounded-lg border p-4 ${className}`}>{children}</div>;

const AlertDescription = ({ children }: { children: React.ReactNode }) => (
  <div className="text-sm">{children}</div>
);
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  CheckCircle,
  XCircle,
  Loader2,
  CreditCard,
  Webhook,
  Shield,
  ExternalLink,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";

interface PaymentProvider {
  id: string;
  name: string;
  description: string;
  icon: string;
  fields: {
    key: string;
    label: string;
    placeholder: string;
    type: "text" | "password";
    required: boolean;
    description?: string;
  }[];
  docsUrl: string;
  webhookDocsUrl: string;
}

interface ValidationResult {
  success: boolean;
  error?: string;
  details?: string;
  webhookUrl?: string;
  accountInfo?: any;
}

const PAYMENT_PROVIDERS: PaymentProvider[] = [
  {
    id: "stripe",
    name: "Stripe",
    description:
      "Accept payments with Stripe Checkout, Payment Intents, and Subscriptions",
    icon: "💳",
    fields: [
      {
        key: "secret_key",
        label: "Secret Key",
        placeholder: "sk_test_... or sk_live_...",
        type: "password",
        required: true,
        description: "Your Stripe secret key from the API keys section",
      },
    ],
    docsUrl: "https://stripe.com/docs/keys",
    webhookDocsUrl: "https://stripe.com/docs/webhooks",
  },
  {
    id: "lemonsqueezy",
    name: "LemonSqueezy",
    description: "Sell digital products with LemonSqueezy",
    icon: "🍋",
    fields: [
      {
        key: "api_key",
        label: "API Key",
        placeholder: "lmsq_api_...",
        type: "password",
        required: true,
        description: "Your LemonSqueezy API key",
      },
    ],
    docsUrl: "https://docs.lemonsqueezy.com/api",
    webhookDocsUrl: "https://docs.lemonsqueezy.com/api/webhooks",
  },
  {
    id: "polar",
    name: "Polar",
    description: "Monetize your open source projects with Polar",
    icon: "🐻‍❄️",
    fields: [
      {
        key: "access_token",
        label: "Access Token",
        placeholder: "polar_...",
        type: "password",
        required: true,
        description: "Your Polar access token",
      },
    ],
    docsUrl: "https://docs.polar.sh/api",
    webhookDocsUrl: "https://docs.polar.sh/webhooks",
  },
];

interface PaymentProviderSetupProps {
  websiteId?: string;
  onProviderConfigured?: (provider: string) => void;
}

interface ProviderStatus {
  provider: string;
  configured: boolean;
  level: "website" | "global" | "none";
}

export function PaymentProviderSetup({
  websiteId,
  onProviderConfigured,
}: PaymentProviderSetupProps) {
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [credentials, setCredentials] = useState<Record<string, string>>({});
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] =
    useState<ValidationResult | null>(null);
  const [showCredentials, setShowCredentials] = useState<
    Record<string, boolean>
  >({});
  const [providerStatuses, setProviderStatuses] = useState<ProviderStatus[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);

  const selectedProviderConfig = PAYMENT_PROVIDERS.find(
    (p) => p.id === selectedProvider
  );

  // Load provider statuses on component mount
  useEffect(() => {
    loadProviderStatuses();
  }, [websiteId]);

  const loadProviderStatuses = async () => {
    try {
      setIsLoading(true);
      const statuses: ProviderStatus[] = [];

      for (const provider of PAYMENT_PROVIDERS) {
        const response = await fetch(
          `/api/payment-providers/status?provider=${provider.id}&websiteId=${websiteId || ""}`
        );
        if (response.ok) {
          const data = await response.json();
          statuses.push({
            provider: provider.id,
            configured: data.configured,
            level: data.level,
          });
        } else {
          statuses.push({
            provider: provider.id,
            configured: false,
            level: "none",
          });
        }
      }

      setProviderStatuses(statuses);
    } catch (error) {
      console.error("Failed to load provider statuses:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getProviderStatus = (providerId: string): ProviderStatus => {
    return (
      providerStatuses.find((p) => p.provider === providerId) || {
        provider: providerId,
        configured: false,
        level: "none",
      }
    );
  };

  const handleProviderSelect = (providerId: string) => {
    setSelectedProvider(providerId);
    setCredentials({});
    setValidationResult(null);
  };

  const handleCredentialChange = (key: string, value: string) => {
    setCredentials((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const toggleCredentialVisibility = (key: string) => {
    setShowCredentials((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const handleSetup = async () => {
    if (!selectedProviderConfig) return;

    setIsValidating(true);
    setValidationResult(null);

    try {
      // the api/payment-providers api endpoint will perform the following:
      // 1. validate credentials with the payment provider
      // 2. store the credentials to the database
      // 3. setup webhook endpoints
      // 4. generate audit log
      // 5. return the following:
      // {
      //  success: true,
      //  provider,
      //  credentialsStored: storedCredentialIds.length,
      //  webhookUrl,
      //  accountInfo: validation.accountInfo,
      //  message: "Payment provider configured successfully",
      // }
      const response = await fetch("/api/payment-providers", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          provider: selectedProvider,
          credentials: Object.entries(credentials).map(([type, value]) => ({
            type,
            value,
          })),
          websiteId,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setValidationResult({
          success: true,
          webhookUrl: result.webhookUrl,
          accountInfo: result.accountInfo,
        });
        // Reload provider statuses to reflect the new configuration
        await loadProviderStatuses();
        onProviderConfigured?.(selectedProvider);
      } else {
        setValidationResult({
          success: false,
          error: result.error,
          details: result.details,
        });
      }
    } catch (error) {
      setValidationResult({
        success: false,
        error: "Setup failed",
        details: "Network error or server unavailable",
      });
    } finally {
      setIsValidating(false);
    }
  };

  const isFormValid = () => {
    if (!selectedProviderConfig) return false;

    return selectedProviderConfig.fields
      .filter((field) => field.required)
      .every((field) => credentials[field.key]?.trim());
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">
            Payment Provider Integration
          </h3>
          <p className="text-sm text-muted-foreground">
            Connect your payment providers to enable automatic revenue tracking
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-1">
          <Shield className="h-3 w-3" />
          End-to-end encrypted
        </Badge>
      </div>

      <Tabs value={selectedProvider} onValueChange={handleProviderSelect}>
        <TabsList className="grid w-full grid-cols-3">
          {PAYMENT_PROVIDERS.map((provider) => (
            <TabsTrigger
              key={provider.id}
              value={provider.id}
              className="flex items-center gap-2"
            >
              <span>{provider.icon}</span>
              {provider.name}
              {getProviderStatus(provider.id).configured && (
                <CheckCircle className="h-3 w-3 text-green-500" />
              )}
              {getProviderStatus(provider.id).level === "global" && (
                <Badge variant="outline" className="ml-1 text-xs">
                  Global
                </Badge>
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        {PAYMENT_PROVIDERS.map((provider) => (
          <TabsContent
            key={provider.id}
            value={provider.id}
            className="space-y-4"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span className="text-2xl">{provider.icon}</span>
                  Configure {provider.name}
                </CardTitle>
                <CardDescription>{provider.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {provider.fields.map((field) => (
                  <div key={field.key} className="space-y-2">
                    <Label
                      htmlFor={field.key}
                      className="flex items-center gap-2"
                    >
                      {field.label}
                      {field.required && (
                        <span className="text-red-500">*</span>
                      )}
                    </Label>
                    <div className="relative">
                      <Input
                        id={field.key}
                        type={showCredentials[field.key] ? "text" : field.type}
                        placeholder={field.placeholder}
                        value={credentials[field.key] || ""}
                        onChange={(e) =>
                          handleCredentialChange(field.key, e.target.value)
                        }
                        className="pr-10"
                      />
                      {field.type === "password" && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => toggleCredentialVisibility(field.key)}
                        >
                          {showCredentials[field.key] ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      )}
                    </div>
                    {field.description && (
                      <p className="text-xs text-muted-foreground">
                        {field.description}
                      </p>
                    )}
                  </div>
                ))}

                <div className="flex items-center gap-2 pt-4">
                  <Button
                    onClick={handleSetup}
                    disabled={!isFormValid() || isValidating}
                    className="flex items-center gap-2"
                  >
                    {isValidating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <CreditCard className="h-4 w-4" />
                    )}
                    {isValidating ? "Validating..." : "Setup Integration"}
                  </Button>

                  <Button variant="outline" size="sm" asChild>
                    <a
                      href={provider.docsUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      API Docs
                    </a>
                  </Button>

                  <Button variant="outline" size="sm" asChild>
                    <a
                      href={provider.webhookDocsUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Webhook className="h-3 w-3 mr-1" />
                      Webhook Docs
                    </a>
                  </Button>
                </div>

                {validationResult && (
                  <Alert
                    className={
                      validationResult.success
                        ? "border-green-200 bg-green-50"
                        : "border-red-200 bg-red-50"
                    }
                  >
                    <div className="flex items-start gap-2">
                      {validationResult.success ? (
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600 mt-0.5" />
                      )}
                      <div className="flex-1">
                        <AlertDescription>
                          {validationResult.success ? (
                            <div className="space-y-2">
                              <p className="font-medium text-green-800">
                                ✅ Integration successful!
                              </p>
                              {validationResult.webhookUrl && (
                                <div className="space-y-1">
                                  <p className="text-sm text-green-700">
                                    Webhook URL configured:
                                  </p>
                                  <div className="flex items-center gap-2 p-2 bg-green-100 rounded text-xs font-mono">
                                    <span className="flex-1 truncate">
                                      {validationResult.webhookUrl}
                                    </span>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() =>
                                        copyToClipboard(
                                          validationResult.webhookUrl!
                                        )
                                      }
                                    >
                                      <Copy className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                              )}
                              {validationResult.accountInfo && (
                                <div className="text-sm text-green-700">
                                  <p>
                                    Account:{" "}
                                    {validationResult.accountInfo.email ||
                                      validationResult.accountInfo.id}
                                  </p>
                                  {validationResult.accountInfo.country && (
                                    <p>
                                      Country:{" "}
                                      {validationResult.accountInfo.country}
                                    </p>
                                  )}
                                </div>
                              )}
                            </div>
                          ) : (
                            <div>
                              <p className="font-medium text-red-800">
                                ❌ {validationResult.error}
                              </p>
                              {validationResult.details && (
                                <p className="text-sm text-red-700 mt-1">
                                  {validationResult.details}
                                </p>
                              )}
                            </div>
                          )}
                        </AlertDescription>
                      </div>
                    </div>
                  </Alert>
                )}

                {provider.id === "stripe" && (
                  <Alert className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-emerald-600" />
                    <AlertDescription>
                      <strong>Automatic Webhook Setup:</strong> We'll
                      automatically create and configure webhook endpoints and
                      signing secrets for you. No manual webhook configuration
                      required.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
