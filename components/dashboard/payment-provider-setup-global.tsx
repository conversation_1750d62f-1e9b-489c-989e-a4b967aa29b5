"use client";

import { PaymentProviderSetup } from "./payment-provider-setup";

/**
 * Client Component wrapper for global PaymentProviderSetup
 * This wrapper handles the event handlers that cannot be passed from Server Components
 */
export function PaymentProviderSetupGlobal() {
  const handleProviderConfigured = (provider: string) => {
    console.log(`${provider} configured globally for all websites`);
  };

  return (
    <PaymentProviderSetup onProviderConfigured={handleProviderConfigured} />
  );
}
