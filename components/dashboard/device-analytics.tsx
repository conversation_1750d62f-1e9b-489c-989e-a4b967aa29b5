"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Monitor,
  Globe,
  Settings,
  RefreshCw,
  MoreHorizontal,
} from "lucide-react";
import type {
  DeviceAnalyticsProps,
  DeviceTab,
  ProcessedDeviceData,
  DeviceLoadingState,
} from "@/lib/types/device";
import { cn } from "@/lib/utils";
import DeviceTable from "./device-table";

export default function DeviceAnalytics({
  websiteId,
  dateRange,
  className,
}: DeviceAnalyticsProps) {
  const [activeTab, setActiveTab] = useState<DeviceTab>("device");
  const [data, setData] = useState<ProcessedDeviceData | null>(null);
  const [loading, setLoading] = useState<DeviceLoadingState>({
    overview: true,
    devices: true,
    browsers: true,
    os: true,
    realtime: false,
  });
  const [error, setError] = useState<string | null>(null);

  // Fetch device data from API route
  const fetchData = async () => {
    if (!websiteId || !dateRange.from || !dateRange.to) {
      return;
    }

    try {
      setError(null);
      setLoading({
        overview: true,
        devices: true,
        browsers: true,
        os: true,
        realtime: false,
      });

      // Call the API route
      const url = `/api/analytics/${websiteId}/device?start_date=${dateRange.from.toISOString()}&end_date=${dateRange.to.toISOString()}`;

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      setData({
        devices: data.devices,
        browsers: data.browsers,
        os: data.os,
        overview: data.overview,
      });
    } catch (error) {
      console.error("Failed to fetch device analytics data:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch data");
    } finally {
      setLoading({
        overview: false,
        devices: false,
        browsers: false,
        os: false,
        realtime: false,
      });
    }
  };

  // Fetch data when dependencies change
  useEffect(() => {
    fetchData();
  }, [websiteId, dateRange.from, dateRange.to]);

  const isLoading = Object.values(loading).some(Boolean);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <div className="flex items-center space-x-2">
          <Monitor className="h-4 w-4 text-blue-600" />
          <CardTitle className="text-base font-semibold">Devices</CardTitle>
        </div>
        <div className="flex items-center space-x-1">
          {data?.overview && (
            <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
              {data.overview.total_unique_visitors} Visitors
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={fetchData}
            disabled={isLoading}
            className="h-6 w-6 p-0"
          >
            <RefreshCw className={cn("h-3 w-3", isLoading && "animate-spin")} />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as DeviceTab)}
        >
          <div className="px-4 mb-4">
            <TabsList className="grid w-full grid-cols-3 h-9">
              <TabsTrigger
                value="device"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <Monitor className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">Device</span>
              </TabsTrigger>
              <TabsTrigger
                value="browser"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <Globe className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">Browser</span>
              </TabsTrigger>
              <TabsTrigger
                value="os"
                className="flex items-center justify-center space-x-1 text-xs px-1 py-1.5 min-w-0"
              >
                <Settings className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">OS</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="px-4 pb-4">
            <TabsContent value="device" className="mt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Device type
                  </h3>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>Visitors</span>
                    <span>%</span>
                  </div>
                </div>
                <DeviceTable
                  data={data?.devices || []}
                  loading={loading.devices}
                  error={error}
                  type="device"
                />
              </div>
            </TabsContent>

            <TabsContent value="browser" className="mt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Browser
                  </h3>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>Visitors</span>
                    <span>%</span>
                  </div>
                </div>
                <DeviceTable
                  data={data?.browsers || []}
                  loading={loading.browsers}
                  error={error}
                  type="browser"
                />
              </div>
            </TabsContent>

            <TabsContent value="os" className="mt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Operating system
                  </h3>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>Visitors</span>
                    <span>%</span>
                  </div>
                </div>
                <DeviceTable
                  data={data?.os || []}
                  loading={loading.os}
                  error={error}
                  type="os"
                />
              </div>
            </TabsContent>
          </div>
        </Tabs>

        {/* Details Button */}
        <div className="flex justify-center py-3 border-t">
          <Button
            variant="ghost"
            size="sm"
            className="text-xs text-muted-foreground"
          >
            <MoreHorizontal className="h-3 w-3 mr-1" />
            DETAILS
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
