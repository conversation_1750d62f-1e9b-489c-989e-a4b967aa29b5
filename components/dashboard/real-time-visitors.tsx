"use client";

import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Clock, Globe, MapPin, Users } from "lucide-react";
import { getRelativeTimeString } from "@/lib/utils/date";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { useMemo } from "react";

interface Visitor {
  visitor_id: string;
  url: string;
  country?: string;
  city?: string;
  device?: string;
  browser?: string;
  referrer?: string;
  timestamp: string;
}

interface RealTimeVisitorsProps {
  websiteId?: string;
}

export default function RealTimeVisitors({ websiteId }: RealTimeVisitorsProps) {
  // 使用 Convex useQuery hook 获取实时访客详细信息
  const presenceData = useQuery(
    api.presence.listWithDetailsByRoomId,
    websiteId ? { roomId: websiteId } : "skip"
  );

  // 转换 Convex Presence 数据为访客格式
  const visitors: Visitor[] = useMemo(() => {
    if (!presenceData || !Array.isArray(presenceData)) return [];

    const now = Date.now();
    const tenMinutesAgo = now - 10 * 60 * 1000; // 5分钟

    return presenceData
      .filter((item: any) => {
        // 只显示在线用户
        if (!item.online) return false;

        // 过滤掉无效或过期的 presence 数据
        const lastUpdate = item.details?.lastUpdate || item.updated || 0;
        return lastUpdate > tenMinutesAgo && item.details;
      })
      .map((item: any) => {
        const details = item.details;
        return {
          visitor_id: item.user || item.userId || "unknown",
          url: details.url || "",
          country: details.country || "",
          city: details.city || "",
          device: details.device || "",
          browser: details.browser || "",
          referrer: details.referrer || "",
          timestamp:
            details.timestamp ||
            new Date(details.lastUpdate || Date.now()).toISOString(),
        };
      });
  }, [presenceData]);

  const onlineCount = visitors.length;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">实时访客</CardTitle>
        <Users className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-4">{onlineCount} 在线</div>
        <CardDescription className="mb-4">
          当前活跃访客和他们的浏览活动
        </CardDescription>

        <div className="space-y-3 max-h-64 overflow-y-auto">
          {visitors.length === 0 ? (
            <div className="text-center text-sm text-muted-foreground py-4">
              {presenceData === undefined ? "加载中..." : "暂无在线访客"}
            </div>
          ) : (
            visitors.map((visitor) => (
              <div
                key={visitor.visitor_id}
                className="flex items-start space-x-3 p-3 rounded-lg border bg-card/50"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-sm font-medium text-foreground truncate">
                      访客 {visitor.visitor_id.slice(-8)}
                    </span>
                    {visitor.country && (
                      <Badge variant="secondary" className="text-xs">
                        <Globe className="w-3 h-3 mr-1" />
                        {visitor.city
                          ? `${visitor.city}, ${visitor.country}`
                          : visitor.country}
                      </Badge>
                    )}
                  </div>

                  <div className="text-xs text-muted-foreground space-y-1">
                    <div className="flex items-center space-x-1 truncate">
                      <MapPin className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">
                        {visitor.url || "未知页面"}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {visitor.device && <span>{visitor.device}</span>}
                        {visitor.browser && <span>{visitor.browser}</span>}
                      </div>

                      <div className="flex items-center space-x-1 flex-shrink-0">
                        <Clock className="w-3 h-3" />
                        <span>
                          {getRelativeTimeString(new Date(visitor.timestamp))}
                        </span>
                      </div>
                    </div>

                    {visitor.referrer && (
                      <div className="text-xs opacity-75 truncate">
                        来源: {visitor.referrer}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
