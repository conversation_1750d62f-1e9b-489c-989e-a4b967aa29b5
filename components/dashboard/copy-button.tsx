"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Copy } from "lucide-react";

interface CopyButtonProps {
  text: string;
  size?: "sm" | "default" | "lg" | "icon";
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  className?: string;
}

/**
 * Client Component for copy-to-clipboard functionality
 */
export function CopyButton({ 
  text, 
  size = "sm", 
  variant = "outline", 
  className 
}: CopyButtonProps) {
  const handleCopy = () => {
    navigator.clipboard.writeText(text);
  };

  return (
    <Button
      size={size}
      variant={variant}
      className={className}
      onClick={handleCopy}
    >
      <Copy className="h-4 w-4" />
    </Button>
  );
}
