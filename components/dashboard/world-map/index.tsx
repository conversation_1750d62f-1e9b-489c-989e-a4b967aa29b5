"use client";

import { useMemo, useState, useCallback, memo } from "react";
import { ComposableMap, Geographies, Geography } from "react-simple-maps";
import { cn } from "@/lib/utils";
import type { CountryMapData, WorldMapProps } from "@/lib/types/geographic";
import { formatVisitorCount } from "@/lib/utils/countries";

// Self-hosted GeoJSON to avoid third-party fetches; exposes ISO-3 in properties["ISO3166-1-Alpha-3"]
const GEO_URL = "/maps/countries-iso3.geojson";

// Minimal ISO2 -> ISO3 mapping for common countries (extend as needed)
const ISO2_TO_ISO3: Record<string, string> = {
  US: "USA",
  GB: "GBR",
  DE: "DEU",
  FR: "FRA",
  CA: "CAN",
  AU: "AUS",
  JP: "JPN",
  CN: "CHN",
  IN: "IND",
  BR: "BRA",
  NL: "NLD",
  ES: "ESP",
  IT: "ITA",
  SE: "SWE",
  NO: "NOR",
  DK: "DNK",
  FI: "FIN",
  CH: "CHE",
  AT: "AUT",
  BE: "BEL",
  IE: "IRL",
  PT: "PRT",
  PL: "POL",
  CZ: "CZE",
  HU: "HUN",
  RO: "ROU",
  BG: "BGR",
  HR: "HRV",
  SI: "SVN",
  SK: "SVK",
  LT: "LTU",
  LV: "LVA",
  EE: "EST",
  KR: "KOR",
  SG: "SGP",
  HK: "HKG",
  TW: "TWN",
  MY: "MYS",
  TH: "THA",
  ID: "IDN",
  PH: "PHL",
  VN: "VNM",
  AE: "ARE",
  SA: "SAU",
  IL: "ISR",
  TR: "TUR",
  ZA: "ZAF",
  EG: "EGY",
  MX: "MEX",
  AR: "ARG",
  CL: "CHL",
  CO: "COL",
  PE: "PER",
  NZ: "NZL",
  RU: "RUS",
  UA: "UKR",
};

// Memoize the ISO3 conversion function to avoid repeated computations
const toISO3 = (code: string): string => {
  const c = code?.toUpperCase();
  if (!c) return "";
  if (c.length === 3) return c;
  if (c.length === 2 && ISO2_TO_ISO3[c]) return ISO2_TO_ISO3[c];
  return c;
};

// Enhanced tooltip that shows data for all countries, even without visitor data
interface EnhancedTooltipData {
  countryName: string;
  visitors: number;
  percentage: number;
  revenue?: number;
  hasData: boolean;
}

const Tooltip = memo(
  ({
    data,
    position,
  }: {
    data: EnhancedTooltipData | null;
    position: { x: number; y: number };
  }) => {
    if (!data) return null;

    return (
      <div
        className="absolute z-50 pointer-events-none bg-white/95 dark:bg-zinc-900 border border-gray-200 dark:border-zinc-700 rounded-md shadow px-2.5 py-2 text-xs"
        style={{ left: position.x + 10, top: position.y + 10 }}
        role="tooltip"
        aria-label={`${data.countryName}: ${data.hasData ? formatVisitorCount(data.visitors) + " visitors" : "No data available"}`}
      >
        <div className="font-medium text-gray-900 dark:text-gray-100">
          {data.countryName}
        </div>
        {data.hasData ? (
          <>
            <div className="mt-0.5 text-gray-700 dark:text-gray-300">
              {formatVisitorCount(data.visitors)}{" "}
              {data.visitors === 1 ? "visitor" : "visitors"}
            </div>
            <div className="text-gray-600 dark:text-gray-400">
              {data.percentage.toFixed(1)}%
            </div>
            {typeof data.revenue === "number" && (
              <div className="text-green-600 dark:text-green-400">
                ${data.revenue.toFixed(2)} revenue
              </div>
            )}
          </>
        ) : (
          <div className="mt-0.5 text-gray-500 dark:text-gray-400 italic">
            No data available
          </div>
        )}
      </div>
    );
  }
);

const WorldMap = memo(
  ({ data, onCountryClick, onCountryHover, className }: WorldMapProps) => {
    // Fallback demo data if none provided - memoized to prevent recreation
    const demo: CountryMapData[] = useMemo(
      () => [
        {
          countryCode: "US",
          countryName: "United States",
          visitors: 1234,
          percentage: 45.2,
          revenue: 2500.75,
        },
        {
          countryCode: "GB",
          countryName: "United Kingdom",
          visitors: 567,
          percentage: 20.8,
          revenue: 1200.5,
        },
      ],
      []
    );

    const mapData = useMemo(() => (data?.length ? data : demo), [data, demo]);

    // Build fast lookup map keyed by ISO3 and original code; memoized for performance
    const dataMap = useMemo(() => {
      const m = new Map<string, CountryMapData>();
      for (const item of mapData) {
        const iso3 = toISO3(item.countryCode);
        if (iso3) m.set(iso3, item);
        if (item.countryCode) m.set(item.countryCode.toUpperCase(), item);
      }
      return m;
    }, [mapData]);

    const maxVisitors = useMemo(
      () => (mapData.length ? Math.max(...mapData.map((d) => d.visitors)) : 0),
      [mapData]
    );

    const [tooltip, setTooltip] = useState<EnhancedTooltipData | null>(null);
    const [mousePos, setMousePos] = useState({ x: 0, y: 0 });

    // Memoize the fill function to prevent recalculation on every render
    const getFill = useCallback(
      (feature: any): string => {
        // Dataset stores ISO3 in properties["ISO3166-1-Alpha-3"]
        const iso3 = (
          feature?.properties?.["ISO3166-1-Alpha-3"] as string | undefined
        )?.toUpperCase();
        if (!iso3) return "#eef2ff"; // low
        const d = dataMap.get(iso3);
        if (!d || maxVisitors === 0) return "#eef2ff";
        const t = d.visitors / maxVisitors;
        if (t > 0.8) return "#4f46e5"; // high
        if (t > 0.6) return "#6366f1";
        if (t > 0.4) return "#818cf8";
        if (t > 0.2) return "#a5b4fc";
        if (t > 0.1) return "#c7d2fe";
        return "#e0e7ff";
      },
      [dataMap, maxVisitors]
    );

    // Memoize mouse position update to prevent unnecessary re-renders
    const updateMousePos = useCallback((e: React.MouseEvent) => {
      const container = (e.currentTarget as Element).closest(
        ".relative"
      ) as HTMLElement | null;
      const bounds = container?.getBoundingClientRect();
      setMousePos({
        x: bounds ? e.clientX - bounds.left : e.clientX,
        y: bounds ? e.clientY - bounds.top : e.clientY,
      });
    }, []);

    return (
      <div
        className={cn(
          "relative w-full h-80 border border-gray-200 rounded-lg bg-slate-50 overflow-hidden",
          className
        )}
      >
        <ComposableMap
          projection="geoMercator"
          projectionConfig={{ scale: 110, center: [0, 20] }}
          className="w-full h-full"
          width={800}
          height={320}
          style={{ width: "100%", height: "100%" }}
        >
          <Geographies geography={GEO_URL}>
            {({ geographies }) =>
              geographies.map((geo) => {
                const iso3 = (geo.properties as any)?.[
                  "ISO3166-1-Alpha-3"
                ]?.toUpperCase();
                const d = iso3 ? dataMap.get(iso3) : undefined;
                return (
                  <Geography
                    key={geo.rsmKey}
                    geography={geo}
                    fill={getFill(geo)}
                    stroke="#cbd5e1"
                    strokeWidth={0.5}
                    className="outline-none focus:outline-none cursor-pointer"
                    onMouseEnter={(e) => {
                      if (iso3) {
                        // Always show tooltip for countries with ISO3 codes
                        const countryName = geo.properties?.name || iso3;
                        if (d) {
                          // Country has visitor data
                          setTooltip({
                            countryName: d.countryName,
                            visitors: d.visitors,
                            percentage: d.percentage,
                            revenue: d.revenue,
                            hasData: true,
                          });
                        } else {
                          // Country exists but no visitor data
                          setTooltip({
                            countryName,
                            visitors: 0,
                            percentage: 0,
                            hasData: false,
                          });
                        }
                        updateMousePos(e);
                        onCountryHover?.(iso3);
                      } else {
                        setTooltip(null);
                        onCountryHover?.(null);
                      }
                    }}
                    onMouseMove={updateMousePos}
                    onMouseLeave={() => {
                      setTooltip(null);
                      onCountryHover?.(null);
                    }}
                    onClick={() => {
                      if (iso3) onCountryClick?.(iso3);
                    }}
                    style={{
                      default: { outline: "none" },
                      hover: { outline: "none" },
                      pressed: { outline: "none" },
                    }}
                  />
                );
              })
            }
          </Geographies>
        </ComposableMap>

        <Tooltip data={tooltip} position={mousePos} />

        <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-md px-2 py-1.5 shadow-sm">
          <div className="flex items-center gap-2 text-xs text-gray-700">
            <span className="inline-block w-3 h-3 rounded-sm bg-[#e0e7ff]" />
            <span>Low</span>
            <span className="inline-block w-12 h-px bg-gray-300 mx-1" />
            <span>High</span>
            <span className="inline-block w-3 h-3 rounded-sm bg-[#4f46e5]" />
          </div>
        </div>
      </div>
    );
  }
);

WorldMap.displayName = "WorldMap";

export default WorldMap;
