"use client";

import { useState, useTransition, useEffect } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  deleteWebsite,
  getWebsiteDeletionInfoAction,
} from "@/lib/actions/dashboard";
import {
  AlertTriangle,
  Loader2,
  Trash2,
  BarChart3,
  Target,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface WebsiteDeletionDialogProps {
  websiteId: string;
  websiteName: string;
  websiteDomain: string;
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

export function WebsiteDeletionDialog({
  websiteId,
  websiteName,
  websiteDomain,
  trigger,
  onSuccess,
}: WebsiteDeletionDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [confirmationText, setConfirmationText] = useState("");
  const [isPending, startTransition] = useTransition();
  const [deletionInfo, setDeletionInfo] = useState<{
    eventCount: number;
    goalCount: number;
  } | null>(null);
  const [isLoadingInfo, setIsLoadingInfo] = useState(false);
  const router = useRouter();

  const isConfirmationValid = confirmationText === websiteName;

  // Load deletion info when dialog opens
  useEffect(() => {
    if (isOpen && !deletionInfo) {
      setIsLoadingInfo(true);
      getWebsiteDeletionInfoAction(websiteId)
        .then(setDeletionInfo)
        .catch((error: Error) => {
          console.error("Failed to load deletion info:", error);
          setDeletionInfo({ eventCount: 0, goalCount: 0 });
        })
        .finally(() => setIsLoadingInfo(false));
    }
  }, [isOpen, websiteId, deletionInfo]);

  const handleDelete = () => {
    if (!isConfirmationValid) {
      toast.error("Confirmation required", {
        description:
          "Please type the website name exactly as shown to confirm deletion.",
      });
      return;
    }

    startTransition(async () => {
      try {
        await deleteWebsite(websiteId);

        toast.success("Website deleted successfully", {
          description: `${websiteName} has been deleted and will no longer collect analytics data.`,
        });

        setIsOpen(false);
        setConfirmationText("");
        setDeletionInfo(null);

        if (onSuccess) {
          onSuccess();
        } else {
          // Default behavior: redirect to websites list
          router.push("/dashboard/websites");
        }
      } catch (error) {
        console.error("Delete website error:", error);

        let errorMessage = "Failed to delete website";
        let errorDescription = "An unexpected error occurred";

        if (error instanceof Error) {
          if (error.message.includes("not found")) {
            errorMessage = "Website not found";
            errorDescription =
              "The website may have already been deleted or you don't have permission to delete it.";
          } else if (error.message.includes("access denied")) {
            errorMessage = "Access denied";
            errorDescription =
              "You don't have permission to delete this website.";
          } else {
            errorDescription = error.message;
          }
        }

        toast.error(errorMessage, {
          description: errorDescription,
        });
      }
    });
  };

  const handleOpenChange = (open: boolean) => {
    if (!isPending) {
      setIsOpen(open);
      if (!open) {
        setConfirmationText("");
        setDeletionInfo(null);
      }
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={handleOpenChange}>
      <AlertDialogTrigger asChild>
        {trigger || (
          <Button variant="destructive" size="sm">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Website
          </Button>
        )}
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Website
          </AlertDialogTitle>
          <AlertDialogDescription className="text-left">
            This action will permanently delete <strong>{websiteName}</strong>{" "}
            and all its analytics data.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          {/* Warning Alert */}
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>This action cannot be undone.</strong> All analytics data,
              goals, and settings for this website will be permanently removed.
            </AlertDescription>
          </Alert>

          {/* Website Details */}
          <div className="bg-gray-50 p-3 rounded-lg space-y-1">
            <div className="text-sm">
              <span className="font-medium">Website:</span> {websiteName}
            </div>
            <div className="text-sm text-gray-600">
              <span className="font-medium">Domain:</span> {websiteDomain}
            </div>
          </div>

          {/* Deletion Info */}
          {isLoadingInfo ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-sm text-gray-600">
                Loading website data...
              </span>
            </div>
          ) : deletionInfo ? (
            <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
              <h4 className="text-sm font-medium text-red-800 mb-2">
                Data to be deleted:
              </h4>
              <div className="space-y-1 text-sm text-red-700">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>
                    {deletionInfo.eventCount.toLocaleString()} analytics events
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  <span>{deletionInfo.goalCount} goals</span>
                </div>
              </div>
            </div>
          ) : null}

          {/* Confirmation Input */}
          <div className="space-y-2">
            <Label htmlFor="confirmation" className="text-sm font-medium">
              Type{" "}
              <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">
                {websiteName}
              </code>{" "}
              to confirm deletion:
            </Label>
            <Input
              id="confirmation"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder={`Type "${websiteName}" here`}
              disabled={isPending}
              className={
                confirmationText && !isConfirmationValid
                  ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                  : ""
              }
            />
            {confirmationText && !isConfirmationValid && (
              <p className="text-sm text-red-600">
                Please type the website name exactly as shown above.
              </p>
            )}
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={!isConfirmationValid || isPending}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
          >
            {isPending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Website
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
