"use client";

import { useEffect, useRef, useState, useMemo } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Globe,
  MapPin,
  Clock,
  Monitor,
  Smartphone,
  X,
} from "lucide-react";
import { api } from "@/convex/_generated/api";

interface Visitor {
  id: string;
  country: string;
  city: string;
  lat: number;
  lng: number;
  timestamp: string;
  url: string;
  referrer?: string;
  device?: string;
  browser?: string;
  avatar: {
    color: string;
    name: string;
  };
}

interface DataFastStyleGlobeProps {
  visitors: any[];
  onClose?: (() => void) | null;
}

// Avatar designs matching DataFast style
const AVATAR_DESIGNS = [
  { color: "#ff6b6b", name: "coral pheasant" },
  { color: "#4ecdc4", name: "cyan mollusk" },
  { color: "#45b7d1", name: "blue capybara" },
  { color: "#96ceb4", name: "mint ferret" },
  { color: "#feca57", name: "golden jay" },
  { color: "#ff9ff3", name: "pink warbler" },
  { color: "#54a0ff", name: "azure krill" },
  { color: "#5f27cd", name: "violet booby" },
  { color: "#00d2d3", name: "teal parrot" },
  { color: "#ff6348", name: "orange mole" },
  { color: "#2ed573", name: "green catfish" },
  { color: "#ffa502", name: "amber sturgeon" },
];

// Static test data matching DataFast's visitor structure for development
const STATIC_TEST_VISITORS = [
  {
    id: "1",
    name: "cyan octopus",
    country: "France",
    countryCode: "FR",
    lat: 46.2276,
    lng: 2.2137,
    avatar: { color: "#00CED1", name: "cyan octopus" },
    lastUpdate: Date.now() - 2 * 60 * 1000, // 2 minutes ago
    device: "desktop",
    referrer: "Google",
    page: "/course/business/backend-front-end/schema",
  },
  {
    id: "2",
    name: "jade swift",
    country: "Germany",
    countryCode: "DE",
    lat: 51.1657,
    lng: 10.4515,
    avatar: { color: "#00A86B", name: "jade swift" },
    lastUpdate: Date.now() - 5 * 60 * 1000, // 5 minutes ago
    device: "desktop",
    referrer: "Direct",
    page: "/course/business/deployment/tips",
  },
  {
    id: "3",
    name: "orange spoonbill",
    country: "Spain",
    countryCode: "ES",
    lat: 40.4637,
    lng: -3.7492,
    avatar: { color: "#FF8C00", name: "orange spoonbill" },
    lastUpdate: Date.now() - 3 * 60 * 1000, // 3 minutes ago
    device: "mobile",
    referrer: "X",
    page: "/",
  },
  {
    id: "4",
    name: "blue felidae",
    country: "Turkey",
    countryCode: "TR",
    lat: 38.9637,
    lng: 35.2433,
    avatar: { color: "#4169E1", name: "blue felidae" },
    lastUpdate: Date.now() - 1 * 60 * 1000, // 1 minute ago
    device: "desktop",
    referrer: "Facebook",
    page: "/",
  },
  {
    id: "5",
    name: "amber landfowl",
    country: "United States",
    countryCode: "US",
    lat: 37.0902,
    lng: -95.7129,
    avatar: { color: "#FFBF00", name: "amber landfowl" },
    lastUpdate: Date.now() - 4 * 60 * 1000, // 4 minutes ago
    device: "desktop",
    referrer: "Direct",
    page: "/",
  },
  {
    id: "6",
    name: "crimson leech",
    country: "North Macedonia",
    countryCode: "MK",
    lat: 41.6086,
    lng: 21.7453,
    avatar: { color: "#DC143C", name: "crimson leech" },
    lastUpdate: Date.now() - 6 * 60 * 1000, // 6 minutes ago
    device: "mobile",
    referrer: "Google",
    page: "/",
  },
  {
    id: "7",
    name: "salmon moth",
    country: "Pakistan",
    countryCode: "PK",
    lat: 30.3753,
    lng: 69.3451,
    avatar: { color: "#FA8072", name: "salmon moth" },
    lastUpdate: Date.now() - 1 * 60 * 1000, // 1 minute ago
    device: "mobile",
    referrer: "shipfa.st",
    page: "/",
  },
  {
    id: "8",
    name: "amethyst swordtail",
    country: "Chile",
    countryCode: "CL",
    lat: -35.6751,
    lng: -71.543,
    avatar: { color: "#9966CC", name: "amethyst swordtail" },
    lastUpdate: Date.now() - 5 * 60 * 1000, // 5 minutes ago
    device: "desktop",
    referrer: "Direct",
    page: "/",
  },
];

export default function DataFastStyleGlobe({
  visitors,
  onClose,
}: DataFastStyleGlobeProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapError, setMapError] = useState<string | null>(null);
  const markersRef = useRef<mapboxgl.Marker[]>([]);

  // Refs for the official Mapbox globe spin implementation
  const resizeHandlerRef = useRef<(() => void) | null>(null);
  const userInteractingRef = useRef<boolean>(false);
  const isZoomingRef = useRef<boolean>(false);
  const spinGlobeRef = useRef<(() => void) | null>(null);
  // Refs to overlays to compute native Mapbox padding
  const headerRef = useRef<HTMLDivElement>(null);
  const activityRef = useRef<HTMLDivElement>(null);

  // Process visitors data - use static test data if no real visitors
  // Memoize to prevent unnecessary marker recreations
  const processedVisitors = useMemo(() => {
    if (!visitors || visitors.length === 0) {
      // Use static test data for development/demonstration
      return STATIC_TEST_VISITORS;
    } else {
      // Process real visitor data
      return visitors.map((visitor, index) => ({
        id: visitor.userId || visitor.id || `visitor-${index}`,
        country: visitor.country || "Unknown",
        city: visitor.city || "Unknown",
        lat: visitor.lat || ((index * 23.7) % 180) - 90, // Stable fallback coordinates
        lng: visitor.lng || ((index * 47.3) % 360) - 180,
        timestamp: visitor.timestamp || new Date().toISOString(),
        url: visitor.url || "/",
        referrer: visitor.referrer,
        device: visitor.device,
        browser: visitor.browser,
        avatar: AVATAR_DESIGNS[index % AVATAR_DESIGNS.length],
      }));
    }
  }, [visitors]);

  // Initialize Mapbox
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    const accessToken = process.env.NEXT_PUBLIC_MAPBOX_PUBLIC_TOKEN;
    if (!accessToken) {
      setMapError("Mapbox access token not found");
      return;
    }

    mapboxgl.accessToken = accessToken;

    try {
      map.current = new mapboxgl.Map({
        container: mapContainer.current!,
        style: "mapbox://styles/mapbox/standard",
        center: [-90, 40], // Perfect center for globe
        zoom: 2.6,
        // pitch: 90, // Start with 0 pitch for better globe view
        bearing: 0,
        projection: "globe" as any,
        antialias: true,
        interactive: true, // Allow user interaction
      });

      // Initial resize to ensure canvas matches container
      map.current.resize();

      map.current.on("load", () => {
        if (!map.current) return;

        // Add DataFast-style atmospheric fog effects - matching their exact appearance
        map.current.setFog({});

        // Ensure map is correctly sized after load
        map.current.resize();

        // Official Mapbox Globe Spin implementation
        // The following values can be changed to control rotation speed:
        const secondsPerRevolution = 120; // Complete a revolution every two minutes
        const maxSpinZoom = 5; // Above zoom level 5, do not rotate
        const slowSpinZoom = 3; // Rotate at intermediate speeds between zoom levels 3 and 5

        let spinEnabled = true;
        let zoomTimeout: NodeJS.Timeout | null = null;

        const spinGlobe = () => {
          const zoom = map.current?.getZoom() || 1.5;
          // Don't spin if user is interacting, zooming, or zoom is too high
          if (
            spinEnabled &&
            !userInteractingRef.current &&
            !isZoomingRef.current &&
            zoom < maxSpinZoom
          ) {
            let distancePerSecond = 360 / secondsPerRevolution;
            if (zoom > slowSpinZoom) {
              // Slow spinning at higher zooms
              const zoomDif =
                (maxSpinZoom - zoom) / (maxSpinZoom - slowSpinZoom);
              distancePerSecond *= zoomDif;
            }
            const center = map.current?.getCenter();
            if (center) {
              center.lng -= distancePerSecond;
              // Use even shorter duration for optimal zoom performance
              map.current?.easeTo({ center, duration: 600, easing: (n) => n });
            }
          }
        };

        // Store spinGlobe function in ref for access from marker handlers
        spinGlobeRef.current = spinGlobe;

        // Official Mapbox interaction handlers for globe spinning
        // Pause spinning on interaction
        map.current.on("mousedown", () => {
          userInteractingRef.current = true;
        });

        // Restart spinning when interaction is complete
        map.current.on("mouseup", () => {
          userInteractingRef.current = false;
          spinGlobe();
        });

        // Handle cases where mouse moves off the map
        map.current.on("dragend", () => {
          userInteractingRef.current = false;
          spinGlobe();
        });

        map.current.on("pitchend", () => {
          userInteractingRef.current = false;
          spinGlobe();
        });

        map.current.on("rotateend", () => {
          userInteractingRef.current = false;
          spinGlobe();
        });

        // Handle zoom events to pause spinning during zoom operations
        map.current.on("zoomstart", () => {
          isZoomingRef.current = true;
          if (zoomTimeout) {
            clearTimeout(zoomTimeout);
            zoomTimeout = null;
          }
        });

        map.current.on("zoomend", () => {
          // Resume spinning after a short delay to ensure zoom is complete
          zoomTimeout = setTimeout(() => {
            isZoomingRef.current = false;
            if (!userInteractingRef.current) {
              spinGlobe();
            }
          }, 200); // 200ms delay after zoom ends
        });

        // When animation is complete, start spinning if no ongoing interaction
        map.current.on("moveend", () => {
          if (!isZoomingRef.current) {
            spinGlobe();
          }
        });

        // Start the globe spinning
        spinGlobe();

        setMapLoaded(true);
      });

      map.current.on("error", (e) => {
        console.error("Mapbox error:", e);
        setMapError("Failed to load map");
      });
    } catch (error) {
      console.error("Failed to initialize Mapbox:", error);
      setMapError("Failed to initialize map");
    }

    return () => {
      // Clean up markers
      markersRef.current.forEach((marker) => marker.remove());
      markersRef.current = [];

      // Clean up map (this will also stop any ongoing animations)
      if (map.current) {
        map.current.stop(); // Stop any ongoing easeTo animations
        map.current.remove();
        map.current = null;
      }
    };
  }, []);

  // Add markers for visitors
  useEffect(() => {
    if (
      !map.current ||
      !mapLoaded ||
      mapError ||
      processedVisitors.length === 0
    )
      return;

    // Clear existing markers
    markersRef.current.forEach((marker) => marker.remove());
    markersRef.current = [];

    // Add new markers
    processedVisitors.forEach((visitor) => {
      // Create avatar element
      const avatarEl = document.createElement("div");
      avatarEl.className =
        "w-14 h-14 rounded-full border-2 border-white shadow-lg cursor-pointer transition-opacity hover:opacity-80";
      avatarEl.style.backgroundColor = visitor.avatar.color;
      avatarEl.style.backgroundImage = `url("data:image/svg+xml,${encodeURIComponent(
        `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="35" r="20" fill="white" opacity="0.8"/><circle cx="50" cy="75" r="30" fill="white" opacity="0.8"/></svg>`
      )}")`;
      avatarEl.style.backgroundSize = "cover";

      // Add interaction handlers to pause spinning during hover
      avatarEl.addEventListener("mouseenter", () => {
        userInteractingRef.current = true; // Pause spinning on hover
      });

      avatarEl.addEventListener("mouseleave", () => {
        userInteractingRef.current = false; // Resume spinning when hover ends
        if (!isZoomingRef.current && spinGlobeRef.current) {
          setTimeout(() => spinGlobeRef.current?.(), 100); // Resume after short delay
        }
      });

      // Add click handler for visitor details
      avatarEl.addEventListener("click", () => {
        console.log("Visitor clicked:", visitor);
      });

      // Create marker
      const marker = new mapboxgl.Marker(avatarEl)
        .setLngLat([visitor.lng, visitor.lat])
        .addTo(map.current!);

      markersRef.current.push(marker);
    });
  }, [mapLoaded, mapError, processedVisitors]); // Removed map.current to prevent flickering

  if (mapError) {
    return (
      <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md mx-4">
          <h3 className="text-lg font-semibold text-red-600 mb-2">Map Error</h3>
          <p className="text-gray-600 mb-4">{mapError}</p>
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Close
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black z-50">
      {/* Header */}
      <div
        ref={headerRef}
        className="absolute top-0 left-0 right-0 z-10 bg-black/50 backdrop-blur-sm"
      >
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-white" />
              <span className="text-white font-medium">Real-time</span>
            </div>
            <div className="text-white/80">|</div>
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-white">
                {processedVisitors.length}
              </span>
              <span className="text-white/80">active users</span>
            </div>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
      </div>

      {/* Map Container - True fullscreen canvas; centering via Mapbox camera + padding */}
      <div ref={mapContainer} className="fixed inset-0 w-screen h-screen " />

      {/* Activity Feed */}
      <div
        ref={activityRef}
        className="absolute top-20 right-4 w-80 max-h-96 overflow-y-auto bg-black/80 backdrop-blur-sm rounded-lg p-4 space-y-3"
      >
        <h3 className="text-white font-medium mb-3">Recent Activity</h3>
        {processedVisitors.slice(0, 10).map((visitor) => (
          <div key={visitor.id} className="flex items-center space-x-3 text-sm">
            <div
              className="w-8 h-8 rounded-full border border-white/20"
              style={{ backgroundColor: visitor.avatar.color }}
            />
            <div className="flex-1 text-white/90">
              <div className="font-medium">{visitor.avatar.name}</div>
              <div className="text-white/60 text-xs">
                from {(visitor as any).city || visitor.country},{" "}
                {visitor.country}
              </div>
              <div className="text-white/60 text-xs">
                visited {(visitor as any).url || (visitor as any).page} •{" "}
                {new Date(
                  (visitor as any).timestamp || (visitor as any).lastUpdate
                ).toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Stats Panel */}
      <div className="absolute bottom-4 left-4 bg-black/80 backdrop-blur-sm rounded-lg p-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-white">
              {processedVisitors.length}
            </div>
            <div className="text-white/60 text-xs">Visitors</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-white">
              {new Set(processedVisitors.map((v) => v.country)).size}
            </div>
            <div className="text-white/60 text-xs">Countries</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-white">
              {new Set(processedVisitors.map((v) => v.device)).size}
            </div>
            <div className="text-white/60 text-xs">Devices</div>
          </div>
        </div>
      </div>
    </div>
  );
}
