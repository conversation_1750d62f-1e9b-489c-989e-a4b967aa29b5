"use client";

import { useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import dynamic from "next/dynamic";

// Dynamically import DataFast-style globe component
const DataFastStyleGlobe = dynamic(() => import("./datafast-style-globe"), {
  ssr: false,
  loading: () => <div className="w-full h-full bg-gray-900 animate-pulse" />,
});

interface FullscreenGlobeProps {
  websiteId: string; // trackingId for presence data
}

export default function FullscreenGlobe({ websiteId }: FullscreenGlobeProps) {
  // Get real-time presence data
  const presenceData = useQuery(
    api.presence.listWithDetailsByRoomId,
    websiteId ? { roomId: websiteId } : "skip"
  );

  // Process presence data into active visitors
  const activeVisitors = useMemo(() => {
    if (!presenceData || !Array.isArray(presenceData)) return [];

    const now = Date.now();
    const fiveMinutesAgo = now - 5 * 60 * 1000;

    return presenceData
      .filter((item: any) => {
        if (!item.online) return false;
        const lastUpdate = item.details?.lastUpdate || item.updated || 0;
        return lastUpdate > fiveMinutesAgo && item.details;
      })
      .slice(0, 50); // Limit to 50 visitors for performance
  }, [presenceData]);

  // Always show the globe directly (no modal/button)
  return (
    <DataFastStyleGlobe
      visitors={activeVisitors}
      // No onClose prop for direct access - this will hide close buttons
    />
  );
}
