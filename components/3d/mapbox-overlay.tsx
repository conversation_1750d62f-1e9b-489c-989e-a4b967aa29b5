"use client";

import { useEffect, useRef, useState } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

interface GlobePoint {
  id: string;
  lat: number;
  lng: number;
  country: string;
  city: string;
  visitorCount: number;
  color: string;
  size: number;
  timestamp: string;
}

interface MapboxOverlayProps {
  visitors: any[];
  globePoints: GlobePoint[];
}

// Avatar designs matching the 3D globe (DataFast-inspired)
const AVATAR_DESIGNS = [
  { emoji: "🦄", name: "Mystic Unicorn", color: "#ff6b9d" },
  { emoji: "🐉", name: "Azure Dragon", color: "#4ecdc4" },
  { emoji: "🦋", name: "Cosmic Butterfly", color: "#45b7d1" },
  { emoji: "🐺", name: "Silver Wolf", color: "#96ceb4" },
  { emoji: "🦅", name: "Golden Eagle", color: "#feca57" },
  { emoji: "🐯", name: "Neon Tiger", color: "#ff9ff3" },
  { emoji: "🦊", name: "Crimson Fox", color: "#ff6b6b" },
  { emoji: "🐙", name: "Electric Octopus", color: "#a8e6cf" },
  { emoji: "🦁", name: "Royal Lion", color: "#ffd93d" },
  { emoji: "🐨", name: "Zen Koala", color: "#6c5ce7" },
];

export default function MapboxOverlay({
  visitors,
  globePoints,
}: MapboxOverlayProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapError, setMapError] = useState<string | null>(null);
  const markersRef = useRef<mapboxgl.Marker[]>([]);

  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    // Get Mapbox access token from environment
    const accessToken = process.env.NEXT_PUBLIC_MAPBOX_PUBLIC_TOKEN;
    if (!accessToken) {
      setMapError(
        "Mapbox access token not found. Please set NEXT_PUBLIC_MAPBOX_PUBLIC_TOKEN environment variable."
      );
      return;
    }

    mapboxgl.accessToken = accessToken;

    try {
      // Initialize Mapbox GL JS map with globe projection (DataFast style)
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/satellite-v9", // Realistic Earth appearance
        center: [0, 20],
        zoom: 1.5,
        pitch: 0,
        bearing: 0,
        projection: "globe", // Key feature for 3D globe view
        antialias: true,
      });

      map.current.on("load", () => {
        if (!map.current) return;

        // Add atmospheric fog effects (DataFast-inspired)
        map.current.setFog({
          range: [0.8, 8],
          color: "rgba(13, 13, 13, 1)",
          "horizon-blend": 0.05,
          "high-color": "rgba(255, 255, 255, 0.5)",
          "space-color": "rgba(11, 11, 25, 1)",
          "star-intensity": 0.15,
        });

        // Add country labels layer for better geographic context
        map.current.addLayer({
          id: "country-labels",
          type: "symbol",
          source: "composite",
          "source-layer": "country_label",
          layout: {
            "text-field": ["get", "name"],
            "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
            "text-size": {
              base: 1,
              stops: [
                [1, 11],
                [6, 14],
              ],
            },
            "text-transform": "uppercase",
            "text-letter-spacing": 0.1,
          },
          paint: {
            "text-color": "#ffffff",
            "text-halo-color": "rgba(0, 0, 0, 0.8)",
            "text-halo-width": 1,
            "text-opacity": 0.8,
          },
        });

        setMapLoaded(true);
      });

      map.current.on("error", (e) => {
        console.error("Mapbox error:", e);
        setMapError(
          "Failed to load map. Please check your internet connection and Mapbox token."
        );
      });
    } catch (error) {
      console.error("Failed to initialize Mapbox:", error);
      setMapError(
        "Failed to initialize map. Please check your Mapbox configuration."
      );
    }

    return () => {
      // Cleanup markers
      markersRef.current.forEach((marker) => marker.remove());
      markersRef.current = [];

      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, []);

  // Add/update markers when visitors data changes
  useEffect(() => {
    if (!map.current || !mapLoaded || mapError) return;

    // Clear existing markers
    markersRef.current.forEach((marker) => marker.remove());
    markersRef.current = [];

    // Add new markers for each visitor (DataFast-style)
    visitors.forEach((visitor, index) => {
      const avatar = AVATAR_DESIGNS[index % AVATAR_DESIGNS.length];

      // Create marker element with DataFast-inspired styling
      const markerEl = document.createElement("div");
      markerEl.className = "mapbox-visitor-marker";
      markerEl.style.cssText = `
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, ${avatar.color}40, ${avatar.color}60);
        border: 2px solid ${avatar.color};
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 20px rgba(0,0,0,0.4), 0 0 0 0 ${avatar.color}40;
        position: relative;
        animation: pulse 2s infinite;
      `;
      markerEl.textContent = avatar.emoji;

      // Add CSS animation for pulsing effect
      const style = document.createElement("style");
      style.textContent = `
        @keyframes pulse {
          0% { box-shadow: 0 4px 20px rgba(0,0,0,0.4), 0 0 0 0 ${avatar.color}40; }
          50% { box-shadow: 0 4px 20px rgba(0,0,0,0.4), 0 0 0 10px ${avatar.color}20; }
          100% { box-shadow: 0 4px 20px rgba(0,0,0,0.4), 0 0 0 0 ${avatar.color}40; }
        }
      `;
      document.head.appendChild(style);

      // Enhanced hover effects (DataFast-quality)
      markerEl.addEventListener("mouseenter", () => {
        markerEl.style.transform = "scale(1.3)";
        markerEl.style.boxShadow = `0 8px 32px ${avatar.color}60, 0 0 0 4px ${avatar.color}30`;
        markerEl.style.zIndex = "1000";
      });

      markerEl.addEventListener("mouseleave", () => {
        markerEl.style.transform = "scale(1)";
        markerEl.style.boxShadow = `0 4px 20px rgba(0,0,0,0.4), 0 0 0 0 ${avatar.color}40`;
        markerEl.style.zIndex = "auto";
      });

      // Get coordinates from visitor data
      const lat = visitor.details?.latitude || 0;
      const lng = visitor.details?.longitude || 0;

      if (lat !== 0 || lng !== 0) {
        // Create professional popup content (DataFast-inspired)
        const timeAgo = Math.floor(
          (Date.now() - (visitor.details?.lastUpdate || visitor.updated || 0)) /
            1000
        );
        const timeText =
          timeAgo < 60 ? "just now" : `${Math.floor(timeAgo / 60)}m ago`;

        const popupContent = `
          <div style="
            padding: 16px; 
            min-width: 240px; 
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-radius: 12px;
            border: 1px solid ${avatar.color}40;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          ">
            <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
              <div style="
                width: 40px; 
                height: 40px; 
                border-radius: 50%; 
                background: ${avatar.color}20; 
                border: 2px solid ${avatar.color}; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                font-size: 18px;
              ">${avatar.emoji}</div>
              <div>
                <div style="font-weight: 600; color: ${avatar.color}; font-size: 14px;">${avatar.name}</div>
                <div style="font-size: 12px; color: #94a3b8;">${timeText}</div>
              </div>
            </div>
            <div style="space-y: 8px;">
              <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                <span style="color: #64748b;">📍</span>
                <span style="font-size: 13px;">${visitor.details?.city || "Unknown"}, ${visitor.details?.country || "Unknown"}</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 6px;">
                <span style="color: #64748b;">💻</span>
                <span style="font-size: 13px;">${visitor.details?.device || "Unknown"} • ${visitor.details?.browser || "Unknown"}</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="color: #64748b;">🌐</span>
                <span style="font-size: 13px;">${visitor.details?.page || "/"}</span>
              </div>
            </div>
          </div>
        `;

        const popup = new mapboxgl.Popup({
          offset: 25,
          closeButton: true,
          closeOnClick: false,
          className: "datafast-popup",
        }).setHTML(popupContent);

        const marker = new mapboxgl.Marker(markerEl)
          .setLngLat([lng, lat])
          .setPopup(popup)
          .addTo(map.current!);

        markersRef.current.push(marker);
      }
    });
  }, [visitors, mapLoaded, mapError]);

  return (
    <div className="relative w-full h-full">
      <div
        ref={mapContainer}
        className="w-full h-full"
        style={{
          background:
            "radial-gradient(ellipse at center, #1e293b 0%, #0f172a 70%, #000000 100%)",
        }}
      />

      {/* Loading overlay */}
      {!mapLoaded && !mapError && (
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
          <div className="text-white text-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-400 mx-auto mb-6"></div>
              <div
                className="absolute inset-0 rounded-full h-16 w-16 border-r-2 border-purple-400 animate-spin mx-auto"
                style={{
                  animationDirection: "reverse",
                  animationDuration: "3s",
                }}
              ></div>
            </div>
            <p className="text-lg font-medium">Initializing 3D Globe...</p>
            <p className="text-sm text-gray-400 mt-2">
              Powered by Mapbox GL JS
            </p>
          </div>
        </div>
      )}

      {/* Error overlay */}
      {mapError && (
        <div className="absolute inset-0 bg-gradient-to-br from-red-900/20 via-slate-900 to-slate-900 flex items-center justify-center">
          <div className="text-white text-center max-w-md px-6">
            <div className="text-red-400 text-6xl mb-4">⚠️</div>
            <h3 className="text-xl font-semibold mb-2">Map Loading Error</h3>
            <p className="text-gray-300 text-sm leading-relaxed">{mapError}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Attribution */}
      <div className="absolute bottom-2 right-2 text-xs text-white/60 bg-black/40 px-3 py-1 rounded-full backdrop-blur-sm">
        <span className="flex items-center gap-1">
          🌍 Powered by Mapbox GL JS
        </span>
      </div>

      {/* Custom CSS for popup styling */}
      <style jsx global>{`
        .datafast-popup .mapboxgl-popup-content {
          background: transparent !important;
          padding: 0 !important;
          border-radius: 12px !important;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
        }
        .datafast-popup .mapboxgl-popup-tip {
          border-top-color: #334155 !important;
        }
        .datafast-popup .mapboxgl-popup-close-button {
          color: #94a3b8 !important;
          font-size: 18px !important;
          padding: 8px !important;
          right: 8px !important;
          top: 8px !important;
        }
        .datafast-popup .mapboxgl-popup-close-button:hover {
          color: white !important;
          background: rgba(255, 255, 255, 0.1) !important;
          border-radius: 50% !important;
        }
        .mapbox-visitor-marker {
          will-change: transform;
        }
        .mapboxgl-canvas {
          outline: none !important;
        }
      `}</style>
    </div>
  );
}
