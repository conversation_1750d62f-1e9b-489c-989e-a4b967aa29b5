import { expect, test } from "@playwright/test"

test.describe("Homepage", () => {
  test("should load homepage successfully", async ({ page }) => {
    await page.goto("/")

    // Check if the main heading is visible
    await expect(
      page.getByRole("heading", {
        name: /Transform Website Visitors Into Revenue Insights/i,
      })
    ).toBeVisible()

    // Check if the DataFast logo is visible
    await expect(page.getByText("DataFast")).toBeVisible()

    // Check if navigation buttons are present
    await expect(page.getByRole("link", { name: "Sign In" })).toBeVisible()
    await expect(page.getByRole("link", { name: "Get Started" })).toBeVisible()
  })

  test("should have proper meta tags", async ({ page }) => {
    await page.goto("/")

    // Check page title
    await expect(page).toHaveTitle(/DataFast/)

    // Check that there are no console errors
    const logs = []
    page.on("console", msg => {
      if (msg.type() === "error") {
        logs.push(msg.text())
      }
    })

    await page.waitForLoadState("networkidle")
    expect(logs).toHaveLength(0)
  })

  test("should navigate to sign in page", async ({ page }) => {
    await page.goto("/")

    // Click on sign in button
    await page.getByRole("link", { name: "Sign In" }).click()

    // Should navigate to sign in page
    await expect(page).toHaveURL(/\/auth\/signin/)
  })

  test("should have responsive design", async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.goto("/")
    await expect(page.getByText("DataFast")).toBeVisible()

    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto("/")
    await expect(page.getByText("DataFast")).toBeVisible()

    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.goto("/")
    await expect(page.getByText("DataFast")).toBeVisible()
  })

  test("should have proper performance metrics", async ({ page }) => {
    await page.goto("/")

    // Wait for page to fully load
    await page.waitForLoadState("networkidle")

    // Get performance metrics
    const metrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType(
        "navigation"
      )[0] as PerformanceNavigationTiming
      return {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded:
          navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        firstByte: navigation.responseStart - navigation.requestStart,
      }
    })

    // Assert reasonable performance
    expect(metrics.loadTime).toBeLessThan(3000) // Less than 3 seconds
    expect(metrics.firstByte).toBeLessThan(1000) // Less than 1 second TTFB
  })

  test("should have accessible content", async ({ page }) => {
    await page.goto("/")

    // Check for proper heading hierarchy
    const h1Count = await page.locator("h1").count()
    expect(h1Count).toBe(1) // Should have exactly one h1

    // Check for alt text on images (if any)
    const images = page.locator("img")
    const imageCount = await images.count()

    for (let i = 0; i < imageCount; i++) {
      const image = images.nth(i)
      const alt = await image.getAttribute("alt")
      expect(alt).toBeTruthy() // All images should have alt text
    }

    // Check for proper link text
    const links = page.locator("a")
    const linkCount = await links.count()

    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i)
      const text = await link.textContent()
      const ariaLabel = await link.getAttribute("aria-label")

      // Links should have either visible text or aria-label
      expect(text || ariaLabel).toBeTruthy()
    }
  })
})
