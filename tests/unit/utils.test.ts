import { sanitizeHtml, sanitizeInput, validateField } from "@/lib/security/validation"
import { cn } from "@/lib/utils"
import { formatDateForDisplay, getRelativeTimeString, isValidDateRange } from "@/lib/utils/date"
// Unit tests for utility functions
import { describe, expect, it } from "@jest/globals"

describe("Utility Functions", () => {
  describe("cn (className utility)", () => {
    it("should merge classes correctly", () => {
      expect(cn("base", "additional")).toBe("base additional")
    })

    it("should handle conditional classes", () => {
      expect(cn("base", true && "conditional", false && "hidden")).toBe("base conditional")
    })
  })

  describe("Date Utilities", () => {
    describe("formatDateForDisplay", () => {
      it("should format date correctly", () => {
        const date = new Date("2024-01-15T12:00:00Z")
        const formatted = formatDateForDisplay(date)
        expect(formatted).toMatch(/Jan 15, 2024/)
      })
    })

    describe("getRelativeTimeString", () => {
      it('should return "just now" for recent dates', () => {
        const recent = new Date(Date.now() - 30000) // 30 seconds ago
        expect(getRelativeTimeString(recent)).toBe("30s ago")
      })

      it("should return minutes for older dates", () => {
        const minutes = new Date(Date.now() - 5 * 60 * 1000) // 5 minutes ago
        expect(getRelativeTimeString(minutes)).toBe("5m ago")
      })
    })

    describe("isValidDateRange", () => {
      it("should validate correct date ranges", () => {
        const from = new Date("2024-01-01")
        const to = new Date("2024-01-31")
        expect(isValidDateRange(from, to)).toBe(true)
      })

      it("should reject invalid date ranges", () => {
        const from = new Date("2024-01-31")
        const to = new Date("2024-01-01")
        expect(isValidDateRange(from, to)).toBe(false)
      })
    })
  })

  describe("Security Validation", () => {
    describe("validateField", () => {
      it("should validate required fields", () => {
        const errors = validateField("", "testField", {
          type: "string",
          required: true,
        })
        expect(errors).toContain("testField is required")
      })

      it("should validate string length", () => {
        const errors = validateField("ab", "testField", {
          type: "string",
          required: true,
          minLength: 5,
        })
        expect(errors).toContain("testField must be at least 5 characters")
      })

      it("should validate email format", () => {
        const errors = validateField("invalid-email", "email", {
          type: "email",
          required: true,
        })
        expect(errors).toContain("email must be a valid email address")

        const validErrors = validateField("<EMAIL>", "email", {
          type: "email",
          required: true,
        })
        expect(validErrors).toHaveLength(0)
      })

      it("should validate number ranges", () => {
        const errors = validateField(150, "age", {
          type: "number",
          min: 0,
          max: 120,
        })
        expect(errors).toContain("age must be no more than 120")
      })
    })

    describe("sanitizeInput", () => {
      it("should remove dangerous characters", () => {
        const input = '<script>alert("xss")</script>Hello'
        const sanitized = sanitizeInput(input)
        expect(sanitized).not.toContain("<script>")
        expect(sanitized).not.toContain("</script>")
      })

      it("should remove javascript protocols", () => {
        const input = 'javascript:alert("xss")'
        const sanitized = sanitizeInput(input)
        expect(sanitized).not.toContain("javascript:")
      })

      it("should trim whitespace", () => {
        const input = "  hello world  "
        const sanitized = sanitizeInput(input)
        expect(sanitized).toBe("hello world")
      })
    })

    describe("sanitizeHtml", () => {
      it("should allow safe HTML tags", () => {
        const html = "<p>Safe <strong>content</strong></p>"
        const sanitized = sanitizeHtml(html)
        expect(sanitized).toContain("<p>")
        expect(sanitized).toContain("<strong>")
      })

      it("should remove dangerous HTML", () => {
        const html = '<script>alert("xss")</script><p>Safe content</p>'
        const sanitized = sanitizeHtml(html)
        expect(sanitized).not.toContain("<script>")
        expect(sanitized).toContain("<p>Safe content</p>")
      })
    })
  })
})
