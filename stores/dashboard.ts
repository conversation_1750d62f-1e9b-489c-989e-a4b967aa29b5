import { create } from "zustand"
import { devtools } from "zustand/middleware"

export interface DashboardMetrics {
  visitors: number
  pageviews: number
  bounceRate: number
  avgSessionDuration: number
  revenue: number
  conversions: number
}

export interface Website {
  id: string
  name: string
  domain: string
  trackingId: string
  isActive: boolean
}

interface DashboardStore {
  selectedWebsite: Website | null
  metrics: DashboardMetrics | null
  isLoading: boolean
  dateRange: {
    from: Date
    to: Date
  }

  // Actions
  setSelectedWebsite: (website: Website | null) => void
  setMetrics: (metrics: DashboardMetrics) => void
  setLoading: (loading: boolean) => void
  setDateRange: (range: { from: Date; to: Date }) => void
}

export const useDashboardStore = create<DashboardStore>()(
  devtools(
    set => ({
      selectedWebsite: null,
      metrics: null,
      isLoading: false,
      dateRange: {
        from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        to: new Date(),
      },

      setSelectedWebsite: website => set({ selectedWebsite: website }),
      setMetrics: metrics => set({ metrics }),
      setLoading: loading => set({ isLoading: loading }),
      setDateRange: range => set({ dateRange: range }),
    }),
    {
      name: "dashboard-store",
    }
  )
)
