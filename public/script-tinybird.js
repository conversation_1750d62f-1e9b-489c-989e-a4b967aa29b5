/**
 * InstaSight 追踪脚本 - Tinybird 版本
 * 优化后的轻量级脚本，直接发送数据到 Tinybird
 */
!(() => {
  // 配置
  const config = {
    tinybirdUrl: "https://api.tinybird.co/v0/events?name=events",
    tinybirdToken: document.currentScript?.getAttribute("data-tinybird-token"),
    websiteId: document.currentScript?.getAttribute("data-website-id"),
    domain: document.currentScript?.getAttribute("data-domain"),
    auto: document.currentScript?.getAttribute("data-auto") !== "false",
    debug: document.currentScript?.getAttribute("data-debug") === "true",
    batchSize: parseInt(
      document.currentScript?.getAttribute("data-batch-size") || "5"
    ),
    flushInterval: parseInt(
      document.currentScript?.getAttribute("data-flush-interval") || "3000"
    ),
  };

  // 验证必需参数
  if (!config.websiteId) {
    console.error("InstaSight: data-website-id is required");
    return;
  }

  if (!config.tinybirdToken) {
    console.error("InstaSight: data-tinybird-token is required");
    return;
  }

  // Bot 检测
  const isBot = () => {
    const botPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /lighthouse/i,
      /gtmetrix/i,
      /pingdom/i,
      /prerender/i,
      /headless/i,
      /phantom/i,
      /selenium/i,
    ];
    return botPatterns.some((pattern) => pattern.test(navigator.userAgent));
  };

  if (isBot()) return;

  // 工具函数
  const getCookie = (name) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(";").shift();
    return null;
  };

  const setCookie = (name, value, days = 365) => {
    const expires = new Date(
      Date.now() + days * 24 * 60 * 60 * 1000
    ).toUTCString();
    document.cookie = `${name}=${value}; expires=${expires}; path=/; SameSite=Strict`;
  };

  const generateId = () => {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  const getOrCreateVisitorId = () => {
    let visitorId = getCookie("_df_visitor_id");
    if (!visitorId) {
      visitorId = generateId();
      setCookie("_df_visitor_id", visitorId);
    }
    return visitorId;
  };

  const getOrCreateSessionId = () => {
    let sessionId = sessionStorage.getItem("_df_session_id");
    if (!sessionId) {
      sessionId = generateId();
      sessionStorage.setItem("_df_session_id", sessionId);
    }
    return sessionId;
  };

  // 设备信息检测
  const getDeviceInfo = () => {
    const ua = navigator.userAgent;
    let device = "Desktop";
    let browser = "Unknown";
    let os = "Unknown";

    // 设备检测
    if (
      /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua)
    ) {
      device = /iPad/i.test(ua) ? "Tablet" : "Mobile";
    }

    // 浏览器检测
    if (ua.includes("Chrome") && !ua.includes("Edge")) browser = "Chrome";
    else if (ua.includes("Firefox")) browser = "Firefox";
    else if (ua.includes("Safari") && !ua.includes("Chrome"))
      browser = "Safari";
    else if (ua.includes("Edge")) browser = "Edge";
    else if (ua.includes("Opera")) browser = "Opera";

    // 操作系统检测
    if (ua.includes("Windows")) os = "Windows";
    else if (ua.includes("Mac")) os = "macOS";
    else if (ua.includes("Linux")) os = "Linux";
    else if (ua.includes("Android")) os = "Android";
    else if (ua.includes("iOS")) os = "iOS";

    return { device, browser, os };
  };

  // UTM 参数提取
  const getUtmParams = () => {
    const params = new URLSearchParams(window.location.search);
    return {
      utm_source: params.get("utm_source"),
      utm_medium: params.get("utm_medium"),
      utm_campaign: params.get("utm_campaign"),
      utm_content: params.get("utm_content"),
      utm_term: params.get("utm_term"),
    };
  };

  // 支付检测
  const detectPayment = () => {
    const url = window.location.href;
    const searchParams = new URLSearchParams(window.location.search);

    // 检测成功页面模式
    const successPatterns = [
      /\/success\/?$/i,
      /\/thank-you\/?$/i,
      /\/checkout\/success\/?$/i,
      /\/payment\/success\/?$/i,
    ];

    const isSuccessPage = successPatterns.some((pattern) => pattern.test(url));

    if (isSuccessPage || searchParams.get("payment") === "success") {
      // 尝试从 URL 参数中提取收入信息
      const amount =
        searchParams.get("amount") ||
        searchParams.get("total") ||
        searchParams.get("value");
      return {
        provider: searchParams.get("provider") || "unknown",
        success: true,
        amount: amount ? parseFloat(amount) : null,
      };
    }

    return null;
  };

  // 数据验证和清理
  const sanitizeData = (data) => {
    if (!data || typeof data !== "object") return {};

    const sanitized = {};
    const maxFields = 10;
    const maxFieldNameLength = 50;
    const maxFieldValueLength = 500;

    let fieldCount = 0;
    for (const [key, value] of Object.entries(data)) {
      if (fieldCount >= maxFields) break;

      // 验证字段名
      if (typeof key !== "string" || key.length > maxFieldNameLength) continue;
      if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(key)) continue;

      // 验证字段值
      if (value === null || value === undefined) continue;
      const stringValue = String(value);
      if (stringValue.length > maxFieldValueLength) continue;

      // XSS 防护
      const sanitizedValue = stringValue
        .replace(/[<>'"&]/g, "")
        .replace(/javascript:/gi, "")
        .replace(/data:/gi, "");

      sanitized[key] = sanitizedValue;
      fieldCount++;
    }

    return sanitized;
  };

  // 事件队列
  class EventQueue {
    constructor() {
      this.queue = [];
      this.isFlushInProgress = false;
      this.flushTimer = null;
      this.startFlushTimer();
    }

    enqueue(event) {
      this.queue.push(event);

      if (this.queue.length >= config.batchSize) {
        this.flush();
      }
    }

    async flush() {
      if (this.queue.length === 0 || this.isFlushInProgress) return;

      this.isFlushInProgress = true;
      const eventsToSend = [...this.queue];
      this.queue = [];

      try {
        // 将事件转换为 NDJSON 格式
        const ndjsonData = eventsToSend
          .map((event) => JSON.stringify(event))
          .join("\n");

        const response = await fetch(config.tinybirdUrl, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${config.tinybirdToken}`,
            "Content-Type": "application/json",
          },
          body: ndjsonData,
          keepalive: true,
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        if (config.debug) {
          console.log(
            `InstaSight: Sent ${eventsToSend.length} events to Tinybird`
          );
        }
      } catch (error) {
        if (config.debug) {
          console.error("InstaSight: Failed to send events:", error);
        }
        // 将失败的事件重新加入队列前端（可选择重试逻辑）
        this.queue.unshift(...eventsToSend);
      } finally {
        this.isFlushInProgress = false;
      }
    }

    startFlushTimer() {
      this.flushTimer = setInterval(() => {
        this.flush();
      }, config.flushInterval);
    }

    stop() {
      if (this.flushTimer) {
        clearInterval(this.flushTimer);
        this.flushTimer = null;
      }
      this.flush();
    }
  }

  const eventQueue = new EventQueue();

  // 事件追踪
  const track = (eventType, eventName, data = {}) => {
    try {
      const deviceInfo = getDeviceInfo();
      const utmParams = getUtmParams();
      const paymentInfo = detectPayment();

      const eventData = {
        id: generateId(),
        website_id: config.websiteId,
        visitor_id: getOrCreateVisitorId(),
        session_id: getOrCreateSessionId(),
        event_type: eventType || "pageview",
        event_name: eventName || null,
        url: window.location.href,
        referrer: document.referrer || null,
        user_agent: navigator.userAgent,
        ...deviceInfo,
        ...utmParams,
        custom_data:
          Object.keys(data).length > 0
            ? JSON.stringify(sanitizeData(data))
            : null,
        revenue: null,
        timestamp: new Date().toISOString(),
      };

      // 添加支付信息
      if (paymentInfo && paymentInfo.success) {
        eventData.event_type = "payment";
        eventData.event_name = "payment_success";
        if (paymentInfo.amount) {
          eventData.revenue = paymentInfo.amount;
        }
        eventData.custom_data = JSON.stringify({
          ...sanitizeData(data),
          payment_provider: paymentInfo.provider,
        });
      }

      if (config.debug) {
        console.log("InstaSight Event:", eventData);
      }

      // 将事件加入队列
      eventQueue.enqueue(eventData);
    } catch (error) {
      if (config.debug) {
        console.error("InstaSight Tracking Error:", error);
      }
    }
  };

  // 页面浏览追踪
  const trackPageView = () => {
    track("pageview", "page_view");
  };

  // 外部链接追踪
  const trackExternalLinks = () => {
    document.addEventListener("click", (e) => {
      const link = e.target.closest("a");
      if (!link) return;

      const href = link.getAttribute("href");
      if (!href) return;

      const isExternal =
        href.startsWith("http") && !href.includes(window.location.hostname);

      if (isExternal) {
        track("custom", "external_link_click", {
          url: href,
          text: link.textContent?.trim() || "",
        });
      }
    });
  };

  // 表单提交追踪
  const trackFormSubmissions = () => {
    document.addEventListener("submit", (e) => {
      const form = e.target;
      if (form.tagName === "FORM") {
        const formData = new FormData(form);
        const data = {};

        // 只追踪非敏感表单字段
        for (const [key, value] of formData.entries()) {
          const lowerKey = key.toLowerCase();
          if (
            !lowerKey.includes("password") &&
            !lowerKey.includes("token") &&
            !lowerKey.includes("secret") &&
            !lowerKey.includes("credit") &&
            !lowerKey.includes("card")
          ) {
            data[key] = value?.toString().substring(0, 100);
          }
        }

        track("custom", "form_submit", {
          form_id: form.id || "",
          form_action: form.action || "",
          ...data,
        });
      }
    });
  };

  // SPA 路由变化检测
  const trackRouteChanges = () => {
    let currentPath = window.location.pathname;

    const checkRouteChange = () => {
      if (window.location.pathname !== currentPath) {
        currentPath = window.location.pathname;
        trackPageView();
      }
    };

    // 监听浏览器导航
    window.addEventListener("popstate", checkRouteChange);

    // 覆盖 pushState 和 replaceState 以检测 SPA 导航
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function (...args) {
      originalPushState.apply(this, args);
      setTimeout(checkRouteChange, 0);
    };

    history.replaceState = function (...args) {
      originalReplaceState.apply(this, args);
      setTimeout(checkRouteChange, 0);
    };
  };

  // 页面卸载时刷新队列
  const handlePageUnload = () => {
    eventQueue.flush();
  };

  // 公开 API（向后兼容 datafast 名称）
  const api = {
    track: (eventName, data) => track("custom", eventName, data),
    trackPageView,
    trackConversion: (value, currency = "USD") =>
      track("conversion", "conversion", { value, currency }),
    trackRevenue: (amount, currency = "USD") =>
      track("payment", "revenue", { amount, currency }),
    identify: (userId, traits = {}) => {
      setCookie("_df_user_id", userId);
      track("custom", "user_identify", { user_id: userId, ...traits });
    },
    flush: () => eventQueue.flush(),
  };
  window.instasight = api;

  // 初始化
  const init = () => {
    if (config.auto) {
      trackPageView();
      trackExternalLinks();
      trackFormSubmissions();
      trackRouteChanges();
    }

    // 监听页面卸载
    window.addEventListener("beforeunload", handlePageUnload);
    window.addEventListener("visibilitychange", () => {
      if (document.visibilityState === "hidden") {
        handlePageUnload();
      }
    });

    if (config.debug) {
      console.log("InstaSight (Tinybird) initialized:", config);
    }
  };

  // 启动
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", init);
  } else {
    init();
  }
})();
