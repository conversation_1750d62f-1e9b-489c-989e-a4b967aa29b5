(()=>{const t={endpoint:"/api/events",websiteId:document.currentScript?.getAttribute("data-website-id"),domain:document.currentScript?.getAttribute("data-domain"),auto:"false"!==document.currentScript?.getAttribute("data-auto"),debug:"true"===document.currentScript?.getAttribute("data-debug")},e=document.currentScript?.getAttribute("data-endpoint");if(e&&(t.endpoint=e),!t.websiteId)return void console.error("InstaSight: data-website-id is required");const n=(()=>{try{return window!==window.parent}catch{return!0}})(),r=[/bot/i,/crawler/i,/spider/i,/scraper/i,/lighthouse/i,/gtmetrix/i,/pingdom/i,/prerender/i].some(t=>t.test(navigator.userAgent))||(()=>{try{const t=navigator.userAgent?.toLowerCase?.()||"";return!0===navigator.webdriver||/headlesschrome|phantomjs|selenium|webdriver|puppeteer|playwright|curl|wget|postman|python|axios|java\//i.test(t)}catch{return!1}})()||n&&!t.debug||"true"===localStorage.getItem("instasight_ignore"),i="1"===navigator.doNotTrack||"1"===window.doNotTrack,o=!0===navigator.globalPrivacyControl,a=document.currentScript?.getAttribute("data-consent"),s=!a||"true"===a,c=(t,e,n=365)=>{const r=new Date(Date.now()+24*n*60*60*1e3).toUTCString(),i=document.currentScript?.getAttribute("data-domain"),o=i?.replace(/^\./,""),a=o&&location.hostname.endsWith(o)?`; domain=.${o}`:"";document.cookie=`${t}=${e}; expires=${r}${a}; path=/; SameSite=Strict`},d=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}),u=()=>{let t=(t=>{const e=`; ${document.cookie}`.split(`; ${t}=`);return 2===e.length?e.pop().split(";").shift():null})("_df_visitor_id");return t||(t=d(),c("_df_visitor_id",t)),t},g=()=>{let t=sessionStorage.getItem("_df_session_id");return t||(t=d(),sessionStorage.setItem("_df_session_id",t)),t},m=()=>{const t=navigator.userAgent;return{viewport:`${Math.max(document.documentElement.clientWidth||0,window.innerWidth||0)}x${Math.max(document.documentElement.clientHeight||0,window.innerHeight||0)}`,userAgent:t,language:navigator.language,platform:navigator.platform}},l=t=>{const e={};if(t&&"object"==typeof t){let n=0;for(const[r,i]of Object.entries(t)){if(n>=10)break;if("string"!=typeof r||r.length>50)continue;if(!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(r))continue;if(null==i)continue;const t=String(i);if(t.length>500)continue;const o=t.replace(/[<>'"&]/g,"").replace(/javascript:/gi,"").replace(/data:/gi,"");e[r]=o,n++}}return e},p=e=>{try{const n=JSON.stringify(e);fetch(t.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:n,keepalive:!0}).catch(e=>{t.debug&&console.error("InstaSight Error:",e)})}catch(e){t.debug&&console.error("InstaSight Send Error:",e)}},h=(e,n,a={})=>{try{if(r)return;if(i||o||!s)return;const c=m(),d=(()=>{const t=new URLSearchParams(window.location.search);return{utm_source:t.get("utm_source"),utm_medium:t.get("utm_medium"),utm_campaign:t.get("utm_campaign"),utm_content:t.get("utm_content"),utm_term:t.get("utm_term")}})(),h=(()=>{const t=window.location.href,e=new URLSearchParams(window.location.search);return t.includes("/success")||t.includes("/thank-you")||"success"===e.get("payment")||"success"===e.get("stripe")?{provider:"stripe",success:!0}:t.includes("lemonsqueezy")&&t.includes("success")?{provider:"lemonsqueezy",success:!0}:t.includes("polar")&&t.includes("success")?{provider:"polar",success:!0}:null})(),w={websiteId:t.websiteId,visitorId:u(),sessionId:g(),eventType:e,eventName:n,url:window.location.href,referrer:document.referrer||"",timestamp:(new Date).toISOString(),...c,...d,customData:l(a)};h&&(w.paymentProvider=h.provider,w.paymentSuccess=h.success),t.debug&&console.log("InstaSight Event:",w),p(w)}catch(e){t.debug&&console.error("InstaSight Tracking Error:",e)}},w=()=>{h("pageview","page_view")},f=()=>{let t=window.location.pathname;const e=()=>{window.location.pathname!==t&&(t=window.location.pathname,w())};window.addEventListener("popstate",e);const n=history.pushState,r=history.replaceState;history.pushState=function(...t){n.apply(this,t),setTimeout(e,0)},history.replaceState=function(...t){r.apply(this,t),setTimeout(e,0)}},v={track:(t,e)=>h("custom",t,e),trackPageView:w,trackConversion:(t,e="USD")=>h("conversion","conversion",{value:t,currency:e}),trackRevenue:(t,e="USD")=>h("payment","revenue",{amount:t,currency:e}),identify:(t,e={})=>{c("_df_user_id",t),h("custom","user_identify",{userId:t,...e})}},y=window.instasight&&Array.isArray(window.instasight.q)?[...window.instasight.q]:[];if(window.instasight=v,y.length)try{for(const t of y)Array.isArray(t)&&v.track(t[0],t[1])}catch{}const S=()=>{t.auto&&!r&&(w(),document.addEventListener("click",t=>{const e=t.target.closest?.("a");if(!e)return;const n=e.getAttribute("href");n&&n.startsWith("http")&&!n.includes(window.location.hostname)&&h("custom","external_link_click",{url:n,text:e.textContent?.trim()||""})}),document.addEventListener("submit",t=>{const e=t.target;if("FORM"===e.tagName){const t=new FormData(e),n={};for(const[e,r]of t.entries())e.toLowerCase().includes("password")||e.toLowerCase().includes("token")||e.toLowerCase().includes("secret")||(n[e]=r?.toString().substring(0,100));h("custom","form_submit",{formId:e.id||"",formAction:e.action||"",...n})}}),f()),t.debug&&console.log("InstaSight initialized:",t)},b=()=>{try{if(r)return;if(i||o||!s)return;const e=m();(e=>{try{const n=JSON.stringify(e);let r=!1;if(navigator.sendBeacon)try{r=navigator.sendBeacon(t.endpoint,new Blob([n],{type:"application/json"}))}catch{}r||p(e)}catch(e){t.debug&&console.error("InstaSight Beacon Error:",e)}})({websiteId:t.websiteId,visitorId:u(),sessionId:g(),eventType:"custom",eventName:"heartbeat",url:window.location.href,referrer:document.referrer||"",timestamp:(new Date).toISOString(),...e,customData:{reason:"page_hide"}})}catch(e){t.debug&&console.error("InstaSight Heartbeat Error:",e)}};"loading"===document.readyState?document.addEventListener("DOMContentLoaded",S):S(),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&b()}),window.addEventListener("pagehide",b)})();