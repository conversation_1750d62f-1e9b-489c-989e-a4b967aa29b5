(()=>{const e={endpoint:"http://localhost:3000/api/events",presenceEndpoint:"http://localhost:3000/api/presence/enter",heartbeatEndpoint:"http://localhost:3000/api/presence/heartbeat",websiteId:document.currentScript?.getAttribute("data-website-id"),domain:document.currentScript?.getAttribute("data-domain"),auto:"false"!==document.currentScript?.getAttribute("data-auto"),debug:"true"===document.currentScript?.getAttribute("data-debug")},t=document.currentScript?.getAttribute("data-endpoint");t&&(e.endpoint=t);const n=document.currentScript?.getAttribute("data-track-idle");e.trackIdle=!!n&&"false"!==n;const i=document.currentScript?.getAttribute("data-idle-threshold");if(e.idleThreshold=i?Number.parseInt(i,10):12e4,!e.websiteId)return;const r=(()=>{try{return window!==window.parent}catch{return!0}})(),o=[/bot/i,/crawler/i,/spider/i,/scraper/i,/lighthouse/i,/gtmetrix/i,/pingdom/i,/prerender/i].some(e=>e.test(navigator.userAgent))||(()=>{try{const e=navigator.userAgent?.toLowerCase?.()||"";return!0===navigator.webdriver||/headlesschrome|phantomjs|selenium|webdriver|puppeteer|playwright|curl|wget|postman|python|axios|java\//i.test(e)}catch{return!1}})()||r&&!e.debug||"true"===localStorage.getItem("instasight_ignore"),a="1"===navigator.doNotTrack||"1"===window.doNotTrack,s=!0===navigator.globalPrivacyControl,c=document.currentScript?.getAttribute("data-consent"),d=!c||"true"===c,u=(e,t,n=365)=>{const i=new Date(Date.now()+24*n*60*60*1e3).toUTCString(),r=document.currentScript?.getAttribute("data-domain"),o=r?.replace(/^\./,""),a=o&&location.hostname.endsWith(o)?`; domain=.${o}`:"";document.cookie=`${e}=${t}; expires=${i}${a}; path=/; SameSite=Strict`},l=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}),h=()=>{let e=(e=>{const t=`; ${document.cookie}`.split(`; ${e}=`);return 2===t.length?t.pop().split(";").shift():null})("_instasight_visitor_id");return e||(e=l(),u("_instasight_visitor_id",e)),e},m=()=>{let e=sessionStorage.getItem("_instasight_session_id");return e||(e=l(),sessionStorage.setItem("_instasight_session_id",e)),e},p=()=>{const e=navigator.userAgent;return{viewport:`${Math.max(document.documentElement.clientWidth||0,window.innerWidth||0)}x${Math.max(document.documentElement.clientHeight||0,window.innerHeight||0)}`,userAgent:e,language:navigator.language,platform:navigator.platform}},g=e=>{const t={};if(e&&"object"==typeof e){let n=0;for(const[i,r]of Object.entries(e)){if(n>=10)break;if("string"!=typeof i||i.length>50)continue;if(!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(i))continue;if(null==r)continue;const e=String(r);if(e.length>500)continue;const o=e.replace(/[<>'"&]/g,"").replace(/javascript:/gi,"").replace(/data:/gi,"");t[i]=o,n++}}return t},w=t=>{try{const n=JSON.stringify(t);fetch(e.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:n,keepalive:!0}).catch(t=>{e.debug})}catch(t){e.debug}},f=(t,n,i={})=>{try{if(o)return;if(a||s||!d)return;const r=p(),c=(()=>{const e=new URLSearchParams(window.location.search);return{utm_source:e.get("utm_source"),utm_medium:e.get("utm_medium"),utm_campaign:e.get("utm_campaign"),utm_content:e.get("utm_content"),utm_term:e.get("utm_term")}})(),u={websiteId:e.websiteId,visitorId:h(),sessionId:m(),eventType:t,eventName:n,url:window.location.href,referrer:document.referrer||"",timestamp:(new Date).toISOString(),...r,...c,customData:g(i)};e.debug,w(u)}catch(t){e.debug}},v=()=>{f("pageview","page_view")},b=()=>{let e=window.location.pathname;const t=()=>{window.location.pathname!==e&&(e=window.location.pathname,v())};window.addEventListener("popstate",t);const n=history.pushState,i=history.replaceState;history.pushState=function(...e){n.apply(this,e),setTimeout(t,0)},history.replaceState=function(...e){i.apply(this,e),setTimeout(t,0)}},y={track:(e,t)=>f("custom",e,t),trackPageView:v,trackConversion:(e,t="USD")=>f("conversion","conversion",{value:e,currency:t}),trackRevenue:(e,t="USD")=>f("payment","revenue",{amount:e,currency:t}),identify:(e,t={})=>{u("_df_user_id",e),f("custom","user_identify",{userId:e,...t})}},S=window.instasight&&Array.isArray(window.instasight.q)?[...window.instasight.q]:[];if(window.instasight=y,e.debug&&(window.sendPresenceDisconnect=_,window.getCurrentSessionToken=()=>x,window.setCurrentSessionToken=e=>{x=e},window.testDisconnectForced=()=>{const t={sessionToken:"test-token-123"},n=`${e.heartbeatEndpoint.replace("/heartbeat","/leave")}`,i=JSON.stringify(t);if(navigator.sendBeacon){navigator.sendBeacon(n,i)}else fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:i,keepalive:!0}).then(e=>e.json()).then(e=>{}).catch(e=>{})}),S.length)try{for(const e of S)Array.isArray(e)&&y.track(e[0],e[1])}catch{}let I=null,x=null;const T=25e3;function k(){try{if(o)return;if(a||s||!d)return;const t=p(),n={trackingId:e.websiteId,visitorId:h(),sessionId:m(),interval:T,url:window.location.href,referrer:document.referrer||"",device:t.device||"",browser:t.browser||"",os:t.os||"",screen:t.screen||"",timestamp:(new Date).toISOString()};fetch(`${e.heartbeatEndpoint}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n),keepalive:!0}).then(e=>e.json()).then(e=>{e&&e.sessionToken&&(x=e.sessionToken)}).catch(()=>{})}catch{}}function _(){try{if(!x)return void function(){try{const t=p(),n={trackingId:e.websiteId,visitorId:h(),sessionId:m(),interval:T,url:window.location.href,referrer:document.referrer||"",device:t.device||"",browser:t.browser||"",os:t.os||"",screen:t.screen||"",timestamp:(new Date).toISOString()};fetch(`${e.heartbeatEndpoint}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n),keepalive:!0}).then(e=>e.json()).then(t=>{if(t&&t.sessionToken){const n={sessionToken:t.sessionToken},i=`${e.heartbeatEndpoint.replace("/heartbeat","/leave")}`,r=JSON.stringify(n);if(navigator.sendBeacon){navigator.sendBeacon(i,r)}else fetch(i,{method:"POST",headers:{"Content-Type":"application/json"},body:r,keepalive:!0}).catch(()=>{})}}).catch(e=>{})}catch(e){}}();if(o)return;if(a||s||!d)return;try{fetch(e.endpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({websiteId:e.websiteId,visitorId:h(),sessionId:m(),eventType:"custom",eventName:"disconnect_attempt",url:window.location.href,timestamp:(new Date).toISOString()}),keepalive:!0}).catch(()=>{})}catch{}const t={sessionToken:x},n=JSON.stringify(t);let i=!1;if(navigator.sendBeacon){const t=`${e.heartbeatEndpoint.replace("/heartbeat","/leave")}`;try{i=navigator.sendBeacon(t,n)}catch(e){}}i||fetch(`${e.heartbeatEndpoint.replace("/heartbeat","/leave")}`,{method:"POST",headers:{"Content-Type":"application/json"},body:n,keepalive:!0}).catch(e=>{}),x=null}catch(e){}}function O(){if(null!=I)return;I=setInterval(()=>{!function(){try{if(o)return;if(a||s||!d)return;const t=p(),n={websiteId:e.websiteId,visitorId:h(),sessionId:m(),eventType:"custom",eventName:"heartbeat",url:window.location.href,referrer:document.referrer||"",timestamp:(new Date).toISOString(),...t,customData:{reason:"interval"}};w(n)}catch{}}(),k()},T)}const A=()=>{e.auto&&!o&&(v(),document.addEventListener("click",e=>{const t=e.target.closest?.("a");if(!t)return;const n=t.getAttribute("href");n&&n.startsWith("http")&&!n.includes(window.location.hostname)&&f("custom","external_link_click",{url:n,text:t.textContent?.trim()||""})}),document.addEventListener("submit",e=>{const t=e.target;if("FORM"===t.tagName){const e=new FormData(t),n={};for(const[t,i]of e.entries())t.toLowerCase().includes("password")||t.toLowerCase().includes("token")||t.toLowerCase().includes("secret")||(n[t]=i?.toString().substring(0,100));f("custom","form_submit",{formId:t.id||"",formAction:t.action||"",...n})}}),b(),function(){try{if(o)return;if(a||s||!d)return;const t=p(),n={trackingId:e.websiteId,visitorId:h(),sessionId:m(),url:window.location.href,referrer:document.referrer||"",device:t.device||"",browser:t.browser||"",os:t.os||"",screen:t.screen||"",timestamp:(new Date).toISOString()};fetch(`${e.presenceEndpoint}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n),keepalive:!0}).catch(e=>{})}catch{}}(),O(),e.trackIdle&&j()),e.debug},E=()=>{try{if(o)return;if(a||s||!d)return;const t=p();(t=>{try{const n=JSON.stringify(t);let i=!1;if(navigator.sendBeacon)try{i=navigator.sendBeacon(e.endpoint,n)}catch(t){e.debug}i||w(t)}catch(t){e.debug}})({websiteId:e.websiteId,visitorId:h(),sessionId:m(),eventType:"custom",eventName:"heartbeat",url:window.location.href,referrer:document.referrer||"",timestamp:(new Date).toISOString(),...t,customData:{reason:"page_hide"}})}catch(t){e.debug}};"loading"===document.readyState?document.addEventListener("DOMContentLoaded",A):A(),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState?(E(),null!=I&&(clearInterval(I),I=null),_()):"visible"===document.visibilityState&&(k(),O())}),window.addEventListener("pagehide",()=>{E(),_()}),window.addEventListener("beforeunload",()=>{_()});let C=null,D=!1;const N=["mousemove","mousedown","keydown","touchstart","scroll"],$=()=>{try{if(o)return;if(a||s||!d)return;D&&(D=!1,f("custom","idle_end")),C&&clearTimeout(C),C=setTimeout(()=>{D=!0,f("custom","idle_start")},e.idleThreshold)}catch{}},j=()=>{try{$();for(const e of N)window.addEventListener(e,$,{passive:!0});document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState?(D=!0,f("custom","idle_start"),C&&(clearTimeout(C),C=null)):"visible"===document.visibilityState&&$()}),window.addEventListener("pagehide",()=>{D=!0,f("custom","idle_start"),C&&(clearTimeout(C),C=null)})}catch{}}})();