!(() => {
  // Configuration
  const config = {
    endpoint: `http://localhost:3000/api/events`,
    presenceEndpoint: `http://localhost:3000/api/presence/enter`,
    heartbeatEndpoint: `http://localhost:3000/api/presence/heartbeat`,
    websiteId: document.currentScript?.getAttribute("data-website-id"),
    domain: document.currentScript?.getAttribute("data-domain"),
    auto: document.currentScript?.getAttribute("data-auto") !== "false",
    debug: document.currentScript?.getAttribute("data-debug") === "true",
  };
  console.log(config);

  // Allow overriding endpoint via data-endpoint (CDN/edge/staging)
  const endpointAttr = document.currentScript?.getAttribute("data-endpoint");
  if (endpointAttr) {
    config.endpoint = endpointAttr;
  }

  // Idle tracking configuration (optional, opt-in)
  const trackIdleAttr = document.currentScript?.getAttribute("data-track-idle");
  config.trackIdle = trackIdleAttr ? trackIdleAttr !== "false" : false;
  const idleThresholdAttr = document.currentScript?.getAttribute(
    "data-idle-threshold"
  );
  config.idleThreshold = idleThresholdAttr
    ? Number.parseInt(idleThresholdAttr, 10)
    : 120000; // 120s

  // Validation
  if (!config.websiteId) {
    console.error("InstaSight: data-website-id is required");
    return;
  }

  // Bot detection
  const isBot = () => {
    const botPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /lighthouse/i,
      /gtmetrix/i,
      /pingdom/i,
      /prerender/i,
    ];
    return botPatterns.some((pattern) => pattern.test(navigator.userAgent));
  };

  // Extended automation checks and iframe protection (unless debug)
  const isAutomation = () => {
    try {
      const ua = navigator.userAgent?.toLowerCase?.() || "";
      return (
        navigator.webdriver === true ||
        /headlesschrome|phantomjs|selenium|webdriver|puppeteer|playwright|curl|wget|postman|python|axios|java\//i.test(
          ua
        )
      );
    } catch {
      return false;
    }
  };

  const inIframe = (() => {
    try {
      return window !== window.parent;
    } catch {
      return true;
    }
  })();

  const disabled =
    isBot() ||
    isAutomation() ||
    (inIframe && !config.debug) ||
    localStorage.getItem("instasight_ignore") === "true";

  // Privacy & consent gates (DNT/GPC/consent)
  const dnt =
    navigator.doNotTrack === "1" ||
    // @ts-ignore - some browsers expose window.doNotTrack
    window.doNotTrack === "1";
  // @ts-ignore - GPC is not standardized across all TS libs
  const gpc = navigator.globalPrivacyControl === true;
  const consentAttr = document.currentScript?.getAttribute("data-consent");
  const consentGiven = consentAttr ? consentAttr === "true" : true;

  // Utilities
  const getCookie = (name) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(";").shift();
    return null;
  };

  const setCookie = (name, value, days = 365) => {
    const expires = new Date(
      Date.now() + days * 24 * 60 * 60 * 1000
    ).toUTCString();
    const domainAttr = document.currentScript?.getAttribute("data-domain");
    const normalized = domainAttr?.replace(/^\./, "");
    const domainPart =
      normalized && location.hostname.endsWith(normalized)
        ? `; domain=.${normalized}`
        : "";
    document.cookie = `${name}=${value}; expires=${expires}${domainPart}; path=/; SameSite=Strict`;
  };

  const generateId = () => {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  const getOrCreateVisitorId = () => {
    let visitorId = getCookie("_instasight_visitor_id");
    if (!visitorId) {
      visitorId = generateId();
      setCookie("_instasight_visitor_id", visitorId);
    }
    return visitorId;
  };

  const getOrCreateSessionId = () => {
    let sessionId = sessionStorage.getItem("_instasight_session_id");
    if (!sessionId) {
      sessionId = generateId();
      sessionStorage.setItem("_instasight_session_id", sessionId);
    }
    return sessionId;
  };

  // Device and browser detection
  const getDeviceInfo = () => {
    const ua = navigator.userAgent;
    const viewport = {
      width: Math.max(
        document.documentElement.clientWidth || 0,
        window.innerWidth || 0
      ),
      height: Math.max(
        document.documentElement.clientHeight || 0,
        window.innerHeight || 0
      ),
    };

    return {
      viewport: `${viewport.width}x${viewport.height}`,
      userAgent: ua,
      language: navigator.language,
      platform: navigator.platform,
    };
  };

  // UTM parameter extraction
  const getUtmParams = () => {
    const params = new URLSearchParams(window.location.search);
    return {
      utm_source: params.get("utm_source"),
      utm_medium: params.get("utm_medium"),
      utm_campaign: params.get("utm_campaign"),
      utm_content: params.get("utm_content"),
      utm_term: params.get("utm_term"),
    };
  };

  // Enhanced payment detection with multiple strategies
  const detectPayment = () => {
    const url = window.location.href;
    const searchParams = new URLSearchParams(window.location.search);
    const pathname = window.location.pathname;

    // Detection strategies in order of reliability
    const detectionStrategies = [
      // Strategy 1: Stripe-specific parameter detection
      {
        name: "stripe_session",
        detect: () => searchParams.get("session_id"),
        extract: (sessionId) => ({
          provider: "stripe",
          success: true,
          sessionId,
          amount:
            extractAmountFromParams(searchParams) || extractAmountFromDOM(),
          currency: searchParams.get("currency") || extractCurrencyFromDOM(),
          metadata: {
            payment_intent: searchParams.get("payment_intent"),
            payment_intent_client_secret: searchParams.get(
              "payment_intent_client_secret"
            ),
          },
        }),
      },

      {
        name: "stripe_payment_intent",
        detect: () =>
          searchParams.get("payment_intent") && !searchParams.get("session_id"),
        extract: (paymentIntentId) => ({
          provider: "stripe",
          success: searchParams.get("payment_intent_client_secret")
            ? true
            : false,
          paymentIntentId,
          amount:
            extractAmountFromParams(searchParams) || extractAmountFromDOM(),
          currency: searchParams.get("currency") || extractCurrencyFromDOM(),
        }),
      },

      // Strategy 2: PayPal detection
      {
        name: "paypal_standard",
        detect: () => searchParams.get("tx") && searchParams.get("st"),
        extract: (tx) => ({
          provider: "paypal",
          success: searchParams.get("st") === "Completed",
          transactionId: tx,
          amount: parseFloat(searchParams.get("amt")) || extractAmountFromDOM(),
          currency: searchParams.get("cc") || "USD",
          payerId: searchParams.get("payer_id"),
        }),
      },

      // Strategy 3: LemonSqueezy detection
      {
        name: "lemonsqueezy_checkout",
        detect: () =>
          searchParams.get("checkout_id") ||
          url.includes("lemonsqueezy.com/checkout"),
        extract: (checkoutId) => ({
          provider: "lemonsqueezy",
          success: true,
          checkoutId: checkoutId || extractFromUrl("checkout_id"),
          amount:
            extractAmountFromParams(searchParams) || extractAmountFromDOM(),
          currency: searchParams.get("currency") || extractCurrencyFromDOM(),
        }),
      },

      // Strategy 4: Polar detection
      {
        name: "polar_checkout",
        detect: () =>
          searchParams.get("polar_checkout_id") ||
          url.includes("polar.sh/checkout"),
        extract: (checkoutId) => ({
          provider: "polar",
          success: true,
          checkoutId: checkoutId || extractFromUrl("polar_checkout_id"),
          amount:
            extractAmountFromParams(searchParams) || extractAmountFromDOM(),
          currency: searchParams.get("currency") || extractCurrencyFromDOM(),
        }),
      },

      // Strategy 5: Generic success page detection (fallback)
      {
        name: "generic_success_page",
        detect: () => {
          const successPatterns = [
            /\/success\/?(\?.*)?$/i,
            /\/thank-you\/?(\?.*)?$/i,
            /\/checkout\/success\/?(\?.*)?$/i,
            /\/payment\/success\/?(\?.*)?$/i,
            /\/order\/complete\/?(\?.*)?$/i,
            /\/purchase\/complete\/?(\?.*)?$/i,
          ];
          return successPatterns.some((pattern) => pattern.test(pathname));
        },
        extract: () => ({
          provider:
            searchParams.get("provider") ||
            detectProviderFromDOM() ||
            "unknown",
          success: true,
          amount:
            extractAmountFromParams(searchParams) || extractAmountFromDOM(),
          currency: searchParams.get("currency") || extractCurrencyFromDOM(),
          source: "url_pattern",
        }),
      },

      // Strategy 6: DOM-based detection for SPAs
      {
        name: "dom_based_detection",
        detect: () => {
          const confirmationSelectors = [
            ".payment-success",
            ".order-confirmation",
            ".purchase-complete",
            ".checkout-success",
            "[data-payment-success]",
            "#payment-success",
          ];
          return confirmationSelectors.some((selector) =>
            document.querySelector(selector)
          );
        },
        extract: () => ({
          provider: detectProviderFromDOM() || "unknown",
          success: true,
          amount: extractAmountFromDOM(),
          currency: extractCurrencyFromDOM(),
          source: "dom_detection",
        }),
      },
    ];

    // Execute detection strategies in order of reliability
    for (const strategy of detectionStrategies) {
      const detected = strategy.detect();
      if (detected) {
        const result = strategy.extract(detected);
        if (result && result.success) {
          return result;
        }
      }
    }

    return null;
  };

  // Helper functions for enhanced payment detection
  const extractAmountFromParams = (searchParams) => {
    const amountKeys = [
      "amount",
      "total",
      "value",
      "amt",
      "price",
      "sum",
      "order_total",
      "payment_amount",
      "transaction_amount",
    ];

    for (const key of amountKeys) {
      const value = searchParams.get(key);
      if (value && !isNaN(parseFloat(value))) {
        return parseFloat(value);
      }
    }
    return null;
  };

  const extractAmountFromDOM = () => {
    const strategies = [
      // Strategy 1: Data attributes
      () => {
        const selectors = [
          "[data-amount]",
          "[data-total]",
          "[data-price]",
          "[data-revenue]",
        ];
        for (const selector of selectors) {
          const element = document.querySelector(selector);
          if (element) {
            const amount =
              element.dataset.amount ||
              element.dataset.total ||
              element.dataset.price ||
              element.dataset.revenue;
            if (amount && !isNaN(parseFloat(amount))) {
              return parseFloat(amount);
            }
          }
        }
        return null;
      },

      // Strategy 2: Class-based selectors
      () => {
        const selectors = [
          ".order-total",
          ".payment-amount",
          ".total-amount",
          ".revenue-amount",
          ".checkout-total",
          ".purchase-total",
          "#total",
          "#amount",
        ];

        for (const selector of selectors) {
          const element = document.querySelector(selector);
          if (element) {
            const text = element.textContent || element.innerText;
            const amount = text.match(/[\d,]+\.?\d*/)?.[0];
            if (amount && !isNaN(parseFloat(amount.replace(/,/g, "")))) {
              return parseFloat(amount.replace(/,/g, ""));
            }
          }
        }
        return null;
      },

      // Strategy 3: JSON-LD structured data
      () => {
        const scripts = document.querySelectorAll(
          'script[type="application/ld+json"]'
        );
        for (const script of scripts) {
          try {
            const data = JSON.parse(script.textContent);
            if (data["@type"] === "Order" && data.totalPrice) {
              return parseFloat(data.totalPrice);
            }
          } catch (e) {
            continue;
          }
        }
        return null;
      },

      // Strategy 4: Meta tags
      () => {
        const metaTags = [
          'meta[name="order:total"]',
          'meta[property="product:price:amount"]',
          'meta[name="transaction:amount"]',
        ];

        for (const selector of metaTags) {
          const meta = document.querySelector(selector);
          if (meta && meta.content) {
            const amount = parseFloat(meta.content);
            if (!isNaN(amount)) {
              return amount;
            }
          }
        }
        return null;
      },
    ];

    // Execute strategies in order of reliability
    for (const strategy of strategies) {
      const amount = strategy();
      if (amount !== null) {
        return amount;
      }
    }

    return null;
  };

  const extractCurrencyFromDOM = () => {
    const currencySelectors = [
      "[data-currency]",
      ".currency",
      ".order-currency",
    ];

    for (const selector of currencySelectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.dataset.currency || element.textContent.trim();
      }
    }

    // Try to detect currency symbols in text
    const currencySymbols = {
      $: "USD",
      "€": "EUR",
      "£": "GBP",
      "¥": "JPY",
      "₹": "INR",
    };

    const bodyText = document.body.textContent;
    for (const [symbol, currency] of Object.entries(currencySymbols)) {
      if (bodyText.includes(symbol)) {
        return currency;
      }
    }

    return null;
  };

  const detectProviderFromDOM = () => {
    const providerIndicators = {
      stripe: ["stripe", "stripe.com"],
      paypal: ["paypal", "paypal.com"],
      square: ["square", "squareup.com"],
      lemonsqueezy: ["lemonsqueezy", "lemon squeezy"],
      polar: ["polar.sh", "polar"],
    };

    const pageContent = document.body.textContent.toLowerCase();

    for (const [provider, indicators] of Object.entries(providerIndicators)) {
      if (indicators.some((indicator) => pageContent.includes(indicator))) {
        return provider;
      }
    }

    return null;
  };

  const extractFromUrl = (paramName) => {
    const url = window.location.href;
    const regex = new RegExp(`[?&]${paramName}=([^&#]*)`);
    const match = url.match(regex);
    return match ? decodeURIComponent(match[1]) : null;
  };

  // Data validation and sanitization
  const validateData = (data) => {
    const validated = {};
    const maxFields = 10;
    const maxFieldNameLength = 50;
    const maxFieldValueLength = 500;

    if (data && typeof data === "object") {
      let fieldCount = 0;
      for (const [key, value] of Object.entries(data)) {
        if (fieldCount >= maxFields) break;

        // Validate field name
        if (typeof key !== "string" || key.length > maxFieldNameLength)
          continue;
        if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(key)) continue;

        // Validate field value
        if (value === null || value === undefined) continue;
        const stringValue = String(value);
        if (stringValue.length > maxFieldValueLength) continue;

        // XSS prevention
        const sanitizedValue = stringValue
          .replace(/[<>'"&]/g, "")
          .replace(/javascript:/gi, "")
          .replace(/data:/gi, "");

        validated[key] = sanitizedValue;
        fieldCount++;
      }
    }

    return validated;
  };

  // Normal sender: fetch with keepalive (for regular events)
  const sendEventFetch = (eventData) => {
    try {
      const payload = JSON.stringify(eventData);
      fetch(config.endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: payload,
        keepalive: true,
      }).catch((error) => {
        if (config.debug) {
          console.error("InstaSight Error:", error);
        }
      });
    } catch (error) {
      if (config.debug) {
        console.error("InstaSight Send Error:", error);
      }
    }
  };

  // Beacon sender: try sendBeacon, fallback to fetch (for pagehide/visibility hidden)
  const sendEventBeacon = (eventData) => {
    try {
      const payload = JSON.stringify(eventData);
      let queued = false;
      if (navigator.sendBeacon) {
        try {
          // Use text/plain to avoid CORS preflight
          queued = navigator.sendBeacon(config.endpoint, payload);
        } catch (e) {
          if (config.debug) {
            console.error("sendBeacon failed:", e);
          }
        }
      }
      if (!queued) {
        sendEventFetch(eventData);
      }
    } catch (error) {
      if (config.debug) {
        console.error("InstaSight Beacon Error:", error);
      }
    }
  };

  // Event tracking
  const track = (eventType, eventName, data = {}) => {
    try {
      if (disabled) return;
      if (dnt || gpc || !consentGiven) return;
      const deviceInfo = getDeviceInfo();
      const utmParams = getUtmParams();

      const eventData = {
        websiteId: config.websiteId,
        visitorId: getOrCreateVisitorId(),
        sessionId: getOrCreateSessionId(),
        eventType,
        eventName,
        url: window.location.href,
        referrer: document.referrer || "",
        timestamp: new Date().toISOString(),
        ...deviceInfo,
        ...utmParams,
        customData: validateData(data),
      };

      if (config.debug) {
        console.log("InstaSight Event:", eventData);
      }

      // Send event (regular path)
      sendEventFetch(eventData);
    } catch (error) {
      if (config.debug) {
        console.error("InstaSight Tracking Error:", error);
      }
    }
  };

  // Page view tracking
  const trackPageView = () => {
    track("pageview", "page_view");
  };

  // External link tracking
  const trackExternalLinks = () => {
    document.addEventListener("click", (e) => {
      const link = e.target.closest?.("a");
      if (!link) return;

      const href = link.getAttribute("href");
      if (!href) return;

      const isExternal =
        href.startsWith("http") && !href.includes(window.location.hostname);

      if (isExternal) {
        track("custom", "external_link_click", {
          url: href,
          text: link.textContent?.trim() || "",
        });
      }
    });
  };

  // Form submission tracking
  const trackFormSubmissions = () => {
    document.addEventListener("submit", (e) => {
      const form = e.target;
      if (form.tagName === "FORM") {
        const formData = new FormData(form);
        const data = {};

        // Only track non-sensitive form fields
        for (const [key, value] of formData.entries()) {
          if (
            !key.toLowerCase().includes("password") &&
            !key.toLowerCase().includes("token") &&
            !key.toLowerCase().includes("secret")
          ) {
            data[key] = value?.toString().substring(0, 100); // Limit length
          }
        }

        track("custom", "form_submit", {
          formId: form.id || "",
          formAction: form.action || "",
          ...data,
        });
      }
    });
  };

  // SPA route change detection
  const trackRouteChanges = () => {
    let currentPath = window.location.pathname;

    const checkRouteChange = () => {
      if (window.location.pathname !== currentPath) {
        currentPath = window.location.pathname;
        trackPageView();
      }
    };

    // Listen for browser navigation
    window.addEventListener("popstate", checkRouteChange);

    // Override pushState and replaceState for SPA navigation
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function (...args) {
      originalPushState.apply(this, args);
      setTimeout(checkRouteChange, 0);
    };

    history.replaceState = function (...args) {
      originalReplaceState.apply(this, args);
      setTimeout(checkRouteChange, 0);
    };
  };

  // Public API: expose only window.instasight
  const api = {
    track: (eventName, data) => track("custom", eventName, data),
    trackPageView,
    trackConversion: (value, currency = "USD") =>
      track("conversion", "conversion", { value, currency }),
    trackRevenue: (amount, currency = "USD") =>
      track("payment", "revenue", { amount, currency }),
    identify: (userId, traits = {}) => {
      setCookie("_df_user_id", userId);
      track("custom", "user_identify", { userId, ...traits });
    },
  };
  // Consume pre-queued calls if any, then expose API
  const queued =
    window.instasight && Array.isArray(window.instasight.q)
      ? [...window.instasight.q]
      : [];
  window.instasight = api;

  // Expose for debugging
  if (config.debug) {
    window.sendPresenceDisconnect = sendPresenceDisconnect;
    window.getCurrentSessionToken = () => currentSessionToken;
    window.setCurrentSessionToken = (token) => {
      currentSessionToken = token;
    };
    window.testDisconnectForced = () => {
      // Force disconnect regardless of conditions
      console.log("testDisconnectForced called");
      const testToken = "test-token-123";
      const disconnectData = { sessionToken: testToken };
      const leaveUrl = `${config.heartbeatEndpoint.replace("/heartbeat", "/leave")}`;
      const payload = JSON.stringify(disconnectData);

      console.log("Forcing disconnect with URL:", leaveUrl);
      console.log("Forcing disconnect with payload:", payload);

      if (navigator.sendBeacon) {
        const sent = navigator.sendBeacon(leaveUrl, payload);
        console.log("sendBeacon result:", sent);
      } else {
        console.log("sendBeacon not available, using fetch");
        fetch(leaveUrl, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: payload,
          keepalive: true,
        })
          .then((r) => r.json())
          .then((data) => {
            console.log("fetch result:", data);
          })
          .catch((e) => {
            console.log("fetch error:", e);
          });
      }
    };
  }
  if (queued.length) {
    try {
      for (const args of queued) {
        if (Array.isArray(args)) {
          api.track(args[0], args[1]);
        }
      }
    } catch {}
  }

  // Periodic heartbeat while page is visible to keep presence fresh
  // this function will insert / update the user details such as device, browser, os, screen, timestamp etc.
  let heartbeatTimer = null;
  let currentSessionToken = null; // Store session token for disconnect
  const HEARTBEAT_INTERVAL_MS = 25000; // 25s

  // Send presence heartbeat to Convex (new function)
  // this function will insert / update the user details such as device, browser, os, screen, timestamp etc.
  function sendPresenceHeartbeat() {
    try {
      if (disabled) return;
      if (dnt || gpc || !consentGiven) return;

      const deviceInfo = getDeviceInfo();
      const heartbeatData = {
        trackingId: config.websiteId,
        visitorId: getOrCreateVisitorId(),
        sessionId: getOrCreateSessionId(),
        interval: HEARTBEAT_INTERVAL_MS,
        // 添加访客详细信息
        url: window.location.href,
        referrer: document.referrer || "",
        device: deviceInfo.device || "",
        browser: deviceInfo.browser || "",
        os: deviceInfo.os || "",
        screen: deviceInfo.screen || "",
        timestamp: new Date().toISOString(),
      };

      // Send to Convex Presence API
      fetch(`${config.heartbeatEndpoint}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(heartbeatData),
        keepalive: true,
      })
        .then((response) => response.json())
        .then((data) => {
          // Store session token for disconnect
          if (data && data.sessionToken) {
            currentSessionToken = data.sessionToken;
          }
        })
        .catch(() => {}); // 静默失败，避免影响主功能
    } catch {}
  }

  // Send presence disconnect to Convex (new function)
  function sendPresenceDisconnect() {
    try {
      console.log("=== sendPresenceDisconnect called ===");
      console.log("currentSessionToken:", currentSessionToken);
      console.log("disabled:", disabled);
      console.log("dnt:", dnt);
      console.log("gpc:", gpc);
      console.log("consentGiven:", consentGiven);

      if (!currentSessionToken) {
        console.log("⚠️ No sessionToken yet, attempting fallback disconnect");
        // Fallback: try to disconnect using visitorId/sessionId directly
        // This handles cases where page hide fires before heartbeat response
        sendFallbackDisconnect();
        return;
      }
      if (disabled) {
        console.log("❌ Tracking disabled, skipping disconnect");
        return;
      }
      if (dnt || gpc || !consentGiven) {
        console.log("❌ Privacy settings prevent disconnect");
        return;
      }

      console.log("✅ All conditions passed, proceeding with disconnect");

      // Also send a test event to verify the request mechanism works
      try {
        fetch(config.endpoint, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            websiteId: config.websiteId,
            visitorId: getOrCreateVisitorId(),
            sessionId: getOrCreateSessionId(),
            eventType: "custom",
            eventName: "disconnect_attempt",
            url: window.location.href,
            timestamp: new Date().toISOString(),
          }),
          keepalive: true,
        }).catch(() => {});
      } catch {}

      const disconnectData = {
        sessionToken: currentSessionToken,
      };

      // Use sendBeacon for reliability during page unload
      const payload = JSON.stringify(disconnectData);
      let sent = false;

      if (navigator.sendBeacon) {
        console.log("sendPresenceDisconnect using sendBeacon", disconnectData);
        const leaveUrl = `${config.heartbeatEndpoint.replace("/heartbeat", "/leave")}`;
        console.log("sendBeacon URL:", leaveUrl);
        console.log("sendBeacon payload:", payload);
        try {
          // Use text/plain to avoid CORS preflight
          sent = navigator.sendBeacon(leaveUrl, payload);
          console.log("sendBeacon result:", sent);
        } catch (e) {
          console.log("sendBeacon failed:", e);
        }
      } else {
        console.log("navigator.sendBeacon not available");
      }

      // Fallback to fetch if sendBeacon fails
      if (!sent) {
        console.log("sendPresenceDisconnect using fetch", disconnectData);
        fetch(`${config.heartbeatEndpoint.replace("/heartbeat", "/leave")}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: payload,
          keepalive: true,
        }).catch((e) => {
          console.log("fetch disconnect failed:", e);
        });
      }

      // Clear the session token after disconnect
      currentSessionToken = null;
    } catch (e) {
      console.log("sendPresenceDisconnect error:", e);
    }
  }

  // Fallback disconnect when sessionToken is not available
  function sendFallbackDisconnect() {
    try {
      console.log("=== sendFallbackDisconnect called ===");

      // Create a temporary heartbeat to get sessionToken, then immediately disconnect
      const deviceInfo = getDeviceInfo();
      const heartbeatData = {
        trackingId: config.websiteId,
        visitorId: getOrCreateVisitorId(),
        sessionId: getOrCreateSessionId(),
        interval: HEARTBEAT_INTERVAL_MS,
        url: window.location.href,
        referrer: document.referrer || "",
        device: deviceInfo.device || "",
        browser: deviceInfo.browser || "",
        os: deviceInfo.os || "",
        screen: deviceInfo.screen || "",
        timestamp: new Date().toISOString(),
      };

      console.log(
        "Sending emergency heartbeat to get sessionToken for disconnect"
      );

      // Send heartbeat to get sessionToken, then immediately disconnect
      fetch(`${config.heartbeatEndpoint}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(heartbeatData),
        keepalive: true,
      })
        .then((response) => response.json())
        .then((data) => {
          if (data && data.sessionToken) {
            console.log(
              "Got sessionToken from emergency heartbeat:",
              data.sessionToken
            );
            // Immediately disconnect with the new sessionToken
            const disconnectData = { sessionToken: data.sessionToken };
            const leaveUrl = `${config.heartbeatEndpoint.replace("/heartbeat", "/leave")}`;
            const payload = JSON.stringify(disconnectData);

            if (navigator.sendBeacon) {
              const sent = navigator.sendBeacon(leaveUrl, payload);
              console.log("Emergency disconnect sendBeacon result:", sent);
            } else {
              fetch(leaveUrl, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: payload,
                keepalive: true,
              }).catch(() => {});
            }
          }
        })
        .catch((e) => {
          console.log("Emergency heartbeat failed:", e);
        });
    } catch (e) {
      console.log("sendFallbackDisconnect error:", e);
    }
  }

  // Enter presence room on initial load
  // this function will insert / update the user details such as device, browser, os, screen, timestamp etc.
  function enterPresenceRoom() {
    try {
      if (disabled) return;
      if (dnt || gpc || !consentGiven) return;

      const deviceInfo = getDeviceInfo();
      const enterData = {
        trackingId: config.websiteId,
        visitorId: getOrCreateVisitorId(),
        sessionId: getOrCreateSessionId(),
        // 添加访客详细信息
        url: window.location.href,
        referrer: document.referrer || "",
        device: deviceInfo.device || "",
        browser: deviceInfo.browser || "",
        os: deviceInfo.os || "",
        screen: deviceInfo.screen || "",
        timestamp: new Date().toISOString(),
      };

      // Send to Convex Presence API
      fetch(`${config.presenceEndpoint}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(enterData),
        keepalive: true,
      }).catch((e) => {
        console.log("enterPresenceRoom error", e);
      }); // 静默失败，避免影响主功能
    } catch {}
  }

  // Legacy analytics heartbeat function
  function sendHeartbeat() {
    try {
      if (disabled) return;
      if (dnt || gpc || !consentGiven) return;
      const deviceInfo = getDeviceInfo();
      const eventData = {
        websiteId: config.websiteId,
        visitorId: getOrCreateVisitorId(),
        sessionId: getOrCreateSessionId(),
        eventType: "custom",
        eventName: "heartbeat",
        url: window.location.href,
        referrer: document.referrer || "",
        timestamp: new Date().toISOString(),
        ...deviceInfo,
        customData: { reason: "interval" },
      };
      // Active page: normal fetch is fine
      sendEventFetch(eventData);
    } catch {}
  }
  function startHeartbeat() {
    if (heartbeatTimer != null) return;
    // 组合心跳：同时发送分析事件和 Convex Presence 心跳
    const combinedHeartbeat = () => {
      sendHeartbeat(); // 传统分析心跳
      sendPresenceHeartbeat(); // Convex Presence 心跳
    };
    heartbeatTimer = setInterval(combinedHeartbeat, HEARTBEAT_INTERVAL_MS);
  }
  function stopHeartbeat() {
    if (heartbeatTimer != null) {
      clearInterval(heartbeatTimer);
      heartbeatTimer = null;
    }
  }

  // Initialize tracking
  const init = () => {
    if (config.auto && !disabled) {
      // page view event
      trackPageView();
      // register external link click event
      trackExternalLinks();
      // register form submission event
      trackFormSubmissions();
      // register SPA route change event
      trackRouteChanges();
      // insert / update the user details such as device, browser, os, screen, timestamp
      enterPresenceRoom();
      startHeartbeat();
      if (config.trackIdle) startIdleTracking();
    }

    if (config.debug) {
      console.log("InstaSight initialized:", config);
    }
  };

  // Flush a lightweight heartbeat on page hide to improve edge-case delivery
  const flushOnHide = () => {
    try {
      if (disabled) return;
      if (dnt || gpc || !consentGiven) return;
      const deviceInfo = getDeviceInfo();
      const eventData = {
        websiteId: config.websiteId,
        visitorId: getOrCreateVisitorId(),
        sessionId: getOrCreateSessionId(),
        eventType: "custom",
        eventName: "heartbeat",
        url: window.location.href,
        referrer: document.referrer || "",
        timestamp: new Date().toISOString(),
        ...deviceInfo,
        customData: { reason: "page_hide" },
      };
      sendEventBeacon(eventData);
    } catch (error) {
      if (config.debug) console.error("InstaSight Heartbeat Error:", error);
    }
  };

  // Start when DOM is ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", init);
  } else {
    init();
  }

  // Page lifecycle hooks for reliability
  document.addEventListener("visibilitychange", () => {
    console.log("=== visibilitychange event ===");
    console.log("document.visibilityState:", document.visibilityState);
    if (document.visibilityState === "hidden") {
      console.log(
        "Page hidden - calling flushOnHide, stopHeartbeat, sendPresenceDisconnect"
      );
      flushOnHide();
      stopHeartbeat();
      sendPresenceDisconnect(); // Disconnect from presence when page becomes hidden
    } else if (document.visibilityState === "visible") {
      console.log(
        "Page visible - sending immediate heartbeat and starting timer"
      );
      // Send immediate heartbeat for instant presence update
      sendPresenceHeartbeat();
      startHeartbeat();
    }
  });

  // Handle page unload events
  window.addEventListener("pagehide", () => {
    console.log("=== pagehide event ===");
    flushOnHide();
    sendPresenceDisconnect(); // Disconnect from presence when page is unloaded
  });

  window.addEventListener("beforeunload", () => {
    console.log("=== beforeunload event ===");
    sendPresenceDisconnect(); // Disconnect from presence before page unloads
  });

  // Idle detection: emit idle_start after no interaction for idleThreshold; emit idle_end on next interaction
  let idleTimer = null;
  let isIdle = false;
  const IDLE_EVENTS = [
    "mousemove",
    "mousedown",
    "keydown",
    "touchstart",
    "scroll",
  ];
  const onUserActivity = () => {
    try {
      if (disabled) return;
      if (dnt || gpc || !consentGiven) return;
      if (isIdle) {
        isIdle = false;
        track("custom", "idle_end");
      }
      if (idleTimer) clearTimeout(idleTimer);
      idleTimer = setTimeout(() => {
        isIdle = true;
        track("custom", "idle_start");
      }, config.idleThreshold);
    } catch {}
  };
  const startIdleTracking = () => {
    try {
      // bootstrap
      onUserActivity();
      // listeners
      for (const ev of IDLE_EVENTS) {
        window.addEventListener(ev, onUserActivity, { passive: true });
      }
      document.addEventListener("visibilitychange", () => {
        if (document.visibilityState === "hidden") {
          isIdle = true;
          track("custom", "idle_start");
          if (idleTimer) {
            clearTimeout(idleTimer);
            idleTimer = null;
          }
        } else if (document.visibilityState === "visible") {
          onUserActivity();
        }
      });
      window.addEventListener("pagehide", () => {
        isIdle = true;
        track("custom", "idle_start");
        if (idleTimer) {
          clearTimeout(idleTimer);
          idleTimer = null;
        }
      });
    } catch {}
  };
})();
