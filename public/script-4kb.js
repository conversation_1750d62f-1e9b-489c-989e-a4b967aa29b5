/**
 * InstaSight - 4KB 轻量级追踪脚本
 * 极简版本，专注核心追踪功能
 */
!(() => {
  const s = document.currentScript;
  const c = {
    e: s?.getAttribute("data-endpoint") || "/api/events",
    w: s?.getAttribute("data-website-id"),
    d: s?.getAttribute("data-domain"),
    a: s?.getAttribute("data-auto") !== "false",
    g: s?.getAttribute("data-debug") === "true",
  };

  if (!c.w) return;

  // 检测机器人
  if (/bot|crawler|spider|lighthouse/i.test(navigator.userAgent)) return;

  // 工具函数
  const getCookie = (n) => {
    const v = `; ${document.cookie}`;
    const p = v.split(`; ${n}=`);
    return p.length === 2 ? p.pop().split(";").shift() : null;
  };

  const setCookie = (n, v, d = 365) => {
    const e = new Date(Date.now() + d * 864e5).toUTCString();
    document.cookie = `${n}=${v}; expires=${e}; path=/; SameSite=Strict`;
  };

  const genId = () =>
    Math.random().toString(36).substr(2) + Date.now().toString(36);

  // 获取或创建ID
  const getVisitorId = () => {
    let v = getCookie("df_v");
    if (!v) {
      v = genId();
      setCookie("df_v", v);
    }
    return v;
  };

  const getSessionId = () => {
    let s = sessionStorage.getItem("df_s");
    if (!s) {
      s = genId();
      sessionStorage.setItem("df_s", s);
    }
    return s;
  };

  // 获取设备信息
  const getDevice = () => {
    const ua = navigator.userAgent;
    const mobile = /Mobile|Android|iPhone|iPad/.test(ua);
    return {
      device: /iPad/.test(ua) ? "tablet" : mobile ? "mobile" : "desktop",
      browser: ua.includes("Chrome")
        ? "chrome"
        : ua.includes("Firefox")
          ? "firefox"
          : ua.includes("Safari")
            ? "safari"
            : "other",
      os: ua.includes("Windows")
        ? "windows"
        : ua.includes("Mac")
          ? "macos"
          : ua.includes("Linux")
            ? "linux"
            : ua.includes("Android")
              ? "android"
              : ua.includes("iOS")
                ? "ios"
                : "other",
    };
  };

  // 获取UTM参数
  const getUtm = () => {
    const u = new URLSearchParams(location.search);
    return {
      utm_source: u.get("utm_source"),
      utm_medium: u.get("utm_medium"),
      utm_campaign: u.get("utm_campaign"),
      utm_content: u.get("utm_content"),
      utm_term: u.get("utm_term"),
    };
  };

  // 发送事件
  const track = (type, name, data = {}) => {
    const event = {
      websiteId: c.w,
      sessionId: getSessionId(),
      visitorId: getVisitorId(),
      eventType: type,
      eventName: name,
      url: location.href,
      referrer: document.referrer || null,
      timestamp: new Date().toISOString(),
      ...getDevice(),
      ...getUtm(),
      ...data,
    };

    // 清理空值
    Object.keys(event).forEach((k) => {
      if (event[k] === null || event[k] === undefined || event[k] === "") {
        delete event[k];
      }
    });

    fetch(c.e, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(event),
      keepalive: true,
    }).catch((e) => c.g && console.error("InstaSight:", e));

    c.g && console.log("InstaSight tracked:", type, event);
  };

  // 页面浏览事件
  const trackPageView = () =>
    track("pageview", null, { title: document.title });

  // 自动追踪
  if (c.a) {
    // 初始页面
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", trackPageView);
    } else {
      trackPageView();
    }

    // SPA路由变化
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function (...args) {
      originalPushState.apply(history, args);
      setTimeout(trackPageView, 100);
    };

    history.replaceState = function (...args) {
      originalReplaceState.apply(history, args);
      setTimeout(trackPageView, 100);
    };

    addEventListener("popstate", () => setTimeout(trackPageView, 100));
  }

  // 全局API（向后兼容 datafast 名称）
  const api = { track, trackPageView };
  window.instasight = api;
})();
