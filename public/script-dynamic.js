/**
 * InstaSight 动态追踪脚本
 * 根据环境自动选择合适的 Tinybird API 端点
 */
!(() => {
  // 环境检测
  const detectEnvironment = () => {
    // 检查当前域名
    const hostname = window.location.hostname;

    // 本地开发环境
    if (
      hostname === "localhost" ||
      hostname === "127.0.0.1" ||
      hostname.includes(".local")
    ) {
      return "local";
    }

    // 检查是否有明确的环境标识
    const envAttribute =
      document.currentScript?.getAttribute("data-environment");
    if (envAttribute) {
      return envAttribute;
    }

    // 默认为生产环境
    return "production";
  };

  // 根据环境获取 API 端点
  const getApiEndpoint = (environment) => {
    const customUrl = document.currentScript?.getAttribute("data-api-url");
    if (customUrl) {
      return customUrl;
    }

    if (environment === "local") {
      return "http://localhost:7181/v0/events?name=events";
    }

    return "https://api.tinybird.co/v0/events?name=events";
  };

  // 检测当前环境
  const environment = detectEnvironment();

  // 配置
  const config = {
    environment,
    tinybirdUrl: getApiEndpoint(environment),
    tinybirdToken: document.currentScript?.getAttribute("data-tinybird-token"),
    websiteId: document.currentScript?.getAttribute("data-website-id"),
    domain: document.currentScript?.getAttribute("data-domain"),
    auto: document.currentScript?.getAttribute("data-auto") !== "false",
    debug:
      document.currentScript?.getAttribute("data-debug") === "true" ||
      environment === "local",
    batchSize: parseInt(
      document.currentScript?.getAttribute("data-batch-size") || "5"
    ),
    flushInterval: parseInt(
      document.currentScript?.getAttribute("data-flush-interval") || "3000"
    ),
  };

  // 环境信息日志
  if (config.debug) {
    console.log(`🚀 InstaSight Tracker initialized`, {
      environment: config.environment,
      apiUrl: config.tinybirdUrl,
      websiteId: config.websiteId,
      debug: config.debug,
    });
  }

  // 验证必需参数
  if (!config.websiteId) {
    console.error("InstaSight: data-website-id is required");
    return;
  }

  if (!config.tinybirdToken) {
    console.error("InstaSight: data-tinybird-token is required");
    return;
  }

  // Bot 检测
  const isBot = () => {
    const botPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i,
      /lighthouse/i,
      /gtmetrix/i,
      /pingdom/i,
      /prerender/i,
      /headless/i,
      /phantom/i,
      /selenium/i,
    ];
    return botPatterns.some((pattern) => pattern.test(navigator.userAgent));
  };

  if (isBot()) {
    if (config.debug) {
      console.log("InstaSight: Bot detected, skipping tracking");
    }
    return;
  }

  // 工具函数
  const getCookie = (name) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(";").shift();
    return null;
  };

  const setCookie = (name, value, days = 365) => {
    const expires = new Date(
      Date.now() + days * 24 * 60 * 60 * 1000
    ).toUTCString();
    document.cookie = `${name}=${value}; expires=${expires}; path=/; SameSite=Strict`;
  };

  const generateId = () =>
    Math.random().toString(36).substring(2) + Date.now().toString(36);

  // 获取或创建访客 ID
  const getVisitorId = () => {
    let visitorId = getCookie("df_visitor_id");
    if (!visitorId) {
      visitorId = generateId();
      setCookie("df_visitor_id", visitorId);
    }
    return visitorId;
  };

  // 获取或创建会话 ID
  const getSessionId = () => {
    let sessionId = sessionStorage.getItem("df_session_id");
    if (!sessionId) {
      sessionId = generateId();
      sessionStorage.setItem("df_session_id", sessionId);
    }
    return sessionId;
  };

  // 获取地理位置信息 (模拟，实际应该从服务端获取)
  const getGeoData = () => {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      return {
        country: null, // 需要从 IP 获取
        region: null,
        city: null,
        timezone,
      };
    } catch {
      return {};
    }
  };

  // 获取设备信息
  const getDeviceInfo = () => {
    const ua = navigator.userAgent;
    const mobile = /Mobile|Android|iPhone|iPad/.test(ua);

    let device = "desktop";
    if (/iPad/.test(ua)) device = "tablet";
    else if (mobile) device = "mobile";

    let browser = "unknown";
    if (ua.includes("Chrome")) browser = "chrome";
    else if (ua.includes("Firefox")) browser = "firefox";
    else if (ua.includes("Safari")) browser = "safari";
    else if (ua.includes("Edge")) browser = "edge";

    let os = "unknown";
    if (ua.includes("Windows")) os = "windows";
    else if (ua.includes("Mac")) os = "macos";
    else if (ua.includes("Linux")) os = "linux";
    else if (ua.includes("Android")) os = "android";
    else if (ua.includes("iOS")) os = "ios";

    return { device, browser, os };
  };

  // 获取 UTM 参数
  const getUtmParams = () => {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      utm_source: urlParams.get("utm_source"),
      utm_medium: urlParams.get("utm_medium"),
      utm_campaign: urlParams.get("utm_campaign"),
      utm_content: urlParams.get("utm_content"),
      utm_term: urlParams.get("utm_term"),
    };
  };

  // 清理数据
  const sanitizeData = (data) => {
    const sanitized = {};
    for (const [key, value] of Object.entries(data)) {
      if (value !== null && value !== undefined && value !== "") {
        if (typeof value === "string") {
          sanitized[key] = value.slice(0, 500); // 限制字符串长度
        } else {
          sanitized[key] = value;
        }
      }
    }
    return sanitized;
  };

  // 事件队列
  class EventQueue {
    constructor() {
      this.queue = [];
      this.isFlushInProgress = false;
      this.flushTimer = null;
      this.startFlushTimer();
    }

    enqueue(event) {
      this.queue.push(event);

      if (config.debug) {
        console.log("InstaSight: Event enqueued", event);
      }

      if (this.queue.length >= config.batchSize) {
        this.flush();
      }
    }

    async flush() {
      if (this.queue.length === 0 || this.isFlushInProgress) return;

      this.isFlushInProgress = true;
      const eventsToSend = [...this.queue];
      this.queue = [];

      try {
        // 将事件转换为 NDJSON 格式
        const ndjsonData = eventsToSend
          .map((event) => JSON.stringify(event))
          .join("\n");

        const response = await fetch(config.tinybirdUrl, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${config.tinybirdToken}`,
            "Content-Type": "application/json",
          },
          body: ndjsonData,
          keepalive: true,
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        if (config.debug) {
          console.log(
            `InstaSight: Successfully sent ${eventsToSend.length} events to ${config.environment} environment`
          );
        }
      } catch (error) {
        if (config.debug) {
          console.error("InstaSight: Failed to send events:", error);
        }
        // 将失败的事件重新加入队列前端 (可选择重试逻辑)
        this.queue.unshift(...eventsToSend);
      } finally {
        this.isFlushInProgress = false;
      }
    }

    startFlushTimer() {
      this.flushTimer = setInterval(() => {
        this.flush();
      }, config.flushInterval);
    }

    stop() {
      if (this.flushTimer) {
        clearInterval(this.flushTimer);
        this.flushTimer = null;
      }
      this.flush();
    }
  }

  const eventQueue = new EventQueue();

  // 事件追踪
  const track = (eventType, eventName, data = {}) => {
    const event = {
      id: generateId(),
      website_id: config.websiteId,
      session_id: getSessionId(),
      visitor_id: getVisitorId(),
      event_type: eventType,
      event_name: eventName,
      url: window.location.href,
      referrer: document.referrer || null,
      user_agent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      ...getGeoData(),
      ...getDeviceInfo(),
      ...getUtmParams(),
      ...data,
    };

    eventQueue.enqueue(sanitizeData(event));
  };

  // 页面浏览事件
  const trackPageView = () => {
    track("pageview", null, {
      title: document.title,
      url: window.location.href,
    });
  };

  // 自动追踪页面浏览
  if (config.auto) {
    // 初始页面加载
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", trackPageView);
    } else {
      trackPageView();
    }

    // SPA 路由变化 (History API)
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function (...args) {
      originalPushState.apply(history, args);
      setTimeout(trackPageView, 100);
    };

    history.replaceState = function (...args) {
      originalReplaceState.apply(history, args);
      setTimeout(trackPageView, 100);
    };

    window.addEventListener("popstate", () => {
      setTimeout(trackPageView, 100);
    });
  }

  // 页面卸载时发送剩余事件
  window.addEventListener("beforeunload", () => {
    eventQueue.stop();
  });

  // 页面可见性变化
  document.addEventListener("visibilitychange", () => {
    if (document.hidden) {
      eventQueue.flush();
    }
  });

  // 暴露全局 API（向后兼容 datafast 名称）
  const api = {
    track,
    trackPageView,
    config: { ...config, tinybirdToken: "***" }, // 隐藏 token
    environment: config.environment,
  };
  window.instasight = api;

  if (config.debug) {
    console.log("InstaSight: Global API available as window.instasight");
  }
})();
