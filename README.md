# InstaSight - Revenue-Driven Analytics

InstaSight 是一个现代化的收入驱动型网站分析平台（Next.js 15 + Tinybird + Neon），聚焦收入归因与实时访客洞察，提供轻量可插拔的前端埋点脚本与高性能服务端上报路径。

## 🚀 Features

- **Revenue Attribution** - See which traffic sources and campaigns actually drive sales
- **Live Visitor Intelligence** - Watch visitors browse your site in real-time
- **Goal Tracking** - Set up conversion goals and track customer journeys
- **4KB Tracking Script** - Ultra-lightweight script that won't slow down your site
- **Privacy Compliant** - GDPR-friendly analytics without compromising insights
- **Payment Integration** - Direct integration with Stripe, LemonSqueezy, and Polar
- **🆕 Smart Environment Detection** - Automatically switch between local/production Tinybird endpoints
- **🆕 Multi-Mode Tracking** - Support for proxy, direct, and dynamic tracking modes

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Analytics**: Tinybird（datasource/pipes + SDK）
- **Database**: Neon (PostgreSQL) + Drizzle ORM（只存元数据/配置）
- **Auth**: Auth.js v5（Resend Magic Link）
- **Cache**: Dev 内存缓存 / Prod Upstash Redis（自动切换）
- **UI**: shadcn/ui + TailwindCSS v4
- **State**: Zustand
- **Deploy**: Vercel（推荐）

## 📋 Prerequisites

- Node.js 18+
- Neon / PostgreSQL（元数据）
- Tinybird 账号（分析与实时）
- Upstash Redis（生产缓存，推荐）
- Resend 账号（邮件登录）

## 🏃‍♂️ Quick Start

1. **Clone and install dependencies**

   ```bash
   git clone <repository-url>
   cd datafast
   npm install
   ```

2. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   ```

   填写环境变量（本地示例见下文 Environment Variables 部分）：
   - `DATABASE_URL`、`AUTH_SECRET`、`AUTH_RESEND_KEY`
   - `TINYBIRD_API_URL`、`TINYBIRD_TOKEN`（可选本地：`TINYBIRD_API_URL_LOCAL`、`TINYBIRD_TOKEN_LOCAL`）
   - `UPSTASH_REDIS_REST_URL`、`UPSTASH_REDIS_REST_TOKEN`（生产推荐）
   - `NEXT_PUBLIC_APP_URL`、`EVENT_WRITE_MODE`（默认 `tinybird`）

3. **Set up the database**

   ```bash
   npm run db:generate
   npm run db:migrate
   ```

4. **Start the development server**

   ```bash
   npm run dev
   ```

5. **Visit your app**
   Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📊 Tracking Setup

开始采集：

1. 在仪表盘创建网站，获得 `trackingId`
2. 推荐使用“动态分发”脚本（自动注入正确端点/Token）：

```html
<script
  src="https://<your-app>.vercel.app/api/script/<trackingId>?mode=default"
  data-website-id="<trackingId>"
  data-domain="yourdomain.com"
  defer
></script>
```

可选：

- 极致性能直连 Tinybird：`mode=direct`
- 4KB 轻量版：`mode=4kb`
- 如使用静态 `public/script.js`，跨域时务必通过 `data-endpoint="https://<your-app>.vercel.app/api/events"` 指向完整端点

脚本默认自动追踪：

- Page views / External link clicks / Form submissions
- Payment 轻量检测（Stripe / LemonSqueezy / Polar
- 自定义事件（通过 JS API）

## 🔧 Development Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server

# Database
npm run db:generate  # Generate migrations
npm run db:migrate   # Run migrations
npm run db:studio    # Open Drizzle Studio
npm run db:push      # Push schema to database

# Code Quality
npm run lint         # Run Biome linter
npm run format       # Format code with Biome
npm run format:check # Check code formatting
```

## 🔒 Environment Variables

`.env.local` 示例：

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/instasight_dev"

# Auth
AUTH_SECRET="your-random-secret-here"
AUTH_RESEND_KEY="re_your-resend-api-key"

# Tinybird (local / production)
TINYBIRD_API_URL_LOCAL="http://localhost:7181"
TINYBIRD_TOKEN_LOCAL="tb_your_local_token"
TINYBIRD_API_URL="https://api.tinybird.co"
TINYBIRD_TOKEN="tb_your_prod_token"

# Upstash (production cache)
UPSTASH_REDIS_REST_URL="https://...upstash.io"
UPSTASH_REDIS_REST_TOKEN="..."

# App
NEXT_PUBLIC_APP_URL="http://localhost:3000"
EVENT_WRITE_MODE="tinybird" # tinybird | dual | neon
```

## 🏗️ Project Structure

```
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── ui/               # shadcn/ui components
│   ├── analytics/        # Analytics components
│   └── dashboard/        # Dashboard components
├── lib/                   # Libraries
│   ├── auth/             # Auth.js 配置
│   ├── db/               # Neon/Drizzle（元数据/配置）
│   ├── tinybird/         # Tinybird 客户端/分析/事件
│   ├── cache/            # 缓存（内存/Upstash）
│   └── utils/            # 工具
├── stores/               # Zustand stores
├── public/               # Static files
│   └── script.js         # Tracking script
└── drizzle/              # Database migrations
```

## 📈 Tracking JavaScript API

脚本对外暴露 `window.instasight`：

```javascript
// Track custom events
instasight.track("button_click", { button: "signup" });

// Track conversions
instasight.trackConversion(99.99, "USD");

// Track revenue
instasight.trackRevenue(299.99, "USD");

// Identify users
instasight.identify("user123", { plan: "pro" });

// Manual page view tracking
instasight.trackPageView();
```

## 🎯 Payment Integration

内置对常见成功页的轻量检测（Stripe/LemonSqueezy/Polar），也可通过自定义事件上报更精确的支付信息。

## 🔐 Security & Privacy

- 受保护读取 API（`/api/realtime/[websiteId]`、`/api/stream`）参数必须传数据库主键 `websites.id`，服务端会做“登录+站点归属”校验；路由内部再用 `website.trackingId` 调 Tinybird。
- 上报热路径 `/api/events` 默认仅写 Tinybird；支持 `EVENT_WRITE_MODE=dual` 同写 Neon（不建议在请求线程重写入）。
- 上报端启用 `withSecurity(SECURITY_CONFIGS.tracking)`：限流、CORS、可疑 UA、包体限制；严格输入校验与清洗。
- 前端遵循 DNT/GPC 与 `data-consent`；建议在生产 CSP 中允许 Tinybird 域名（直连模式）。

## 📚 Learn More

- 架构与调用流：`docs/architecture-flow.md`
- 环境变量示例：`docs/环境变量配置示例.md`
- 追踪脚本对比：`docs/追踪脚本对比.md`
- Next.js: https://nextjs.org/docs
- Drizzle: https://orm.drizzle.team
- Auth.js: https://authjs.dev
- shadcn/ui: https://ui.shadcn.com

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines before submitting PRs.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**InstaSight** - Turn your website visitors into revenue insights. 🚀
